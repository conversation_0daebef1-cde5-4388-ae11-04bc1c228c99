import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import "./App.css";

const BACKEND_URL = "http://localhost:8000";

export default function SelectTicketPage({ token }) {
  const navigate = useNavigate();
  const accessToken = token || localStorage.getItem("access");
  const [loading, setLoading] = useState(true);
  const [pendingTickets, setPendingTickets] = useState([]);
  const [error, setError] = useState("");

  useEffect(() => {
    if (!accessToken) {
      navigate("/auth");
      return;
    }

    fetchPendingTickets();
  }, [accessToken, navigate]);

  const fetchPendingTickets = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      const data = await response.json();

      if (response.ok) {
        setPendingTickets(data.tickets || []);
        if (data.tickets && data.tickets.length === 0) {
          setError("No pending tickets found. Redirecting to actions page...");
          setTimeout(() => navigate("/actions"), 2000);
        }
      } else {
        setError("Failed to fetch pending tickets.");
      }
    } catch (err) {
      console.error("Error fetching pending tickets:", err);
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleTicketSelect = (ticketNumber) => {
    navigate(`/chatbot/${ticketNumber}`);
  };

  const handleCancel = () => {
    navigate("/actions");
  };

  if (loading) {
    return (
      <div style={{ 
        padding: "40px", 
        textAlign: "center",
        fontFamily: "Arial, sans-serif"
      }}>
        <h2>Loading your pending tickets...</h2>
        <div style={{ 
          marginTop: "20px",
          fontSize: "18px"
        }}>
          ⏳ Please wait...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ 
        padding: "40px", 
        textAlign: "center",
        fontFamily: "Arial, sans-serif"
      }}>
        <h2 style={{ color: "#d32f2f" }}>Error</h2>
        <p style={{ fontSize: "18px", color: "#666" }}>{error}</p>
        <button
          onClick={handleCancel}
          style={{
            marginTop: "20px",
            padding: "12px 24px",
            backgroundColor: "#2196F3",
            color: "white",
            border: "none",
            borderRadius: "4px",
            fontSize: "16px",
            cursor: "pointer"
          }}
        >
          Back to Actions
        </button>
      </div>
    );
  }

  return (
    <div style={{ 
      padding: "40px", 
      maxWidth: "800px", 
      margin: "0 auto", 
      fontFamily: "Arial, sans-serif"
    }}>
      <h1 style={{ 
        color: "#333", 
        marginBottom: "20px",
        textAlign: "center"
      }}>
        Select a Pending Ticket
      </h1>
      
      <p style={{ 
        fontSize: "1.1rem", 
        color: "#666", 
        marginBottom: "30px",
        textAlign: "center"
      }}>
        You have {pendingTickets.length} pending ticket{pendingTickets.length !== 1 ? 's' : ''}. 
        Please select one to continue:
      </p>

      <div style={{ 
        display: "flex", 
        flexDirection: "column", 
        gap: "15px",
        marginBottom: "30px"
      }}>
        {pendingTickets.map((ticket, index) => (
          <div
            key={ticket.ticket_number}
            onClick={() => handleTicketSelect(ticket.ticket_number)}
            style={{
              padding: "20px",
              border: "2px solid #e0e0e0",
              borderRadius: "8px",
              backgroundColor: "#f9f9f9",
              cursor: "pointer",
              transition: "all 0.3s ease",
              boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
            }}
            onMouseOver={(e) => {
              e.target.style.borderColor = "#2196F3";
              e.target.style.backgroundColor = "#f0f8ff";
              e.target.style.transform = "translateY(-2px)";
              e.target.style.boxShadow = "0 4px 8px rgba(0,0,0,0.15)";
            }}
            onMouseOut={(e) => {
              e.target.style.borderColor = "#e0e0e0";
              e.target.style.backgroundColor = "#f9f9f9";
              e.target.style.transform = "translateY(0)";
              e.target.style.boxShadow = "0 2px 4px rgba(0,0,0,0.1)";
            }}
          >
            <div style={{ 
              display: "flex", 
              justifyContent: "space-between", 
              alignItems: "flex-start",
              marginBottom: "10px"
            }}>
              <h3 style={{ 
                margin: 0, 
                color: "#2196F3",
                fontSize: "1.2rem"
              }}>
                #{ticket.ticket_number}
              </h3>
              <span style={{
                backgroundColor: "#4CAF50",
                color: "white",
                padding: "4px 8px",
                borderRadius: "12px",
                fontSize: "12px",
                fontWeight: "bold"
              }}>
                {ticket.status || "OPEN"}
              </span>
            </div>
            
            <p style={{ 
              margin: 0, 
              color: "#666",
              fontSize: "16px",
              lineHeight: "1.4"
            }}>
              <strong>Issue:</strong> {ticket.issue || "No description available"}
            </p>
            
            {ticket.created_at && (
              <p style={{ 
                margin: "8px 0 0 0", 
                color: "#999",
                fontSize: "14px"
              }}>
                Created: {new Date(ticket.created_at).toLocaleDateString()}
              </p>
            )}
          </div>
        ))}
      </div>

      <div style={{ 
        textAlign: "center",
        borderTop: "1px solid #e0e0e0",
        paddingTop: "20px"
      }}>
        <button
          onClick={handleCancel}
          style={{
            padding: "12px 24px",
            backgroundColor: "#6c757d",
            color: "white",
            border: "none",
            borderRadius: "4px",
            fontSize: "16px",
            cursor: "pointer",
            marginRight: "15px"
          }}
          onMouseOver={(e) => {
            e.target.style.backgroundColor = "#5a6268";
          }}
          onMouseOut={(e) => {
            e.target.style.backgroundColor = "#6c757d";
          }}
        >
          ← Back to Actions
        </button>
        
        <button
          onClick={() => navigate("/new-ticket")}
          style={{
            padding: "12px 24px",
            backgroundColor: "#4CAF50",
            color: "white",
            border: "none",
            borderRadius: "4px",
            fontSize: "16px",
            cursor: "pointer"
          }}
          onMouseOver={(e) => {
            e.target.style.backgroundColor = "#45a049";
          }}
          onMouseOut={(e) => {
            e.target.style.backgroundColor = "#4CAF50";
          }}
        >
          + Create New Ticket
        </button>
      </div>
    </div>
  );
}
