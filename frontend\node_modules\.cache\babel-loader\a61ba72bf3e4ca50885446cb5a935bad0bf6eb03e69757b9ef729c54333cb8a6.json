{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AI-Agent-Chatbot-main\\\\frontend\\\\src\\\\NewTicketForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport \"./App.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BACKEND_URL = \"http://localhost:8000\";\nexport default function NewTicketForm({\n  token\n}) {\n  _s();\n  const navigate = useNavigate();\n  const accessToken = token || localStorage.getItem(\"access\");\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n  const [formData, setFormData] = useState({\n    productType: \"Camera\",\n    productName: \"\",\n    model: \"\",\n    serialNo: \"\",\n    purchasedFrom: \"\",\n    yearOfPurchase: \"\",\n    operatingSystem: \"\",\n    poNumber: \"\"\n  });\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    if (error) setError(\"\");\n  };\n  const validateForm = () => {\n    const requiredFields = ['productName', 'model', 'serialNo', 'purchasedFrom', 'yearOfPurchase', 'operatingSystem', 'poNumber'];\n    for (let field of requiredFields) {\n      if (!formData[field] || formData[field].trim() === \"\") {\n        return `Please fill in the ${field.replace(/([A-Z])/g, ' $1').toLowerCase()} field.`;\n      }\n    }\n    return null;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const validationError = validateForm();\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n    setLoading(true);\n    setError(\"\");\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        },\n        body: JSON.stringify({\n          product_type: formData.productType,\n          product_name: formData.productName,\n          model: formData.model,\n          serial_no: formData.serialNo,\n          purchased_from: formData.purchasedFrom,\n          year_of_purchase: formData.yearOfPurchase,\n          operating_system: formData.operatingSystem,\n          po_number: formData.poNumber\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        // Redirect to Home.jsx chatbot with the new ticket\n        navigate(`/legacy-chat?ticket=${data.ticket_number}&mode=new`);\n      } else {\n        setError(data.message || \"Failed to create ticket. Please try again.\");\n      }\n    } catch (err) {\n      console.error(\"Error creating ticket:\", err);\n      setError(\"Network error. Please check your connection and try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCancel = () => {\n    navigate(\"/actions\");\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: \"40px\",\n      maxWidth: \"600px\",\n      margin: \"0 auto\",\n      fontFamily: \"Arial, sans-serif\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      style: {\n        color: \"#333\",\n        marginBottom: \"20px\",\n        textAlign: \"center\"\n      },\n      children: \"Create New Support Ticket\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        fontSize: \"1.1rem\",\n        color: \"#666\",\n        marginBottom: \"30px\",\n        textAlign: \"center\"\n      },\n      children: \"Please provide your product details below. The system will automatically generate a problem description and title for your ticket.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: \"#ffebee\",\n        color: \"#c62828\",\n        padding: \"12px\",\n        borderRadius: \"4px\",\n        marginBottom: \"20px\",\n        border: \"1px solid #ef5350\"\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      style: {\n        display: \"flex\",\n        flexDirection: \"column\",\n        gap: \"20px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Product Type *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          name: \"productType\",\n          value: formData.productType,\n          onChange: handleInputChange,\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Camera\",\n            children: \"Camera\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Frame Grabber\",\n            children: \"Frame Grabber\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Accessories\",\n            children: \"Accessories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Software\",\n            children: \"Software\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Product Name *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"productName\",\n          value: formData.productName,\n          onChange: handleInputChange,\n          placeholder: \"e.g., Genie Nano, Falcon4-CLHS\",\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Model *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"model\",\n          value: formData.model,\n          onChange: handleInputChange,\n          placeholder: \"e.g., C2590, M1920\",\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Serial Number *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"serialNo\",\n          value: formData.serialNo,\n          onChange: handleInputChange,\n          placeholder: \"e.g., SN123456789\",\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Purchased From *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"purchasedFrom\",\n          value: formData.purchasedFrom,\n          onChange: handleInputChange,\n          placeholder: \"e.g., Teledyne DALSA, Authorized Dealer\",\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Year of Purchase *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"yearOfPurchase\",\n          value: formData.yearOfPurchase,\n          onChange: handleInputChange,\n          placeholder: \"e.g., 2023\",\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Operating System *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"operatingSystem\",\n          value: formData.operatingSystem,\n          onChange: handleInputChange,\n          placeholder: \"e.g., Windows 11, Ubuntu 20.04\",\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"PO Number *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"poNumber\",\n          value: formData.poNumber,\n          onChange: handleInputChange,\n          placeholder: \"Purchase Order Number\",\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          gap: \"15px\",\n          marginTop: \"20px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handleCancel,\n          disabled: loading,\n          style: {\n            flex: 1,\n            padding: \"15px\",\n            backgroundColor: \"#6c757d\",\n            color: \"white\",\n            border: \"none\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\",\n            cursor: loading ? \"not-allowed\" : \"pointer\"\n          },\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: loading,\n          style: {\n            flex: 2,\n            padding: \"15px\",\n            backgroundColor: loading ? \"#ccc\" : \"#4CAF50\",\n            color: \"white\",\n            border: \"none\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\",\n            cursor: loading ? \"not-allowed\" : \"pointer\"\n          },\n          children: loading ? \"Creating Ticket...\" : \"Create Ticket & Start Chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n}\n_s(NewTicketForm, \"dyQeBaWdxKAtGCXOBbfShkJ9vkA=\", false, function () {\n  return [useNavigate];\n});\n_c = NewTicketForm;\nvar _c;\n$RefreshReg$(_c, \"NewTicketForm\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "jsxDEV", "_jsxDEV", "BACKEND_URL", "NewTicketForm", "token", "_s", "navigate", "accessToken", "localStorage", "getItem", "loading", "setLoading", "error", "setError", "formData", "setFormData", "productType", "productName", "model", "serialNo", "purchasedFrom", "yearOfPurchase", "operatingSystem", "poNumber", "handleInputChange", "e", "name", "value", "target", "prev", "validateForm", "requiredFields", "field", "trim", "replace", "toLowerCase", "handleSubmit", "preventDefault", "validationError", "response", "fetch", "method", "headers", "Authorization", "body", "JSON", "stringify", "product_type", "product_name", "serial_no", "purchased_from", "year_of_purchase", "operating_system", "po_number", "data", "json", "ok", "ticket_number", "message", "err", "console", "handleCancel", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "fontFamily", "children", "color", "marginBottom", "textAlign", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "backgroundColor", "borderRadius", "border", "onSubmit", "display", "flexDirection", "gap", "fontWeight", "onChange", "required", "width", "type", "placeholder", "marginTop", "onClick", "disabled", "flex", "cursor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/AI-Agent-Chatbot-main/frontend/src/NewTicketForm.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport \"./App.css\";\n\nconst BACKEND_URL = \"http://localhost:8000\";\n\nexport default function NewTicketForm({ token }) {\n  const navigate = useNavigate();\n  const accessToken = token || localStorage.getItem(\"access\");\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  const [formData, setFormData] = useState({\n    productType: \"Camera\",\n    productName: \"\",\n    model: \"\",\n    serialNo: \"\",\n    purchasedFrom: \"\",\n    yearOfPurchase: \"\",\n    operatingSystem: \"\",\n    poNumber: \"\",\n  });\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    if (error) setError(\"\");\n  };\n\n  const validateForm = () => {\n    const requiredFields = [\n      'productName', 'model', 'serialNo', 'purchasedFrom', 'yearOfPurchase', 'operatingSystem', 'poNumber'\n    ];\n\n    for (let field of requiredFields) {\n      if (!formData[field] || formData[field].trim() === \"\") {\n        return `Please fill in the ${field.replace(/([A-Z])/g, ' $1').toLowerCase()} field.`;\n      }\n    }\n\n    return null;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const validationError = validateForm();\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    setLoading(true);\n    setError(\"\");\n\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`,\n        },\n        body: JSON.stringify({\n          product_type: formData.productType,\n          product_name: formData.productName,\n          model: formData.model,\n          serial_no: formData.serialNo,\n          purchased_from: formData.purchasedFrom,\n          year_of_purchase: formData.yearOfPurchase,\n          operating_system: formData.operatingSystem,\n          po_number: formData.poNumber,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        // Redirect to Home.jsx chatbot with the new ticket\n        navigate(`/legacy-chat?ticket=${data.ticket_number}&mode=new`);\n      } else {\n        setError(data.message || \"Failed to create ticket. Please try again.\");\n      }\n    } catch (err) {\n      console.error(\"Error creating ticket:\", err);\n      setError(\"Network error. Please check your connection and try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCancel = () => {\n    navigate(\"/actions\");\n  };\n\n  return (\n    <div style={{ \n      padding: \"40px\", \n      maxWidth: \"600px\", \n      margin: \"0 auto\", \n      fontFamily: \"Arial, sans-serif\"\n    }}>\n      <h1 style={{ \n        color: \"#333\", \n        marginBottom: \"20px\",\n        textAlign: \"center\"\n      }}>\n        Create New Support Ticket\n      </h1>\n      \n      <p style={{\n        fontSize: \"1.1rem\",\n        color: \"#666\",\n        marginBottom: \"30px\",\n        textAlign: \"center\"\n      }}>\n        Please provide your product details below. The system will automatically generate a problem description and title for your ticket.\n      </p>\n\n      {error && (\n        <div style={{\n          backgroundColor: \"#ffebee\",\n          color: \"#c62828\",\n          padding: \"12px\",\n          borderRadius: \"4px\",\n          marginBottom: \"20px\",\n          border: \"1px solid #ef5350\"\n        }}>\n          {error}\n        </div>\n      )}\n\n      <form onSubmit={handleSubmit} style={{ display: \"flex\", flexDirection: \"column\", gap: \"20px\" }}>\n\n\n\n        {/* Product Type */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Product Type *\n          </label>\n          <select\n            name=\"productType\"\n            value={formData.productType}\n            onChange={handleInputChange}\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          >\n            <option value=\"Camera\">Camera</option>\n            <option value=\"Frame Grabber\">Frame Grabber</option>\n            <option value=\"Accessories\">Accessories</option>\n            <option value=\"Software\">Software</option>\n          </select>\n        </div>\n\n        {/* Product Name */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Product Name *\n          </label>\n          <input\n            type=\"text\"\n            name=\"productName\"\n            value={formData.productName}\n            onChange={handleInputChange}\n            placeholder=\"e.g., Genie Nano, Falcon4-CLHS\"\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n\n        {/* Model */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Model *\n          </label>\n          <input\n            type=\"text\"\n            name=\"model\"\n            value={formData.model}\n            onChange={handleInputChange}\n            placeholder=\"e.g., C2590, M1920\"\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n\n        {/* Serial Number */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Serial Number *\n          </label>\n          <input\n            type=\"text\"\n            name=\"serialNo\"\n            value={formData.serialNo}\n            onChange={handleInputChange}\n            placeholder=\"e.g., SN123456789\"\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n\n        {/* Purchased From */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Purchased From *\n          </label>\n          <input\n            type=\"text\"\n            name=\"purchasedFrom\"\n            value={formData.purchasedFrom}\n            onChange={handleInputChange}\n            placeholder=\"e.g., Teledyne DALSA, Authorized Dealer\"\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n\n        {/* Year of Purchase */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Year of Purchase *\n          </label>\n          <input\n            type=\"text\"\n            name=\"yearOfPurchase\"\n            value={formData.yearOfPurchase}\n            onChange={handleInputChange}\n            placeholder=\"e.g., 2023\"\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n\n        {/* Operating System */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Operating System *\n          </label>\n          <input\n            type=\"text\"\n            name=\"operatingSystem\"\n            value={formData.operatingSystem}\n            onChange={handleInputChange}\n            placeholder=\"e.g., Windows 11, Ubuntu 20.04\"\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n\n        {/* PO Number */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            PO Number *\n          </label>\n          <input\n            type=\"text\"\n            name=\"poNumber\"\n            value={formData.poNumber}\n            onChange={handleInputChange}\n            placeholder=\"Purchase Order Number\"\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n\n\n\n        {/* Buttons */}\n        <div style={{ display: \"flex\", gap: \"15px\", marginTop: \"20px\" }}>\n          <button\n            type=\"button\"\n            onClick={handleCancel}\n            disabled={loading}\n            style={{\n              flex: 1,\n              padding: \"15px\",\n              backgroundColor: \"#6c757d\",\n              color: \"white\",\n              border: \"none\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\",\n              cursor: loading ? \"not-allowed\" : \"pointer\"\n            }}\n          >\n            Cancel\n          </button>\n          \n          <button\n            type=\"submit\"\n            disabled={loading}\n            style={{\n              flex: 2,\n              padding: \"15px\",\n              backgroundColor: loading ? \"#ccc\" : \"#4CAF50\",\n              color: \"white\",\n              border: \"none\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\",\n              cursor: loading ? \"not-allowed\" : \"pointer\"\n            }}\n          >\n            {loading ? \"Creating Ticket...\" : \"Create Ticket & Start Chat\"}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,MAAMC,WAAW,GAAG,uBAAuB;AAE3C,eAAe,SAASC,aAAaA,CAAC;EAAEC;AAAM,CAAC,EAAE;EAAAC,EAAA;EAC/C,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAMQ,WAAW,GAAGH,KAAK,IAAII,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;EAC3D,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,WAAW,EAAE,QAAQ;IACrBC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE,EAAE;IAClBC,eAAe,EAAE,EAAE;IACnBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCb,WAAW,CAACc,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;IACH,IAAIf,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC;EAED,MAAMiB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,cAAc,GAAG,CACrB,aAAa,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,UAAU,CACrG;IAED,KAAK,IAAIC,KAAK,IAAID,cAAc,EAAE;MAChC,IAAI,CAACjB,QAAQ,CAACkB,KAAK,CAAC,IAAIlB,QAAQ,CAACkB,KAAK,CAAC,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACrD,OAAO,sBAAsBD,KAAK,CAACE,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACC,WAAW,CAAC,CAAC,SAAS;MACtF;IACF;IAEA,OAAO,IAAI;EACb,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,MAAMC,eAAe,GAAGR,YAAY,CAAC,CAAC;IACtC,IAAIQ,eAAe,EAAE;MACnBzB,QAAQ,CAACyB,eAAe,CAAC;MACzB;IACF;IAEA3B,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAM0B,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGtC,WAAW,qBAAqB,EAAE;QAChEuC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUpC,WAAW;QACtC,CAAC;QACDqC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,YAAY,EAAEjC,QAAQ,CAACE,WAAW;UAClCgC,YAAY,EAAElC,QAAQ,CAACG,WAAW;UAClCC,KAAK,EAAEJ,QAAQ,CAACI,KAAK;UACrB+B,SAAS,EAAEnC,QAAQ,CAACK,QAAQ;UAC5B+B,cAAc,EAAEpC,QAAQ,CAACM,aAAa;UACtC+B,gBAAgB,EAAErC,QAAQ,CAACO,cAAc;UACzC+B,gBAAgB,EAAEtC,QAAQ,CAACQ,eAAe;UAC1C+B,SAAS,EAAEvC,QAAQ,CAACS;QACtB,CAAC;MACH,CAAC,CAAC;MAEF,MAAM+B,IAAI,GAAG,MAAMf,QAAQ,CAACgB,IAAI,CAAC,CAAC;MAElC,IAAIhB,QAAQ,CAACiB,EAAE,EAAE;QACf;QACAlD,QAAQ,CAAC,uBAAuBgD,IAAI,CAACG,aAAa,WAAW,CAAC;MAChE,CAAC,MAAM;QACL5C,QAAQ,CAACyC,IAAI,CAACI,OAAO,IAAI,4CAA4C,CAAC;MACxE;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAAChD,KAAK,CAAC,wBAAwB,EAAE+C,GAAG,CAAC;MAC5C9C,QAAQ,CAAC,4DAA4D,CAAC;IACxE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkD,YAAY,GAAGA,CAAA,KAAM;IACzBvD,QAAQ,CAAC,UAAU,CAAC;EACtB,CAAC;EAED,oBACEL,OAAA;IAAK6D,KAAK,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,gBACAlE,OAAA;MAAI6D,KAAK,EAAE;QACTM,KAAK,EAAE,MAAM;QACbC,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE;MACb,CAAE;MAAAH,QAAA,EAAC;IAEH;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAELzE,OAAA;MAAG6D,KAAK,EAAE;QACRa,QAAQ,EAAE,QAAQ;QAClBP,KAAK,EAAE,MAAM;QACbC,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE;MACb,CAAE;MAAAH,QAAA,EAAC;IAEH;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,EAEH9D,KAAK,iBACJX,OAAA;MAAK6D,KAAK,EAAE;QACVc,eAAe,EAAE,SAAS;QAC1BR,KAAK,EAAE,SAAS;QAChBL,OAAO,EAAE,MAAM;QACfc,YAAY,EAAE,KAAK;QACnBR,YAAY,EAAE,MAAM;QACpBS,MAAM,EAAE;MACV,CAAE;MAAAX,QAAA,EACCvD;IAAK;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDzE,OAAA;MAAM8E,QAAQ,EAAE3C,YAAa;MAAC0B,KAAK,EAAE;QAAEkB,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAf,QAAA,gBAK7FlE,OAAA;QAAAkE,QAAA,gBACElE,OAAA;UAAO6D,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRzE,OAAA;UACEyB,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAEb,QAAQ,CAACE,WAAY;UAC5BoE,QAAQ,EAAE5D,iBAAkB;UAC5B6D,QAAQ;UACRvB,KAAK,EAAE;YACLwB,KAAK,EAAE,MAAM;YACbvB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ,CAAE;UAAAR,QAAA,gBAEFlE,OAAA;YAAQ0B,KAAK,EAAC,QAAQ;YAAAwC,QAAA,EAAC;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCzE,OAAA;YAAQ0B,KAAK,EAAC,eAAe;YAAAwC,QAAA,EAAC;UAAa;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpDzE,OAAA;YAAQ0B,KAAK,EAAC,aAAa;YAAAwC,QAAA,EAAC;UAAW;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChDzE,OAAA;YAAQ0B,KAAK,EAAC,UAAU;YAAAwC,QAAA,EAAC;UAAQ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNzE,OAAA;QAAAkE,QAAA,gBACElE,OAAA;UAAO6D,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRzE,OAAA;UACEsF,IAAI,EAAC,MAAM;UACX7D,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAEb,QAAQ,CAACG,WAAY;UAC5BmE,QAAQ,EAAE5D,iBAAkB;UAC5BgE,WAAW,EAAC,gCAAgC;UAC5CH,QAAQ;UACRvB,KAAK,EAAE;YACLwB,KAAK,EAAE,MAAM;YACbvB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNzE,OAAA;QAAAkE,QAAA,gBACElE,OAAA;UAAO6D,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRzE,OAAA;UACEsF,IAAI,EAAC,MAAM;UACX7D,IAAI,EAAC,OAAO;UACZC,KAAK,EAAEb,QAAQ,CAACI,KAAM;UACtBkE,QAAQ,EAAE5D,iBAAkB;UAC5BgE,WAAW,EAAC,oBAAoB;UAChCH,QAAQ;UACRvB,KAAK,EAAE;YACLwB,KAAK,EAAE,MAAM;YACbvB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNzE,OAAA;QAAAkE,QAAA,gBACElE,OAAA;UAAO6D,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRzE,OAAA;UACEsF,IAAI,EAAC,MAAM;UACX7D,IAAI,EAAC,UAAU;UACfC,KAAK,EAAEb,QAAQ,CAACK,QAAS;UACzBiE,QAAQ,EAAE5D,iBAAkB;UAC5BgE,WAAW,EAAC,mBAAmB;UAC/BH,QAAQ;UACRvB,KAAK,EAAE;YACLwB,KAAK,EAAE,MAAM;YACbvB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNzE,OAAA;QAAAkE,QAAA,gBACElE,OAAA;UAAO6D,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRzE,OAAA;UACEsF,IAAI,EAAC,MAAM;UACX7D,IAAI,EAAC,eAAe;UACpBC,KAAK,EAAEb,QAAQ,CAACM,aAAc;UAC9BgE,QAAQ,EAAE5D,iBAAkB;UAC5BgE,WAAW,EAAC,yCAAyC;UACrDH,QAAQ;UACRvB,KAAK,EAAE;YACLwB,KAAK,EAAE,MAAM;YACbvB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNzE,OAAA;QAAAkE,QAAA,gBACElE,OAAA;UAAO6D,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRzE,OAAA;UACEsF,IAAI,EAAC,MAAM;UACX7D,IAAI,EAAC,gBAAgB;UACrBC,KAAK,EAAEb,QAAQ,CAACO,cAAe;UAC/B+D,QAAQ,EAAE5D,iBAAkB;UAC5BgE,WAAW,EAAC,YAAY;UACxBH,QAAQ;UACRvB,KAAK,EAAE;YACLwB,KAAK,EAAE,MAAM;YACbvB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNzE,OAAA;QAAAkE,QAAA,gBACElE,OAAA;UAAO6D,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRzE,OAAA;UACEsF,IAAI,EAAC,MAAM;UACX7D,IAAI,EAAC,iBAAiB;UACtBC,KAAK,EAAEb,QAAQ,CAACQ,eAAgB;UAChC8D,QAAQ,EAAE5D,iBAAkB;UAC5BgE,WAAW,EAAC,gCAAgC;UAC5CH,QAAQ;UACRvB,KAAK,EAAE;YACLwB,KAAK,EAAE,MAAM;YACbvB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNzE,OAAA;QAAAkE,QAAA,gBACElE,OAAA;UAAO6D,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRzE,OAAA;UACEsF,IAAI,EAAC,MAAM;UACX7D,IAAI,EAAC,UAAU;UACfC,KAAK,EAAEb,QAAQ,CAACS,QAAS;UACzB6D,QAAQ,EAAE5D,iBAAkB;UAC5BgE,WAAW,EAAC,uBAAuB;UACnCH,QAAQ;UACRvB,KAAK,EAAE;YACLwB,KAAK,EAAE,MAAM;YACbvB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAKNzE,OAAA;QAAK6D,KAAK,EAAE;UAAEkB,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE,MAAM;UAAEO,SAAS,EAAE;QAAO,CAAE;QAAAtB,QAAA,gBAC9DlE,OAAA;UACEsF,IAAI,EAAC,QAAQ;UACbG,OAAO,EAAE7B,YAAa;UACtB8B,QAAQ,EAAEjF,OAAQ;UAClBoD,KAAK,EAAE;YACL8B,IAAI,EAAE,CAAC;YACP7B,OAAO,EAAE,MAAM;YACfa,eAAe,EAAE,SAAS;YAC1BR,KAAK,EAAE,OAAO;YACdU,MAAM,EAAE,MAAM;YACdD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE,MAAM;YAChBkB,MAAM,EAAEnF,OAAO,GAAG,aAAa,GAAG;UACpC,CAAE;UAAAyD,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETzE,OAAA;UACEsF,IAAI,EAAC,QAAQ;UACbI,QAAQ,EAAEjF,OAAQ;UAClBoD,KAAK,EAAE;YACL8B,IAAI,EAAE,CAAC;YACP7B,OAAO,EAAE,MAAM;YACfa,eAAe,EAAElE,OAAO,GAAG,MAAM,GAAG,SAAS;YAC7C0D,KAAK,EAAE,OAAO;YACdU,MAAM,EAAE,MAAM;YACdD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE,MAAM;YAChBkB,MAAM,EAAEnF,OAAO,GAAG,aAAa,GAAG;UACpC,CAAE;UAAAyD,QAAA,EAEDzD,OAAO,GAAG,oBAAoB,GAAG;QAA4B;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACrE,EAAA,CAjWuBF,aAAa;EAAA,QAClBJ,WAAW;AAAA;AAAA+F,EAAA,GADN3F,aAAa;AAAA,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}