{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AI-Agent-Chatbot-main\\\\frontend\\\\src\\\\Home.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { YesNoButtons, FileDownloadButtons, TicketCloseButtons } from \"./YesNoButtons\";\nimport \"./App.css\";\n\n// Define the backend URL\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BACKEND_URL = \"http://localhost:8000\";\nexport default function Home({\n  token\n}) {\n  _s();\n  const accessToken = token || localStorage.getItem(\"access\");\n\n  // --- States ---\n  const [query, setQuery] = useState(\"\");\n  const [messages, setMessages] = useState([]);\n  const [promptTemplate, setPromptTemplate] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n  const [mode, setMode] = useState(\"strict\");\n  const [pendingFiles, setPendingFiles] = useState(null);\n  const [orgVerified, setOrgVerified] = useState(false);\n  const [verifying, setVerifying] = useState(false);\n  const [username, setUserName] = useState(\"\");\n  const [askRaiseTicket, setAskRaiseTicket] = useState(false);\n  const [awaitingCloseConfirmation, setAwaitingCloseConfirmation] = useState(false);\n  const [awaitingOtherQueries, setAwaitingOtherQueries] = useState(false);\n  const [pendingTickets, setPendingTickets] = useState([]);\n  const [awaitingPendingChoice, setAwaitingPendingChoice] = useState(false);\n  const [awaitingTicketSelect, setAwaitingTicketSelect] = useState(false);\n  const [activeTicket, setActiveTicket] = useState(null);\n  const [ticketRefused, setTicketRefused] = useState(false);\n  const [queriesAfterNoTicket, setQueriesAfterNoTicket] = useState(0);\n  const [awaitingUnrelatedQueryResponse, setAwaitingUnrelatedQueryResponse] = useState(false);\n  const [sessionKeepAlive, setSessionKeepAlive] = useState(null);\n  const MAX_QUERIES_AFTER_NO_TICKET = 10;\n  const [tickets, setTickets] = useState([]);\n  const [showTickets, setShowTickets] = useState(false);\n  const [ticketQueryCount, setTicketQueryCount] = useState(0);\n\n  // --- New ticket states ---\n  const [ticketStep, setTicketStep] = useState(0);\n  const [ticketData, setTicketData] = useState({\n    productType: \"\",\n    purchasedFrom: \"\",\n    yearOfPurchase: \"\",\n    productName: \"\",\n    model: \"\",\n    serialNo: \"\",\n    operatingSystem: \"\"\n  });\n  const [awaitingProblemDescription, setAwaitingProblemDescription] = useState(false);\n  const [currentTicketNumber, setCurrentTicketNumber] = useState(null);\n  const ticketQuestions = [\"Please select the product type using the dropdown below:\", \"Please enter the 'Purchased From' information:\", \"Please enter the 'Year of Purchase':\", \"Please enter the 'Product Name':\", \"Please enter the 'Model':\", \"Please enter the 'Serial Number':\", \"Please enter the 'Operating System':\"];\n  const productTypeOptions = [\"Camera\", \"Frame Grabber\", \"Accessories\", \"Software\"];\n\n  // Auto-scroll messages\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  }, [messages, loading, error]);\n  const messagesEndRef = useRef(null);\n\n  // Fetch prompt template\n  useEffect(() => {\n    fetch(`${BACKEND_URL}/api/prompt/?type=chat`).then(res => res.json()).then(data => setPromptTemplate(data.template)).catch(err => {\n      console.error(\"Failed to fetch prompt template:\", err);\n      setPromptTemplate(null);\n    });\n  }, []);\n\n  // Check for pending mode or new ticket mode from URL\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const isPendingMode = urlParams.get('mode') === 'pending';\n    const isNewTicketMode = urlParams.get('mode') === 'new';\n    const ticketNumber = urlParams.get('ticket');\n    if (isPendingMode && ticketNumber) {\n      // Load specific pending ticket\n      loadSpecificPendingTicket(ticketNumber);\n    } else if (isPendingMode) {\n      // Load latest pending ticket (fallback)\n      loadPendingTicketDirectly();\n    } else if (isNewTicketMode && ticketNumber) {\n      // Load the newly created ticket\n      loadNewTicket(ticketNumber);\n    }\n  }, []);\n\n  // Fetch username and welcome message\n  useEffect(() => {\n    if (!accessToken) return;\n    const urlParams = new URLSearchParams(window.location.search);\n    const isPendingMode = urlParams.get('mode') === 'pending';\n    if (isPendingMode) {\n      return; // Skip normal flow for pending mode\n    }\n    fetch(`${BACKEND_URL}/api/user_info/`, {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${accessToken}`\n      }\n    }).then(async res => {\n      if (!res.ok) {\n        const errorText = await res.text();\n        console.error(\"User info fetch failed:\", res.status, errorText);\n        throw new Error(\"User info fetch failed\");\n      }\n      return res.json();\n    }).then(data => {\n      const name = data.name || data.username || data.email;\n      if (!name) throw new Error(\"Name missing in response\");\n      setUserName(name);\n      setMessages([{\n        id: 1,\n        type: \"bot\",\n        content: `👋 Welcome, ${name}! Please enter your organization name to verify your account.`,\n        timestamp: new Date()\n      }]);\n    }).catch(err => {\n      console.error(\"Failed to fetch user info:\", err.message);\n      localStorage.removeItem(\"access\");\n      window.location.href = \"/auth\";\n    });\n  }, [accessToken]);\n\n  // Load new ticket (for new ticket mode)\n  const loadNewTicket = async ticketNumber => {\n    if (!accessToken) return;\n    try {\n      // Get user info\n      const userResponse = await fetch(`${BACKEND_URL}/api/user_info/`, {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        }\n      });\n      if (!userResponse.ok) {\n        throw new Error(\"Failed to fetch user info\");\n      }\n      const userData = await userResponse.json();\n      const name = userData.name || userData.username || userData.email;\n      setUserName(name);\n      setOrgVerified(true);\n      setActiveTicket(ticketNumber);\n\n      // Fetch the ticket details to show generated content\n      try {\n        const ticketResponse = await fetch(`${BACKEND_URL}/api/ticket/${ticketNumber}/`, {\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${accessToken}`\n          }\n        });\n        if (ticketResponse.ok) {\n          const ticketData = await ticketResponse.json();\n          setMessages([{\n            id: 1,\n            type: \"bot\",\n            content: `👋 Welcome, ${name}! Your new support ticket ${ticketNumber} has been created successfully.`,\n            timestamp: new Date()\n          }, {\n            id: 2,\n            type: \"bot\",\n            content: `🎫 **Ticket Created:** ${ticketNumber}\\n\\n**Generated Title:** ${ticketData.ticket.short_title || 'No title'}\\n\\n**Generated Problem Description:** ${ticketData.ticket.problem_description || 'No description'}\\n\\nHow can I help you with this issue?`,\n            timestamp: new Date()\n          }]);\n        } else {\n          // Fallback if ticket details can't be fetched\n          setMessages([{\n            id: 1,\n            type: \"bot\",\n            content: `👋 Welcome, ${name}! Your new support ticket ${ticketNumber} has been created successfully.`,\n            timestamp: new Date()\n          }, {\n            id: 2,\n            type: \"bot\",\n            content: `🎫 **Ticket Created:** ${ticketNumber}\\n\\nHow can I help you with your issue?`,\n            timestamp: new Date()\n          }]);\n        }\n      } catch (err) {\n        console.error(\"Failed to fetch ticket details:\", err);\n        // Fallback messages\n        setMessages([{\n          id: 1,\n          type: \"bot\",\n          content: `👋 Welcome, ${name}! Your new support ticket ${ticketNumber} has been created successfully.`,\n          timestamp: new Date()\n        }, {\n          id: 2,\n          type: \"bot\",\n          content: `🎫 **Ticket Created:** ${ticketNumber}\\n\\nHow can I help you with your issue?`,\n          timestamp: new Date()\n        }]);\n      }\n    } catch (err) {\n      console.error(\"Failed to load new ticket:\", err);\n      setError(\"Failed to load ticket. Please try again.\");\n    }\n  };\n\n  // Load specific pending ticket (when selected from list)\n  const loadSpecificPendingTicket = async ticketNumber => {\n    if (!accessToken) return;\n    try {\n      // First get user info\n      const userResponse = await fetch(`${BACKEND_URL}/api/user_info/`, {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        }\n      });\n      if (!userResponse.ok) {\n        throw new Error(\"Failed to fetch user info\");\n      }\n      const userData = await userResponse.json();\n      const name = userData.name || userData.username || userData.email;\n      setUserName(name);\n      setOrgVerified(true);\n      setActiveTicket(ticketNumber);\n\n      // Fetch specific ticket details\n      const ticketResponse = await fetch(`${BACKEND_URL}/api/ticket/${ticketNumber}/`, {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        }\n      });\n      if (!ticketResponse.ok) {\n        throw new Error(\"Failed to fetch ticket details\");\n      }\n      const ticket = await ticketResponse.json();\n      const messages = [{\n        id: 1,\n        type: \"bot\",\n        content: `👋 Welcome back, ${name}! Continuing with your ticket: ${ticketNumber}`,\n        timestamp: new Date()\n      }, {\n        id: 2,\n        type: \"bot\",\n        content: `📋 **Ticket Details:**\\n**Title:** ${ticket.short_title || 'No title'}\\n**Problem:** ${ticket.problem_description || 'No description available'}`,\n        timestamp: new Date()\n      }];\n\n      // Add previous solution if available\n      if (ticket.solution_summary && ticket.solution_summary.trim() !== \"\" && ticket.solution_summary !== \"No solution yet.\") {\n        messages.push({\n          id: 3,\n          type: \"bot\",\n          content: `💡 **Previous Solution Summary:**\\n${ticket.solution_summary}\\n\\nDo you have any follow-up questions about this solution?`,\n          timestamp: new Date()\n        });\n      } else {\n        messages.push({\n          id: 3,\n          type: \"bot\",\n          content: `How can I help you with this ticket?`,\n          timestamp: new Date()\n        });\n      }\n      setMessages(messages);\n    } catch (err) {\n      console.error(\"Failed to load specific pending ticket:\", err);\n      setError(\"Failed to load ticket. Please try again.\");\n    }\n  };\n\n  // Load pending ticket directly (for pending mode)\n  const loadPendingTicketDirectly = async () => {\n    if (!accessToken) return;\n    try {\n      // First get user info\n      const userResponse = await fetch(`${BACKEND_URL}/api/user_info/`, {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        }\n      });\n      if (!userResponse.ok) {\n        throw new Error(\"Failed to fetch user info\");\n      }\n      const userData = await userResponse.json();\n      const name = userData.name || userData.username || userData.email;\n      setUserName(name);\n      setOrgVerified(true);\n\n      // Fetch pending tickets\n      const ticketsResponse = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        }\n      });\n      if (!ticketsResponse.ok) {\n        throw new Error(\"Failed to fetch pending tickets\");\n      }\n      const ticketsData = await ticketsResponse.json();\n      if (ticketsData.tickets && ticketsData.tickets.length > 0) {\n        const latestTicket = ticketsData.tickets[0]; // Get the latest ticket\n        setActiveTicket(latestTicket.ticket_number);\n\n        // Fetch detailed ticket information including solution summary\n        try {\n          const detailResponse = await fetch(`${BACKEND_URL}/api/ticket/${latestTicket.ticket_number}/`, {\n            headers: {\n              \"Content-Type\": \"application/json\",\n              Authorization: `Bearer ${accessToken}`\n            }\n          });\n          if (detailResponse.ok) {\n            const detailData = await detailResponse.json();\n            const ticket = detailData;\n            const messages = [{\n              id: 1,\n              type: \"bot\",\n              content: `👋 Welcome back, ${name}! Continuing with your ticket: ${latestTicket.ticket_number}`,\n              timestamp: new Date()\n            }, {\n              id: 2,\n              type: \"bot\",\n              content: `📋 **Ticket Details:**\\n**Title:** ${ticket.short_title || 'No title'}\\n**Problem:** ${ticket.problem_description || 'No description available'}`,\n              timestamp: new Date()\n            }];\n\n            // Add previous solution if available\n            if (ticket.solution_summary && ticket.solution_summary.trim() !== \"\" && ticket.solution_summary !== \"No solution yet.\") {\n              messages.push({\n                id: 3,\n                type: \"bot\",\n                content: `💡 **Previous Solution Summary:**\\n${ticket.solution_summary}\\n\\nDo you have any follow-up questions about this solution?`,\n                timestamp: new Date()\n              });\n            } else {\n              messages.push({\n                id: 3,\n                type: \"bot\",\n                content: `How can I help you with this ticket?`,\n                timestamp: new Date()\n              });\n            }\n            setMessages(messages);\n          } else {\n            // Fallback if detailed ticket info can't be fetched\n            setMessages([{\n              id: 1,\n              type: \"bot\",\n              content: `👋 Welcome back, ${name}! Continuing with your ticket: ${latestTicket.ticket_number}`,\n              timestamp: new Date()\n            }, {\n              id: 2,\n              type: \"bot\",\n              content: `📋 **Ticket Details:**\\n**Title:** ${latestTicket.title || latestTicket.short_title || 'No title'}\\n**Problem:** ${latestTicket.problem_description || 'No description available'}\\n\\nHow can I help you with this ticket?`,\n              timestamp: new Date()\n            }]);\n          }\n        } catch (err) {\n          console.error(\"Failed to fetch detailed ticket info:\", err);\n          // Fallback messages\n          setMessages([{\n            id: 1,\n            type: \"bot\",\n            content: `👋 Welcome back, ${name}! Continuing with your ticket: ${latestTicket.ticket_number}`,\n            timestamp: new Date()\n          }, {\n            id: 2,\n            type: \"bot\",\n            content: `📋 **Ticket Details:**\\n**Title:** ${latestTicket.title || latestTicket.short_title || 'No title'}\\n**Problem:** ${latestTicket.problem_description || 'No description available'}\\n\\nHow can I help you with this ticket?`,\n            timestamp: new Date()\n          }]);\n        }\n      } else {\n        setMessages([{\n          id: 1,\n          type: \"bot\",\n          content: `👋 Welcome, ${name}! No pending tickets found. Please go back to create a new ticket.`,\n          timestamp: new Date()\n        }]);\n      }\n    } catch (err) {\n      console.error(\"Failed to load pending ticket:\", err);\n      setError(\"Failed to load pending ticket. Please try again.\");\n    }\n  };\n\n  // File download handler\n  const handleFileDownload = file => {\n    // Open file in new tab instead of downloading\n    window.open(file.url, '_blank');\n\n    // Immediately remove the file download message from chat\n    setMessages(prev => prev.map(msg => {\n      if (msg.files && msg.files.length > 0) {\n        // Remove the file download part from the message\n        return {\n          ...msg,\n          content: msg.content.replace(/\\n\\n💡 For full explanation, do you want the related file\\?.*$/s, ''),\n          files: null\n        };\n      }\n      return msg;\n    }));\n    setPendingFiles(null);\n\n    // Add confirmation message\n    addBot(\"✅ File opened in new tab! Do you have any more questions about this ticket?\");\n    setAwaitingOtherQueries(true);\n  };\n\n  // Ticket closure handler\n  const handleTicketClose = async () => {\n    if (!activeTicket) return;\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/update_ticket_status/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        },\n        body: JSON.stringify({\n          ticket_number: activeTicket,\n          status: \"closed\"\n        })\n      });\n      if (response.ok) {\n        addBot(`✅ Ticket ${activeTicket} has been closed successfully. Thank you for using our support system!`);\n\n        // Auto logout after 3 seconds\n        setTimeout(() => {\n          handleLogout();\n        }, 3000);\n      } else {\n        addBot(\"❌ Failed to close the ticket. Please try again.\");\n      }\n    } catch (err) {\n      console.error(\"Error closing ticket:\", err);\n      addBot(\"❌ Error occurred while closing the ticket. Please try again.\");\n    }\n  };\n\n  // Handle yes/no responses\n  const handleYesNoResponse = (response, context) => {\n    const userMsg = {\n      id: Date.now(),\n      type: \"user\",\n      content: response ? \"Yes\" : \"No\",\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, userMsg]);\n    if (context === \"file_download\") {\n      if (response && pendingFiles && pendingFiles.length > 0) {\n        handleFileDownload(pendingFiles[0]);\n      } else {\n        setPendingFiles(null);\n        addBot(\"Do you have any more questions about this ticket?\");\n        setAwaitingOtherQueries(true);\n      }\n    } else if (context === \"more_queries\") {\n      setAwaitingOtherQueries(false);\n      if (response) {\n        addBot(\"Please ask your question:\");\n      } else {\n        addBot(\"Do you want to close this ticket?\");\n        setAwaitingCloseConfirmation(true);\n      }\n    } else if (context === \"close_ticket\") {\n      setAwaitingCloseConfirmation(false);\n      if (response) {\n        handleTicketClose();\n      } else {\n        addBot(\"Ticket remains open. How else can I help you?\");\n      }\n    } else if (context === \"unrelated_query\") {\n      setAwaitingUnrelatedQueryResponse(false);\n      if (response) {\n        // Redirect to new ticket page\n        window.location.href = \"/new-ticket\";\n      } else {\n        addBot(\"Continuing with the current ticket. How else can I help you?\");\n      }\n    }\n  };\n\n  // Session keep-alive when buttons are shown\n  useEffect(() => {\n    const isAwaitingConfirmation = awaitingOtherQueries || awaitingCloseConfirmation || awaitingUnrelatedQueryResponse || pendingFiles && pendingFiles.length > 0;\n    if (isAwaitingConfirmation) {\n      // Start keep-alive interval\n      const interval = setInterval(() => {\n        if (accessToken) {\n          fetch(`${BACKEND_URL}/api/user_info/`, {\n            headers: {\n              \"Content-Type\": \"application/json\",\n              Authorization: `Bearer ${accessToken}`\n            }\n          }).catch(() => {\n            // Silently handle errors to prevent disrupting user experience\n          });\n        }\n      }, 30000); // Keep session alive every 30 seconds\n\n      setSessionKeepAlive(interval);\n      return () => {\n        if (interval) {\n          clearInterval(interval);\n        }\n      };\n    } else {\n      // Clear keep-alive when no buttons are shown\n      if (sessionKeepAlive) {\n        clearInterval(sessionKeepAlive);\n        setSessionKeepAlive(null);\n      }\n    }\n  }, [awaitingOtherQueries, awaitingCloseConfirmation, awaitingUnrelatedQueryResponse, pendingFiles, accessToken, sessionKeepAlive]);\n\n  // Cleanup session keep-alive on unmount\n  useEffect(() => {\n    return () => {\n      if (sessionKeepAlive) {\n        clearInterval(sessionKeepAlive);\n      }\n    };\n  }, [sessionKeepAlive]);\n\n  // Check if input should be disabled\n  const isInputDisabled = () => {\n    return awaitingOtherQueries || awaitingCloseConfirmation || awaitingUnrelatedQueryResponse || pendingFiles && pendingFiles.length > 0;\n  };\n\n  // Input change handler\n  const onInputChange = e => {\n    if (error) setError(\"\");\n    setQuery(e.target.value);\n  };\n\n  // Dropdown change handler for productType\n  const handleProductTypeChange = e => {\n    const selectedType = e.target.value;\n    setTicketData({\n      ...ticketData,\n      productType: selectedType\n    });\n    setMessages(prev => [...prev, {\n      id: Date.now(),\n      type: \"user\",\n      content: `Selected product type: ${selectedType}`,\n      timestamp: new Date()\n    }]);\n    setTicketStep(ticketStep + 1);\n    addBot(ticketQuestions[ticketStep]);\n  };\n\n  // Verify organization name\n  const verifyOrganization = async orgName => {\n    setVerifying(true);\n    setError(\"\");\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/verify_organization/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        },\n        body: JSON.stringify({\n          organization: orgName\n        })\n      });\n      const data = await response.json();\n      if (response.ok && data.status === \"verified\") {\n        setOrgVerified(true);\n        setUserName(data.name || username);\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: data.message || \"✅ Organization verified.\",\n          timestamp: new Date()\n        }]);\n        await fetchPendingTickets();\n      } else {\n        const baseUrl = window.location.origin;\n        const signupLink = `[sign up here](${baseUrl}/signup/)`;\n        setError(data.message || \"❌ Organization mismatch.\");\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: `❌ Organization verification failed.\\n\\nIf you belong to a new organization, please ${signupLink} to register first.`,\n          timestamp: new Date()\n        }]);\n        localStorage.removeItem(\"access\");\n      }\n    } catch (err) {\n      setError(\"Network error during verification.\");\n      setMessages(prev => [...prev, {\n        id: Date.now(),\n        type: \"bot\",\n        content: \"❌ Network error during organization verification. Please try again.\",\n        timestamp: new Date()\n      }]);\n      console.error(\"Verification error:\", err);\n    } finally {\n      setVerifying(false);\n    }\n  };\n\n  // Helper function to fetch pending tickets after verification\n  async function fetchPendingTickets() {\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {\n        headers: {\n          Authorization: `Bearer ${accessToken}`\n        }\n      });\n      const data = await response.json();\n      if (response.ok) {\n        setPendingTickets(data.tickets || []);\n        if (data.tickets && data.tickets.length > 0) {\n          setAwaitingPendingChoice(true);\n          addBot(`You have ${data.tickets.length} pending ticket(s). Do you want to continue with any of them? (yes/no)`);\n        } else {\n          setAskRaiseTicket(true);\n          addBot(\"No pending tickets found. Would you like to raise a support ticket? (yes/no)\");\n        }\n      } else {\n        setPendingTickets([]);\n        setAskRaiseTicket(true);\n        addBot(\"Could not fetch pending tickets. Would you like to raise a support ticket? ( Juno/no)\");\n      }\n    } catch (err) {\n      console.error(\"Error fetching pending tickets:\", err);\n      setPendingTickets([]);\n      setAskRaiseTicket(true);\n      addBot(\"Error fetching pending tickets. Would you like to raise a support ticket? (yes/no)\");\n    }\n  }\n\n  // Submit ticket with latest data\n  const submitTicket = async finalTicketData => {\n    setLoading(true);\n    setError(\"\");\n    const validProductTypes = [\"Camera\", \"Frame Grabber\", \"Accessories\", \"Software\"];\n    const allFilled = Object.values(finalTicketData).every(v => v && v.trim() !== \"\");\n    if (!allFilled) {\n      setMessages(prev => [...prev, {\n        id: Date.now(),\n        type: \"bot\",\n        content: \"❌ Please fill in all required fields before submitting the ticket.\",\n        timestamp: new Date()\n      }]);\n      setLoading(false);\n      return;\n    }\n    if (!validProductTypes.includes(finalTicketData.productType)) {\n      setMessages(prev => [...prev, {\n        id: Date.now(),\n        type: \"bot\",\n        content: \"❌ Invalid product type. Please select: Camera, Frame Grabber, Accessories, or Software.\",\n        timestamp: new Date()\n      }]);\n      setLoading(false);\n      return;\n    }\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        },\n        body: JSON.stringify({\n          product_type: finalTicketData.productType,\n          purchased_from: finalTicketData.purchasedFrom,\n          year_of_purchase: finalTicketData.yearOfPurchase,\n          product_name: finalTicketData.productName,\n          model: finalTicketData.model,\n          serial_no: finalTicketData.serialNo,\n          operating_system: finalTicketData.operatingSystem\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        setCurrentTicketNumber(data.ticket_number);\n        setActiveTicket(data.ticket_number);\n        setAwaitingProblemDescription(true);\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: `🎉 Thank you! Your support ticket has been created successfully. Your ticket number is **${data.ticket_number}**.\\n\\nPlease describe your problem so we can assist you.`,\n          timestamp: new Date()\n        }]);\n      } else {\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: `❌ Failed to create ticket: ${JSON.stringify(data.errors || data.message)}`,\n          timestamp: new Date()\n        }]);\n      }\n    } catch (err) {\n      setMessages(prev => [...prev, {\n        id: Date.now(),\n        type: \"bot\",\n        content: `❌ Network error while creating ticket: ${err.message}`,\n        timestamp: new Date()\n      }]);\n    } finally {\n      setLoading(false);\n      setTicketStep(0);\n      setAskRaiseTicket(false);\n      setTicketData({\n        productType: \"\",\n        purchasedFrom: \"\",\n        yearOfPurchase: \"\",\n        productName: \"\",\n        model: \"\",\n        serialNo: \"\",\n        operatingSystem: \"\"\n      });\n    }\n  };\n\n  // Helper to add a bot message\n  function addBot(text) {\n    setMessages(prev => [...prev, {\n      id: Date.now(),\n      type: \"bot\",\n      content: text,\n      timestamp: new Date()\n    }]);\n  }\n\n  // Handle ticket selection from UI\n  const handleTicketSelect = async ticketNumber => {\n    setAwaitingTicketSelect(false);\n    setActiveTicket(ticketNumber);\n    setCurrentTicketNumber(ticketNumber);\n    setShowTickets(false);\n    try {\n      const summaryRes = await fetch(`${BACKEND_URL}/api/ticket_summary/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        },\n        body: JSON.stringify({\n          ticket_number: ticketNumber\n        })\n      });\n      const summaryData = await summaryRes.json();\n      if (summaryRes.ok) {\n        addBot(`🔄 Resuming ticket **${ticketNumber}** …\\n\\n` + `📝 **Raised problem:** ${summaryData.problem_summary || summaryData.problem_description}\\n\\n` + `💡 **Given solution:** ${summaryData.solution_summary || \"No solution yet.\"}\\n\\n` + \"✅ You can ask your follow-up query now.\");\n      } else {\n        addBot(\"⚠️ Error fetching ticket summary.\");\n      }\n    } catch (err) {\n      addBot(\"❌ Network error while fetching ticket summary.\");\n    }\n  };\n\n  // FULLY UPDATED handleSubmit\n  async function handleSubmit(e) {\n    e.preventDefault();\n    if (!query.trim() || loading || verifying) return;\n    const currentQuery = query.trim().toLowerCase();\n\n    // --- New: Check 5 queries per ticket limit ---\n    if (activeTicket &&\n    // There is an active ticket\n    ticketStep === 0 &&\n    // Not in ticket creation steps\n    !awaitingProblemDescription &&\n    // Not waiting for problem description input\n    ticketQueryCount >= 5 // Limit reached\n    ) {\n      addBot(\"🛑 You have reached the maximum of five queries for this ticket. It has been automatically escalated to ensure prompt resolution. Kindly create a new ticket for any further inquiries or await our team’s response.\");\n      setQuery(\"\");\n      return;\n    }\n    // ---------------------------------------------\n\n    if (ticketRefused) {\n      if (queriesAfterNoTicket >= MAX_QUERIES_AFTER_NO_TICKET) {\n        addBot(\"⚠️ You have reached the maximum number of free queries. Please raise a support ticket for further assistance.\");\n        setQuery(\"\");\n        return;\n      } else {\n        setQueriesAfterNoTicket(n => n + 1);\n      }\n    }\n    const userMsg = {\n      id: Date.now(),\n      type: \"user\",\n      content: query.trim(),\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, userMsg]);\n    setQuery(\"\");\n    setError(\"\");\n    if (awaitingPendingChoice) {\n      setAwaitingPendingChoice(false);\n      if (currentQuery === \"yes\") {\n        setAwaitingTicketSelect(true);\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: `Select a ticket by typing its number:`,\n          timestamp: new Date(),\n          tickets: pendingTickets.map((t, i) => ({\n            index: i + 1,\n            ticketNumber: t.ticket_number,\n            title: t.title || t.short_title || \"No title\"\n          }))\n        }]);\n      } else if (currentQuery === \"no\") {\n        setAskRaiseTicket(true);\n        addBot(\"Do you want to raise a support ticket? (yes/no)\");\n      } else {\n        addBot(\"Please answer 'yes' or 'no'. Do you want to continue an open ticket?\");\n        setAwaitingPendingChoice(true);\n      }\n      return;\n    }\n    if (awaitingTicketSelect) {\n      const picked = pendingTickets.find((t, idx) => currentQuery === String(idx + 1) || currentQuery.includes(t.ticket_number.toLowerCase()));\n      if (!picked) {\n        addBot(\"Ticket not recognised, please type its number.\");\n        return;\n      }\n      await handleTicketSelect(picked.ticket_number);\n      return;\n    }\n    if (!orgVerified) {\n      await verifyOrganization(currentQuery);\n      return;\n    }\n    if (askRaiseTicket) {\n      if (currentQuery === \"yes\") {\n        setAskRaiseTicket(false);\n        setTicketStep(1);\n        addBot(ticketQuestions[0]);\n        setTicketRefused(false);\n        setQueriesAfterNoTicket(0);\n\n        // --- New: Reset query count when new ticket starts ---\n        setTicketQueryCount(0);\n        // ------------------------------------------------------\n      } else if (currentQuery === \"no\") {\n        setAskRaiseTicket(false);\n        addBot(\"👍 Okay, no ticket will be raised. How else can I help you?\");\n        setTicketRefused(true);\n        setQueriesAfterNoTicket(0);\n      } else {\n        addBot(\"Please answer 'yes' or 'no'. Do you want to raise a support ticket?\");\n      }\n      return;\n    }\n    if (awaitingUnrelatedQueryResponse) {\n      setAwaitingUnrelatedQueryResponse(false);\n      if (currentQuery === \"yes\") {\n        setTicketStep(1);\n        setActiveTicket(null);\n        setCurrentTicketNumber(null);\n        addBot(ticketQuestions[0]);\n\n        // --- New: Reset query count when new ticket starts here too ---\n        setTicketQueryCount(0);\n        // ---------------------------------------------------------------\n      } else if (currentQuery === \"no\") {\n        setAwaitingCloseConfirmation(true);\n        addBot(\"Can I close this ticket now? (yes/no)\");\n      } else {\n        setAwaitingUnrelatedQueryResponse(true);\n        addBot(\"Please answer 'yes' or 'no'. Do you want to create a new ticket?\");\n      }\n      return;\n    }\n    if (ticketStep > 0 && ticketStep <= ticketQuestions.length) {\n      if (ticketStep === 1) {\n        const selectedType = query.trim();\n        if (productTypeOptions.includes(selectedType)) {\n          setTicketData({\n            ...ticketData,\n            productType: selectedType\n          });\n          setMessages(prev => [...prev, {\n            id: Date.now(),\n            type: \"user\",\n            content: `Selected product type: ${selectedType}`,\n            timestamp: new Date()\n          }]);\n          setTicketStep(ticketStep + 1);\n          addBot(ticketQuestions[ticketStep]);\n        } else {\n          addBot(\"Please select a valid product type from: Camera, Frame Grabber, Accessories, or Software.\");\n        }\n        return;\n      }\n      const keys = [\"purchasedFrom\", \"yearOfPurchase\", \"productName\", \"model\", \"serialNo\", \"operatingSystem\"];\n      const currentField = keys[ticketStep - 2];\n      const updatedTicketData = {\n        ...ticketData,\n        [currentField]: query.trim()\n      };\n      setTicketData(updatedTicketData);\n      if (ticketStep < ticketQuestions.length) {\n        setTicketStep(ticketStep + 1);\n        addBot(ticketQuestions[ticketStep]);\n      } else {\n        await submitTicket(updatedTicketData);\n      }\n      return;\n    }\n    if (awaitingProblemDescription && currentTicketNumber) {\n      setAwaitingProblemDescription(false);\n      setLoading(true);\n      try {\n        var _saveData$files;\n        const saveRes = await fetch(`${BACKEND_URL}/api/add_problem_description/`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${accessToken}`\n          },\n          body: JSON.stringify({\n            ticket_number: currentTicketNumber,\n            problem_description: userMsg.content\n          })\n        });\n        const saveData = await saveRes.json();\n        if (!saveRes.ok) {\n          throw new Error(saveData.error || \"Failed to save problem description.\");\n        }\n        let botContent = saveData.answer || \"No solution available at the moment.\";\n        const rawFiles = (_saveData$files = saveData.files) !== null && _saveData$files !== void 0 ? _saveData$files : []; // Updated from saveData.related_files\n        const validFiles = rawFiles.filter(f => {\n          if (typeof f === \"string\") {\n            return f.trim() !== \"\" && !f.toLowerCase().startsWith(\"none\");\n          }\n          if (typeof f === \"object\" && f !== null && f.filename) {\n            return f.filename.trim() !== \"\" && !f.filename.toLowerCase().startsWith(\"none\");\n          }\n          return false;\n        });\n        if (validFiles.length > 0) {\n          setPendingFiles(validFiles.map(f => {\n            const filename = typeof f === \"string\" ? f : f.filename;\n            const url = typeof f === \"string\" || !f.url || !f.url.startsWith(\"http\") ? `${BACKEND_URL}/api/files/${encodeURIComponent(filename)}?token=${accessToken}` : `${f.url}${f.url.includes(\"?\") ? \"&\" : \"?\"}token=${accessToken}`;\n            return {\n              source_file: filename,\n              url\n            };\n          }));\n          botContent += \"\\n\\n💡 For full explanation, do you want the related file? (yes/no)\";\n        } else {\n          setPendingFiles(null);\n        }\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: botContent,\n          timestamp: new Date()\n        }]);\n        setAwaitingOtherQueries(true);\n      } catch (err) {\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: `❌ Error processing problem description: ${err.message}`,\n          timestamp: new Date()\n        }]);\n      } finally {\n        setLoading(false);\n      }\n      return;\n    }\n    if (pendingFiles && (currentQuery === \"yes\" || currentQuery === \"no\")) {\n      const baseMessages = [];\n      if (currentQuery === \"yes\") {\n        const downloadLinks = pendingFiles.map((f, idx) => `${idx + 1}. [${f.source_file}](${f.url})`).join(\"\\n\");\n        baseMessages.push({\n          id: Date.now() + 1,\n          type: \"bot\",\n          content: `📎 Here are the related files:\\n\\n${downloadLinks}`,\n          timestamp: new Date()\n        });\n      } else {\n        baseMessages.push({\n          id: Date.now() + 1,\n          type: \"bot\",\n          content: \"👍 Okay, no files will be sent.\",\n          timestamp: new Date()\n        });\n      }\n      if (activeTicket !== null) {\n        baseMessages.push({\n          id: Date.now() + 2,\n          type: \"bot\",\n          content: \"Do you have any other queries? (yes/no)\",\n          timestamp: new Date()\n        });\n        setAwaitingOtherQueries(true);\n      }\n      setMessages(prev => [...prev, ...baseMessages]);\n      setPendingFiles(null);\n      return;\n    }\n    if (awaitingOtherQueries) {\n      if (currentQuery === \"no\") {\n        setAwaitingOtherQueries(false);\n        setAwaitingCloseConfirmation(true);\n        addBot(\"Can I close this ticket now? (yes/no)\");\n      } else if (currentQuery === \"yes\") {\n        setAwaitingOtherQueries(false);\n        addBot(\"Please go ahead and ask your question.\");\n      } else {\n        addBot(\"Please answer 'yes' or 'no'. Do you have any other queries?\");\n      }\n      return;\n    }\n    if (awaitingCloseConfirmation) {\n      if (currentQuery === \"yes\") {\n        setAwaitingCloseConfirmation(false);\n        setLoading(true);\n        try {\n          const response = await fetch(`${BACKEND_URL}/api/update_ticket_status/`, {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\",\n              Authorization: `Bearer ${accessToken}`\n            },\n            body: JSON.stringify({\n              ticket_number: currentTicketNumber,\n              status: \"closed\"\n            })\n          });\n          const data = await response.json();\n          if (response.ok) {\n            setActiveTicket(null);\n            setCurrentTicketNumber(null);\n            setTicketData({\n              productType: \"\",\n              purchasedFrom: \"\",\n              yearOfPurchase: \"\",\n              productName: \"\",\n              model: \"\",\n              serialNo: \"\",\n              operatingSystem: \"\"\n            });\n            addBot(`✅ Ticket ${currentTicketNumber} has been closed. Thank you!`);\n\n            // --- New: Reset query count when ticket is closed ---\n            setTicketQueryCount(0);\n            // -----------------------------------------------------\n          } else {\n            throw new Error(data.error || \"Failed to close ticket.\");\n          }\n        } catch (err) {\n          addBot(`❌ Error closing ticket: ${err.message}`);\n        } finally {\n          setLoading(false);\n        }\n      } else if (currentQuery === \"no\") {\n        setAwaitingCloseConfirmation(false);\n        addBot(\"Okay, ticket will remain open.\");\n      } else {\n        addBot(\"Please answer 'yes' or 'no'. Can I close this ticket now?\");\n      }\n      return;\n    }\n    setLoading(true);\n    try {\n      const historyText = messages.filter(m => m.type === \"user\" || m.type === \"bot\").map(m => `${m.type === \"user\" ? \"User\" : \"Bot\"}: ${m.content}`).join(\"\\n\");\n      const finalPrompt = promptTemplate ? promptTemplate.replace(\"{context_text}\", \"some context text here\").replace(\"{history_text}\", historyText).replace(\"{query}\", query.trim()) : query.trim();\n      const response = await fetch(`${BACKEND_URL}/api/chat/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        },\n        body: JSON.stringify({\n          query: finalPrompt,\n          ticket_mode: !!activeTicket,\n          ticket_id: activeTicket,\n          stage: awaitingUnrelatedQueryResponse ? \"unrelated_query\" : \"\"\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        var _data$files;\n        let botContent = data.answer || \"…\";\n        if (data.stage === \"unrelated_query\") {\n          setAwaitingUnrelatedQueryResponse(true);\n        } else if (data.stage === \"create_new_ticket\") {\n          setTicketStep(1);\n          setActiveTicket(null);\n          setCurrentTicketNumber(null);\n          addBot(ticketQuestions[0]);\n\n          // --- New: Reset query count on new ticket here too ---\n          setTicketQueryCount(0);\n          // ------------------------------------------------------\n          return;\n        }\n        const rawFiles = (_data$files = data.files) !== null && _data$files !== void 0 ? _data$files : [];\n        const validFiles = rawFiles.filter(f => {\n          if (typeof f === \"string\") {\n            return f.trim() !== \"\" && !f.toLowerCase().startsWith(\"none\");\n          }\n          if (typeof f === \"object\" && f !== null && f.filename) {\n            return f.filename.trim() !== \"\" && !f.filename.toLowerCase().startsWith(\"none\");\n          }\n          return false;\n        });\n        if (validFiles.length > 0) {\n          setPendingFiles(validFiles.map(f => {\n            const filename = typeof f === \"string\" ? f : f.filename;\n            const url = typeof f === \"string\" || !f.url || !f.url.startsWith(\"http\") ? `${BACKEND_URL}/api/files/${encodeURIComponent(filename)}?token=${accessToken}` : `${f.url}${f.url.includes(\"?\") ? \"&\" : \"?\"}token=${accessToken}`;\n            return {\n              source_file: filename,\n              url: url\n            };\n          }));\n          if (!botContent.toLowerCase().includes(\"do you want the related file\")) {\n            botContent += \"\\n\\n💡 For full explanation, do you want the related file? (yes/no)\";\n          }\n        } else {\n          setPendingFiles(null);\n        }\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: botContent,\n          timestamp: new Date()\n        }]);\n\n        // --- New: Increment query count for ticket queries ---\n        if (activeTicket && ticketStep === 0) {\n          setTicketQueryCount(prev => prev + 1);\n        }\n        // ------------------------------------------------------\n\n        if (data.stage === \"await_close\") {\n          setAwaitingCloseConfirmation(true);\n        }\n      } else {\n        setError(data.error || \"Error processing request\");\n      }\n    } catch (err) {\n      setError(\"Network error: \" + err.message);\n    } finally {\n      setLoading(false);\n    }\n  }\n\n  // Logout handler\n  function handleLogout() {\n    localStorage.removeItem(\"access\");\n    localStorage.removeItem(\"refresh\");\n    localStorage.removeItem(\"userData\");\n    window.location.href = \"/auth\";\n  }\n\n  // Format message time HH:MM\n  const formatTime = timestamp => {\n    return timestamp.toLocaleTimeString([], {\n      hour: \"2-digit\",\n      minute: \"2-digit\"\n    });\n  };\n\n  // Render chat messages with links parsed\n  const renderMessages = () => messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `message ${message.type}`,\n    style: {\n      textAlign: message.type === \"user\" ? \"right\" : \"left\"\n    },\n    \"aria-live\": \"polite\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"message-content\",\n      style: {\n        display: \"inline-block\",\n        maxWidth: \"75%\",\n        padding: \"8px 12px\",\n        borderRadius: \"12px\",\n        backgroundColor: message.type === \"user\" ? \"#DCF8C6\" : \"#F1F0F0\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-text\",\n        style: {\n          whiteSpace: \"pre-wrap\",\n          overflowY: \"auto\",\n          maxHeight: \"400px\",\n          fontSize: \"1em\",\n          lineHeight: \"1.5\"\n        },\n        children: [message.content.split(\"\\n\").map((line, idx) => {\n          const parts = [];\n          let remaining = line;\n          let keyIndex = 0;\n          while (remaining.length > 0) {\n            const linkMatch = remaining.match(/\\[(.*?)\\]\\((http.*?)\\)/);\n            const boldMatch = remaining.match(/\\*\\*(.*?)\\*\\*/);\n            if (linkMatch && (!boldMatch || linkMatch.index < boldMatch.index)) {\n              let href = linkMatch[2];\n              if (!href.startsWith(\"http\")) {\n                href = `${BACKEND_URL}${href.startsWith(\"/\") ? href : \"/\" + href}`;\n              }\n              if ((href.startsWith(`${BACKEND_URL}/api/files/`) || href.startsWith(\"/api/files/\")) && accessToken && !href.includes(\"token=\")) {\n                href += href.includes(\"?\") ? `&token=${accessToken}` : `?token=${accessToken}`;\n              }\n              parts.push(/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [remaining.slice(0, linkMatch.index), /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: href,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  style: {\n                    color: \"#0645AD\",\n                    textDecoration: \"underline\"\n                  },\n                  children: linkMatch[1]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1424,\n                  columnNumber: 23\n                }, this)]\n              }, keyIndex++, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1422,\n                columnNumber: 21\n              }, this));\n              remaining = remaining.slice(linkMatch.index + linkMatch[0].length);\n            } else if (boldMatch) {\n              parts.push(/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [remaining.slice(0, boldMatch.index), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: boldMatch[1]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1439,\n                  columnNumber: 23\n                }, this)]\n              }, keyIndex++, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1437,\n                columnNumber: 21\n              }, this));\n              remaining = remaining.slice(boldMatch.index + boldMatch[0].length);\n            } else {\n              parts.push(/*#__PURE__*/_jsxDEV(\"span\", {\n                children: remaining\n              }, keyIndex++, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1444,\n                columnNumber: 30\n              }, this));\n              break;\n            }\n          }\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            children: parts\n          }, idx, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1449,\n            columnNumber: 22\n          }, this);\n        }), message.tickets && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: \"8px\"\n          },\n          children: message.tickets.map((ticket, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              cursor: \"pointer\",\n              padding: \"6px 4px\",\n              borderBottom: idx !== message.tickets.length - 1 ? \"1px solid #eee\" : \"none\"\n            },\n            onClick: () => handleTicketSelect(ticket.ticketNumber),\n            children: [ticket.index, \". \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: ticket.ticketNumber\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1464,\n              columnNumber: 37\n            }, this), \" \\u2014 \", ticket.title]\n          }, ticket.ticketNumber, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1454,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1452,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1390,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-time\",\n        style: {\n          fontSize: \"0.7em\",\n          color: \"#666\",\n          marginTop: \"6px\"\n        },\n        children: formatTime(message.timestamp)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1470,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1380,\n      columnNumber: 9\n    }, this)\n  }, message.id, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1374,\n    columnNumber: 7\n  }, this));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chat-container\",\n    role: \"main\",\n    \"aria-label\": \"AI Agent Chatbot\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-header\",\n      style: {\n        position: \"relative\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"ONLINE SOLUTIONS TECHNICAL SUPPORT\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1483,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"subtitle\",\n        children: \"Technical Documentation Assistant\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1484,\n        columnNumber: 9\n      }, this), username && /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          fontSize: \"0.9em\",\n          color: \"#444\"\n        },\n        children: [\"Logged in as: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: username\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1487,\n          columnNumber: 27\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1486,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: \"absolute\",\n          top: 15,\n          right: 15\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleLogout,\n          style: {\n            padding: \"6px 12px\",\n            cursor: \"pointer\",\n            backgroundColor: \"#d9534f\",\n            border: \"none\",\n            borderRadius: 4,\n            color: \"white\",\n            fontWeight: \"bold\"\n          },\n          \"aria-label\": \"Logout\",\n          title: \"Logout\",\n          children: \"Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1491,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1490,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1482,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-messages\",\n      \"aria-live\": \"polite\",\n      \"aria-relevant\": \"additions\",\n      style: {\n        maxHeight: \"60vh\",\n        overflowY: \"auto\",\n        padding: \"10px\",\n        scrollBehavior: \"smooth\"\n      },\n      children: [renderMessages(), (loading || verifying) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message bot typing\",\n        \"aria-label\": \"Analyzing\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"message-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"typing-indicator\",\n            \"aria-hidden\": \"true\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1527,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1528,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1529,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1526,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"message-text\",\n            children: verifying ? \"Verifying organization...\" : \"Analyzing...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1531,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1525,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1524,\n        columnNumber: 11\n      }, this), pendingFiles && pendingFiles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: \"10px\",\n          textAlign: \"center\"\n        },\n        children: /*#__PURE__*/_jsxDEV(YesNoButtons, {\n          onYes: () => handleYesNoResponse(true, \"file_download\"),\n          onNo: () => handleYesNoResponse(false, \"file_download\"),\n          yesText: \"Download File\",\n          noText: \"Skip\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1541,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1540,\n        columnNumber: 11\n      }, this), awaitingOtherQueries && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: \"10px\",\n          textAlign: \"center\"\n        },\n        children: /*#__PURE__*/_jsxDEV(YesNoButtons, {\n          onYes: () => handleYesNoResponse(true, \"more_queries\"),\n          onNo: () => handleYesNoResponse(false, \"more_queries\"),\n          yesText: \"Yes\",\n          noText: \"No\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1553,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1552,\n        columnNumber: 11\n      }, this), awaitingCloseConfirmation && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: \"10px\",\n          textAlign: \"center\"\n        },\n        children: /*#__PURE__*/_jsxDEV(YesNoButtons, {\n          onYes: () => handleYesNoResponse(true, \"close_ticket\"),\n          onNo: () => handleYesNoResponse(false, \"close_ticket\"),\n          yesText: \"Close Ticket\",\n          noText: \"Keep Open\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1565,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1564,\n        columnNumber: 11\n      }, this), awaitingUnrelatedQueryResponse && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: \"10px\",\n          textAlign: \"center\"\n        },\n        children: /*#__PURE__*/_jsxDEV(YesNoButtons, {\n          onYes: () => handleYesNoResponse(true, \"unrelated_query\"),\n          onNo: () => handleYesNoResponse(false, \"unrelated_query\"),\n          yesText: \"Create New Ticket\",\n          noText: \"Continue Current\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1577,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1576,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1586,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1511,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      onClick: () => setError(\"\"),\n      style: {\n        cursor: \"pointer\"\n      },\n      role: \"alert\",\n      \"aria-live\": \"assertive\",\n      tabIndex: 0,\n      children: [error, \" (click to dismiss)\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1590,\n      columnNumber: 9\n    }, this), isInputDisabled() && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: \"#fff3cd\",\n        color: \"#856404\",\n        padding: \"10px\",\n        borderRadius: \"6px\",\n        margin: \"10px 0\",\n        textAlign: \"center\",\n        border: \"1px solid #ffeaa7\",\n        fontSize: \"14px\",\n        fontWeight: \"bold\"\n      },\n      children: \"\\uD83D\\uDCAC Please use the buttons above to respond. Text input is temporarily disabled.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1604,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      className: \"chat-input-form\",\n      onSubmit: handleSubmit,\n      \"aria-label\": \"Send message form\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-container\",\n        style: {\n          display: \"flex\",\n          alignItems: \"center\",\n          width: \"100%\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: query,\n          onChange: onInputChange,\n          placeholder: orgVerified ? ticketStep > 0 ? ticketQuestions[ticketStep - 1] : isInputDisabled() ? \"Please use the buttons above to respond...\" : \"Type your question here...\" : \"Enter your organization name...\",\n          disabled: loading || verifying || ticketStep === 1 && !query || isInputDisabled(),\n          autoFocus: true,\n          \"aria-label\": \"Chat input\",\n          style: {\n            flex: \"1\",\n            width: \"100%\",\n            padding: \"8px\",\n            borderRadius: \"4px 0 0 4px\",\n            border: \"1px solid #ccc\",\n            margin: 0,\n            backgroundColor: isInputDisabled() ? \"#f5f5f5\" : \"white\",\n            color: isInputDisabled() ? \"#999\" : \"black\",\n            cursor: isInputDisabled() ? \"not-allowed\" : \"text\"\n          },\n          onKeyDown: e => {\n            if (e.key === \"Enter\" && !e.shiftKey) {\n              e.preventDefault();\n              handleSubmit(e);\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1621,\n          columnNumber: 11\n        }, this), ticketStep === 1 && /*#__PURE__*/_jsxDEV(\"select\", {\n          value: ticketData.productType,\n          onChange: handleProductTypeChange,\n          style: {\n            marginLeft: \"0\",\n            padding: \"6px\",\n            borderRadius: \"0\",\n            border: \"1px solid #ccc\",\n            borderLeft: \"none\",\n            fontSize: \"1em\"\n          },\n          \"aria-label\": \"Select product type\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            disabled: true,\n            children: \"Select a product type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1669,\n            columnNumber: 15\n          }, this), productTypeOptions.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: option,\n            children: option\n          }, option, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1673,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1656,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: loading || !query.trim() || verifying || ticketStep === 1 && !ticketData.productType,\n          title: loading || verifying ? \"Please wait...\" : \"Send\",\n          \"aria-label\": \"Send message\",\n          style: {\n            padding: \"8px 12px\",\n            borderRadius: \"0 4px 4px 0\",\n            border: \"1px solid #ccc\",\n            borderLeft: \"none\",\n            backgroundColor: \"#4CAF50\",\n            color: \"white\"\n          },\n          children: loading || verifying ? /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1686,\n            columnNumber: 37\n          }, this) : \"📤\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1679,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1620,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1619,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1481,\n    columnNumber: 5\n  }, this);\n}\n_s(Home, \"77PMYpIMsOP4j+ArEMIyCJNWYSI=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "YesNoButtons", "FileDownloadButtons", "TicketCloseButtons", "jsxDEV", "_jsxDEV", "BACKEND_URL", "Home", "token", "_s", "accessToken", "localStorage", "getItem", "query", "<PERSON><PERSON><PERSON><PERSON>", "messages", "setMessages", "promptTemplate", "setPromptTemplate", "loading", "setLoading", "error", "setError", "mode", "setMode", "pendingFiles", "setPendingFiles", "orgVerified", "setOrgVerified", "verifying", "setVerifying", "username", "setUserName", "askRaiseTicket", "setAskRaiseTicket", "awaitingCloseConfirmation", "setAwaitingCloseConfirmation", "awaiting<PERSON><PERSON><PERSON><PERSON><PERSON>", "setAwaiting<PERSON><PERSON><PERSON><PERSON><PERSON>", "pendingTickets", "setPendingTickets", "awaitingPendingChoice", "setAwaitingPendingChoice", "awaitingTicketSelect", "setAwaitingTicketSelect", "activeTicket", "setActiveTicket", "ticketRefused", "setTicketRefused", "queriesAfterNoTicket", "setQueriesAfterNoTicket", "awaitingUnrelatedQueryResponse", "setAwaitingUnrelatedQueryResponse", "sessionKeepAlive", "setSessionKeepAlive", "MAX_QUERIES_AFTER_NO_TICKET", "tickets", "setTickets", "showTickets", "setShowTickets", "ticketQueryCount", "setTicketQueryCount", "ticketStep", "setTicketStep", "ticketData", "setTicketData", "productType", "purchasedFrom", "yearOfPurchase", "productName", "model", "serialNo", "operatingSystem", "awaitingProblemDescription", "setAwaitingProblemDescription", "currentTicketNumber", "setCurrentTicketNumber", "ticketQuestions", "productTypeOptions", "_messagesEndRef$curre", "messagesEndRef", "current", "scrollIntoView", "behavior", "fetch", "then", "res", "json", "data", "template", "catch", "err", "console", "urlParams", "URLSearchParams", "window", "location", "search", "isPendingMode", "get", "isNewTicketMode", "ticketNumber", "loadSpecificPendingTicket", "loadPendingTicketDirectly", "loadNewTicket", "headers", "Authorization", "ok", "errorText", "text", "status", "Error", "name", "email", "id", "type", "content", "timestamp", "Date", "message", "removeItem", "href", "userResponse", "userData", "ticketResponse", "ticket", "short_title", "problem_description", "solution_summary", "trim", "push", "ticketsResponse", "ticketsData", "length", "latestTicket", "ticket_number", "detailResponse", "detailData", "title", "handleFileDownload", "file", "open", "url", "prev", "map", "msg", "files", "replace", "addBot", "handleTicketClose", "response", "method", "body", "JSON", "stringify", "setTimeout", "handleLogout", "handleYesNoResponse", "context", "userMsg", "now", "isAwaitingConfirmation", "interval", "setInterval", "clearInterval", "isInputDisabled", "onInputChange", "e", "target", "value", "handleProductTypeChange", "selectedType", "verifyOrganization", "orgName", "organization", "fetchPendingTickets", "baseUrl", "origin", "signupLink", "submitTicket", "finalTicketData", "validProductTypes", "allFilled", "Object", "values", "every", "v", "includes", "product_type", "purchased_from", "year_of_purchase", "product_name", "serial_no", "operating_system", "errors", "handleTicketSelect", "summaryRes", "summaryData", "problem_summary", "handleSubmit", "preventDefault", "<PERSON><PERSON><PERSON><PERSON>", "toLowerCase", "n", "t", "i", "index", "picked", "find", "idx", "String", "keys", "current<PERSON><PERSON>", "updatedTicketData", "_saveData$files", "saveRes", "saveData", "botContent", "answer", "rawFiles", "validFiles", "filter", "f", "startsWith", "filename", "encodeURIComponent", "source_file", "baseMessages", "downloadLinks", "join", "historyText", "m", "finalPrompt", "ticket_mode", "ticket_id", "stage", "_data$files", "formatTime", "toLocaleTimeString", "hour", "minute", "renderMessages", "className", "style", "textAlign", "children", "display", "max<PERSON><PERSON><PERSON>", "padding", "borderRadius", "backgroundColor", "whiteSpace", "overflowY", "maxHeight", "fontSize", "lineHeight", "split", "line", "parts", "remaining", "keyIndex", "linkMatch", "match", "boldMatch", "slice", "rel", "color", "textDecoration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "cursor", "borderBottom", "onClick", "role", "position", "top", "right", "border", "fontWeight", "scroll<PERSON>eh<PERSON>or", "onYes", "onNo", "yesText", "noText", "ref", "tabIndex", "margin", "onSubmit", "alignItems", "width", "onChange", "placeholder", "disabled", "autoFocus", "flex", "onKeyDown", "key", "shift<PERSON>ey", "marginLeft", "borderLeft", "option", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/AI-Agent-Chatbot-main/frontend/src/Home.jsx"], "sourcesContent": ["\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { YesNoButtons, FileDownloadButtons, TicketCloseButtons } from \"./YesNoButtons\";\r\nimport \"./App.css\";\r\n\r\n// Define the backend URL\r\nconst BACKEND_URL = \"http://localhost:8000\";\r\n\r\nexport default function Home({ token }) {\r\n  const accessToken = token || localStorage.getItem(\"access\");\r\n\r\n  // --- States ---\r\n  const [query, setQuery] = useState(\"\");\r\n  const [messages, setMessages] = useState([]);\r\n  const [promptTemplate, setPromptTemplate] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(\"\");\r\n  const [mode, setMode] = useState(\"strict\");\r\n  const [pendingFiles, setPendingFiles] = useState(null);\r\n  const [orgVerified, setOrgVerified] = useState(false);\r\n  const [verifying, setVerifying] = useState(false);\r\n  const [username, setUserName] = useState(\"\");\r\n  const [askRaiseTicket, setAskRaiseTicket] = useState(false);\r\n  const [awaitingCloseConfirmation, setAwaitingCloseConfirmation] = useState(false);\r\n  const [awaitingOtherQueries, setAwaitingOtherQueries] = useState(false);\r\n  const [pendingTickets, setPendingTickets] = useState([]);\r\n  const [awaitingPendingChoice, setAwaitingPendingChoice] = useState(false);\r\n  const [awaitingTicketSelect, setAwaitingTicketSelect] = useState(false);\r\n  const [activeTicket, setActiveTicket] = useState(null);\r\n  const [ticketRefused, setTicketRefused] = useState(false);\r\n  const [queriesAfterNoTicket, setQueriesAfterNoTicket] = useState(0);\r\n  const [awaitingUnrelatedQueryResponse, setAwaitingUnrelatedQueryResponse] = useState(false);\r\n  const [sessionKeepAlive, setSessionKeepAlive] = useState(null);\r\n  const MAX_QUERIES_AFTER_NO_TICKET = 10;\r\n  const [tickets, setTickets] = useState([]);\r\n  const [showTickets, setShowTickets] = useState(false);\r\n  const [ticketQueryCount, setTicketQueryCount] = useState(0);\r\n\r\n\r\n  // --- New ticket states ---\r\n  const [ticketStep, setTicketStep] = useState(0);\r\n  const [ticketData, setTicketData] = useState({\r\n    productType: \"\",\r\n    purchasedFrom: \"\",\r\n    yearOfPurchase: \"\",\r\n    productName: \"\",\r\n    model: \"\",\r\n    serialNo: \"\",\r\n    operatingSystem: \"\",\r\n  });\r\n  const [awaitingProblemDescription, setAwaitingProblemDescription] = useState(false);\r\n  const [currentTicketNumber, setCurrentTicketNumber] = useState(null);\r\n\r\n  const ticketQuestions = [\r\n    \"Please select the product type using the dropdown below:\",\r\n    \"Please enter the 'Purchased From' information:\",\r\n    \"Please enter the 'Year of Purchase':\",\r\n    \"Please enter the 'Product Name':\",\r\n    \"Please enter the 'Model':\",\r\n    \"Please enter the 'Serial Number':\",\r\n    \"Please enter the 'Operating System':\",\r\n  ];\r\n\r\n  const productTypeOptions = [\"Camera\", \"Frame Grabber\", \"Accessories\", \"Software\"];\r\n\r\n  // Auto-scroll messages\r\n  useEffect(() => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  }, [messages, loading, error]);\r\n\r\n  const messagesEndRef = useRef(null);\r\n\r\n  // Fetch prompt template\r\n  useEffect(() => {\r\n    fetch(`${BACKEND_URL}/api/prompt/?type=chat`)\r\n      .then((res) => res.json())\r\n      .then((data) => setPromptTemplate(data.template))\r\n      .catch((err) => {\r\n        console.error(\"Failed to fetch prompt template:\", err);\r\n        setPromptTemplate(null);\r\n      });\r\n  }, []);\r\n\r\n  // Check for pending mode or new ticket mode from URL\r\n  useEffect(() => {\r\n    const urlParams = new URLSearchParams(window.location.search);\r\n    const isPendingMode = urlParams.get('mode') === 'pending';\r\n    const isNewTicketMode = urlParams.get('mode') === 'new';\r\n    const ticketNumber = urlParams.get('ticket');\r\n\r\n    if (isPendingMode && ticketNumber) {\r\n      // Load specific pending ticket\r\n      loadSpecificPendingTicket(ticketNumber);\r\n    } else if (isPendingMode) {\r\n      // Load latest pending ticket (fallback)\r\n      loadPendingTicketDirectly();\r\n    } else if (isNewTicketMode && ticketNumber) {\r\n      // Load the newly created ticket\r\n      loadNewTicket(ticketNumber);\r\n    }\r\n  }, []);\r\n\r\n  // Fetch username and welcome message\r\n  useEffect(() => {\r\n    if (!accessToken) return;\r\n\r\n    const urlParams = new URLSearchParams(window.location.search);\r\n    const isPendingMode = urlParams.get('mode') === 'pending';\r\n\r\n    if (isPendingMode) {\r\n      return; // Skip normal flow for pending mode\r\n    }\r\n\r\n    fetch(`${BACKEND_URL}/api/user_info/`, {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        Authorization: `Bearer ${accessToken}`,\r\n      },\r\n    })\r\n      .then(async (res) => {\r\n        if (!res.ok) {\r\n          const errorText = await res.text();\r\n          console.error(\"User info fetch failed:\", res.status, errorText);\r\n          throw new Error(\"User info fetch failed\");\r\n        }\r\n        return res.json();\r\n      })\r\n      .then((data) => {\r\n        const name = data.name || data.username || data.email;\r\n        if (!name) throw new Error(\"Name missing in response\");\r\n\r\n        setUserName(name);\r\n        setMessages([\r\n          {\r\n            id: 1,\r\n            type: \"bot\",\r\n            content: `👋 Welcome, ${name}! Please enter your organization name to verify your account.`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n      })\r\n      .catch((err) => {\r\n        console.error(\"Failed to fetch user info:\", err.message);\r\n        localStorage.removeItem(\"access\");\r\n        window.location.href = \"/auth\";\r\n      });\r\n  }, [accessToken]);\r\n\r\n  // Load new ticket (for new ticket mode)\r\n  const loadNewTicket = async (ticketNumber) => {\r\n    if (!accessToken) return;\r\n\r\n    try {\r\n      // Get user info\r\n      const userResponse = await fetch(`${BACKEND_URL}/api/user_info/`, {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n      });\r\n\r\n      if (!userResponse.ok) {\r\n        throw new Error(\"Failed to fetch user info\");\r\n      }\r\n\r\n      const userData = await userResponse.json();\r\n      const name = userData.name || userData.username || userData.email;\r\n      setUserName(name);\r\n      setOrgVerified(true);\r\n      setActiveTicket(ticketNumber);\r\n\r\n      // Fetch the ticket details to show generated content\r\n      try {\r\n        const ticketResponse = await fetch(`${BACKEND_URL}/api/ticket/${ticketNumber}/`, {\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: `Bearer ${accessToken}`,\r\n          },\r\n        });\r\n\r\n        if (ticketResponse.ok) {\r\n          const ticketData = await ticketResponse.json();\r\n\r\n          setMessages([\r\n            {\r\n              id: 1,\r\n              type: \"bot\",\r\n              content: `👋 Welcome, ${name}! Your new support ticket ${ticketNumber} has been created successfully.`,\r\n              timestamp: new Date(),\r\n            },\r\n            {\r\n              id: 2,\r\n              type: \"bot\",\r\n              content: `🎫 **Ticket Created:** ${ticketNumber}\\n\\n**Generated Title:** ${ticketData.ticket.short_title || 'No title'}\\n\\n**Generated Problem Description:** ${ticketData.ticket.problem_description || 'No description'}\\n\\nHow can I help you with this issue?`,\r\n              timestamp: new Date(),\r\n            },\r\n          ]);\r\n        } else {\r\n          // Fallback if ticket details can't be fetched\r\n          setMessages([\r\n            {\r\n              id: 1,\r\n              type: \"bot\",\r\n              content: `👋 Welcome, ${name}! Your new support ticket ${ticketNumber} has been created successfully.`,\r\n              timestamp: new Date(),\r\n            },\r\n            {\r\n              id: 2,\r\n              type: \"bot\",\r\n              content: `🎫 **Ticket Created:** ${ticketNumber}\\n\\nHow can I help you with your issue?`,\r\n              timestamp: new Date(),\r\n            },\r\n          ]);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Failed to fetch ticket details:\", err);\r\n        // Fallback messages\r\n        setMessages([\r\n          {\r\n            id: 1,\r\n            type: \"bot\",\r\n            content: `👋 Welcome, ${name}! Your new support ticket ${ticketNumber} has been created successfully.`,\r\n            timestamp: new Date(),\r\n          },\r\n          {\r\n            id: 2,\r\n            type: \"bot\",\r\n            content: `🎫 **Ticket Created:** ${ticketNumber}\\n\\nHow can I help you with your issue?`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Failed to load new ticket:\", err);\r\n      setError(\"Failed to load ticket. Please try again.\");\r\n    }\r\n  };\r\n\r\n  // Load specific pending ticket (when selected from list)\r\n  const loadSpecificPendingTicket = async (ticketNumber) => {\r\n    if (!accessToken) return;\r\n\r\n    try {\r\n      // First get user info\r\n      const userResponse = await fetch(`${BACKEND_URL}/api/user_info/`, {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n      });\r\n\r\n      if (!userResponse.ok) {\r\n        throw new Error(\"Failed to fetch user info\");\r\n      }\r\n\r\n      const userData = await userResponse.json();\r\n      const name = userData.name || userData.username || userData.email;\r\n      setUserName(name);\r\n      setOrgVerified(true);\r\n      setActiveTicket(ticketNumber);\r\n\r\n      // Fetch specific ticket details\r\n      const ticketResponse = await fetch(`${BACKEND_URL}/api/ticket/${ticketNumber}/`, {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n      });\r\n\r\n      if (!ticketResponse.ok) {\r\n        throw new Error(\"Failed to fetch ticket details\");\r\n      }\r\n\r\n      const ticket = await ticketResponse.json();\r\n\r\n      const messages = [\r\n        {\r\n          id: 1,\r\n          type: \"bot\",\r\n          content: `👋 Welcome back, ${name}! Continuing with your ticket: ${ticketNumber}`,\r\n          timestamp: new Date(),\r\n        },\r\n        {\r\n          id: 2,\r\n          type: \"bot\",\r\n          content: `📋 **Ticket Details:**\\n**Title:** ${ticket.short_title || 'No title'}\\n**Problem:** ${ticket.problem_description || 'No description available'}`,\r\n          timestamp: new Date(),\r\n        },\r\n      ];\r\n\r\n      // Add previous solution if available\r\n      if (ticket.solution_summary && ticket.solution_summary.trim() !== \"\" && ticket.solution_summary !== \"No solution yet.\") {\r\n        messages.push({\r\n          id: 3,\r\n          type: \"bot\",\r\n          content: `💡 **Previous Solution Summary:**\\n${ticket.solution_summary}\\n\\nDo you have any follow-up questions about this solution?`,\r\n          timestamp: new Date(),\r\n        });\r\n      } else {\r\n        messages.push({\r\n          id: 3,\r\n          type: \"bot\",\r\n          content: `How can I help you with this ticket?`,\r\n          timestamp: new Date(),\r\n        });\r\n      }\r\n\r\n      setMessages(messages);\r\n    } catch (err) {\r\n      console.error(\"Failed to load specific pending ticket:\", err);\r\n      setError(\"Failed to load ticket. Please try again.\");\r\n    }\r\n  };\r\n\r\n  // Load pending ticket directly (for pending mode)\r\n  const loadPendingTicketDirectly = async () => {\r\n    if (!accessToken) return;\r\n\r\n    try {\r\n      // First get user info\r\n      const userResponse = await fetch(`${BACKEND_URL}/api/user_info/`, {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n      });\r\n\r\n      if (!userResponse.ok) {\r\n        throw new Error(\"Failed to fetch user info\");\r\n      }\r\n\r\n      const userData = await userResponse.json();\r\n      const name = userData.name || userData.username || userData.email;\r\n      setUserName(name);\r\n      setOrgVerified(true);\r\n\r\n      // Fetch pending tickets\r\n      const ticketsResponse = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n      });\r\n\r\n      if (!ticketsResponse.ok) {\r\n        throw new Error(\"Failed to fetch pending tickets\");\r\n      }\r\n\r\n      const ticketsData = await ticketsResponse.json();\r\n\r\n      if (ticketsData.tickets && ticketsData.tickets.length > 0) {\r\n        const latestTicket = ticketsData.tickets[0]; // Get the latest ticket\r\n        setActiveTicket(latestTicket.ticket_number);\r\n\r\n        // Fetch detailed ticket information including solution summary\r\n        try {\r\n          const detailResponse = await fetch(`${BACKEND_URL}/api/ticket/${latestTicket.ticket_number}/`, {\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: `Bearer ${accessToken}`,\r\n            },\r\n          });\r\n\r\n          if (detailResponse.ok) {\r\n            const detailData = await detailResponse.json();\r\n            const ticket = detailData;\r\n\r\n            const messages = [\r\n              {\r\n                id: 1,\r\n                type: \"bot\",\r\n                content: `👋 Welcome back, ${name}! Continuing with your ticket: ${latestTicket.ticket_number}`,\r\n                timestamp: new Date(),\r\n              },\r\n              {\r\n                id: 2,\r\n                type: \"bot\",\r\n                content: `📋 **Ticket Details:**\\n**Title:** ${ticket.short_title || 'No title'}\\n**Problem:** ${ticket.problem_description || 'No description available'}`,\r\n                timestamp: new Date(),\r\n              },\r\n            ];\r\n\r\n            // Add previous solution if available\r\n            if (ticket.solution_summary && ticket.solution_summary.trim() !== \"\" && ticket.solution_summary !== \"No solution yet.\") {\r\n              messages.push({\r\n                id: 3,\r\n                type: \"bot\",\r\n                content: `💡 **Previous Solution Summary:**\\n${ticket.solution_summary}\\n\\nDo you have any follow-up questions about this solution?`,\r\n                timestamp: new Date(),\r\n              });\r\n            } else {\r\n              messages.push({\r\n                id: 3,\r\n                type: \"bot\",\r\n                content: `How can I help you with this ticket?`,\r\n                timestamp: new Date(),\r\n              });\r\n            }\r\n\r\n            setMessages(messages);\r\n          } else {\r\n            // Fallback if detailed ticket info can't be fetched\r\n            setMessages([\r\n              {\r\n                id: 1,\r\n                type: \"bot\",\r\n                content: `👋 Welcome back, ${name}! Continuing with your ticket: ${latestTicket.ticket_number}`,\r\n                timestamp: new Date(),\r\n              },\r\n              {\r\n                id: 2,\r\n                type: \"bot\",\r\n                content: `📋 **Ticket Details:**\\n**Title:** ${latestTicket.title || latestTicket.short_title || 'No title'}\\n**Problem:** ${latestTicket.problem_description || 'No description available'}\\n\\nHow can I help you with this ticket?`,\r\n                timestamp: new Date(),\r\n              },\r\n            ]);\r\n          }\r\n        } catch (err) {\r\n          console.error(\"Failed to fetch detailed ticket info:\", err);\r\n          // Fallback messages\r\n          setMessages([\r\n            {\r\n              id: 1,\r\n              type: \"bot\",\r\n              content: `👋 Welcome back, ${name}! Continuing with your ticket: ${latestTicket.ticket_number}`,\r\n              timestamp: new Date(),\r\n            },\r\n            {\r\n              id: 2,\r\n              type: \"bot\",\r\n              content: `📋 **Ticket Details:**\\n**Title:** ${latestTicket.title || latestTicket.short_title || 'No title'}\\n**Problem:** ${latestTicket.problem_description || 'No description available'}\\n\\nHow can I help you with this ticket?`,\r\n              timestamp: new Date(),\r\n            },\r\n          ]);\r\n        }\r\n      } else {\r\n        setMessages([\r\n          {\r\n            id: 1,\r\n            type: \"bot\",\r\n            content: `👋 Welcome, ${name}! No pending tickets found. Please go back to create a new ticket.`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Failed to load pending ticket:\", err);\r\n      setError(\"Failed to load pending ticket. Please try again.\");\r\n    }\r\n  };\r\n\r\n  // File download handler\r\n  const handleFileDownload = (file) => {\r\n    // Open file in new tab instead of downloading\r\n    window.open(file.url, '_blank');\r\n\r\n    // Immediately remove the file download message from chat\r\n    setMessages(prev => prev.map(msg => {\r\n      if (msg.files && msg.files.length > 0) {\r\n        // Remove the file download part from the message\r\n        return {\r\n          ...msg,\r\n          content: msg.content.replace(/\\n\\n💡 For full explanation, do you want the related file\\?.*$/s, ''),\r\n          files: null\r\n        };\r\n      }\r\n      return msg;\r\n    }));\r\n\r\n    setPendingFiles(null);\r\n\r\n    // Add confirmation message\r\n    addBot(\"✅ File opened in new tab! Do you have any more questions about this ticket?\");\r\n    setAwaitingOtherQueries(true);\r\n  };\r\n\r\n  // Ticket closure handler\r\n  const handleTicketClose = async () => {\r\n    if (!activeTicket) return;\r\n\r\n    try {\r\n      const response = await fetch(`${BACKEND_URL}/api/update_ticket_status/`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n        body: JSON.stringify({\r\n          ticket_number: activeTicket,\r\n          status: \"closed\"\r\n        }),\r\n      });\r\n\r\n      if (response.ok) {\r\n        addBot(`✅ Ticket ${activeTicket} has been closed successfully. Thank you for using our support system!`);\r\n\r\n        // Auto logout after 3 seconds\r\n        setTimeout(() => {\r\n          handleLogout();\r\n        }, 3000);\r\n      } else {\r\n        addBot(\"❌ Failed to close the ticket. Please try again.\");\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error closing ticket:\", err);\r\n      addBot(\"❌ Error occurred while closing the ticket. Please try again.\");\r\n    }\r\n  };\r\n\r\n  // Handle yes/no responses\r\n  const handleYesNoResponse = (response, context) => {\r\n    const userMsg = {\r\n      id: Date.now(),\r\n      type: \"user\",\r\n      content: response ? \"Yes\" : \"No\",\r\n      timestamp: new Date(),\r\n    };\r\n    setMessages((prev) => [...prev, userMsg]);\r\n\r\n    if (context === \"file_download\") {\r\n      if (response && pendingFiles && pendingFiles.length > 0) {\r\n        handleFileDownload(pendingFiles[0]);\r\n      } else {\r\n        setPendingFiles(null);\r\n        addBot(\"Do you have any more questions about this ticket?\");\r\n        setAwaitingOtherQueries(true);\r\n      }\r\n    } else if (context === \"more_queries\") {\r\n      setAwaitingOtherQueries(false);\r\n      if (response) {\r\n        addBot(\"Please ask your question:\");\r\n      } else {\r\n        addBot(\"Do you want to close this ticket?\");\r\n        setAwaitingCloseConfirmation(true);\r\n      }\r\n    } else if (context === \"close_ticket\") {\r\n      setAwaitingCloseConfirmation(false);\r\n      if (response) {\r\n        handleTicketClose();\r\n      } else {\r\n        addBot(\"Ticket remains open. How else can I help you?\");\r\n      }\r\n    } else if (context === \"unrelated_query\") {\r\n      setAwaitingUnrelatedQueryResponse(false);\r\n      if (response) {\r\n        // Redirect to new ticket page\r\n        window.location.href = \"/new-ticket\";\r\n      } else {\r\n        addBot(\"Continuing with the current ticket. How else can I help you?\");\r\n      }\r\n    }\r\n  };\r\n\r\n  // Session keep-alive when buttons are shown\r\n  useEffect(() => {\r\n    const isAwaitingConfirmation = awaitingOtherQueries || awaitingCloseConfirmation || awaitingUnrelatedQueryResponse || (pendingFiles && pendingFiles.length > 0);\r\n\r\n    if (isAwaitingConfirmation) {\r\n      // Start keep-alive interval\r\n      const interval = setInterval(() => {\r\n        if (accessToken) {\r\n          fetch(`${BACKEND_URL}/api/user_info/`, {\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: `Bearer ${accessToken}`,\r\n            },\r\n          }).catch(() => {\r\n            // Silently handle errors to prevent disrupting user experience\r\n          });\r\n        }\r\n      }, 30000); // Keep session alive every 30 seconds\r\n\r\n      setSessionKeepAlive(interval);\r\n\r\n      return () => {\r\n        if (interval) {\r\n          clearInterval(interval);\r\n        }\r\n      };\r\n    } else {\r\n      // Clear keep-alive when no buttons are shown\r\n      if (sessionKeepAlive) {\r\n        clearInterval(sessionKeepAlive);\r\n        setSessionKeepAlive(null);\r\n      }\r\n    }\r\n  }, [awaitingOtherQueries, awaitingCloseConfirmation, awaitingUnrelatedQueryResponse, pendingFiles, accessToken, sessionKeepAlive]);\r\n\r\n  // Cleanup session keep-alive on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (sessionKeepAlive) {\r\n        clearInterval(sessionKeepAlive);\r\n      }\r\n    };\r\n  }, [sessionKeepAlive]);\r\n\r\n  // Check if input should be disabled\r\n  const isInputDisabled = () => {\r\n    return awaitingOtherQueries || awaitingCloseConfirmation || awaitingUnrelatedQueryResponse || (pendingFiles && pendingFiles.length > 0);\r\n  };\r\n\r\n  // Input change handler\r\n  const onInputChange = (e) => {\r\n    if (error) setError(\"\");\r\n    setQuery(e.target.value);\r\n  };\r\n\r\n  // Dropdown change handler for productType\r\n  const handleProductTypeChange = (e) => {\r\n    const selectedType = e.target.value;\r\n    setTicketData({ ...ticketData, productType: selectedType });\r\n    setMessages((prev) => [\r\n      ...prev,\r\n      {\r\n        id: Date.now(),\r\n        type: \"user\",\r\n        content: `Selected product type: ${selectedType}`,\r\n        timestamp: new Date(),\r\n      },\r\n    ]);\r\n    setTicketStep(ticketStep + 1);\r\n    addBot(ticketQuestions[ticketStep]);\r\n  };\r\n\r\n  // Verify organization name\r\n  const verifyOrganization = async (orgName) => {\r\n    setVerifying(true);\r\n    setError(\"\");\r\n\r\n    try {\r\n      const response = await fetch(`${BACKEND_URL}/api/verify_organization/`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n        body: JSON.stringify({ organization: orgName }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok && data.status === \"verified\") {\r\n        setOrgVerified(true);\r\n        setUserName(data.name || username);\r\n\r\n        setMessages((prev) => [\r\n          ...prev,\r\n          {\r\n            id: Date.now(),\r\n            type: \"bot\",\r\n            content: data.message || \"✅ Organization verified.\",\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n\r\n        await fetchPendingTickets();\r\n      } else {\r\n        const baseUrl = window.location.origin;\r\n        const signupLink = `[sign up here](${baseUrl}/signup/)`;\r\n\r\n        setError(data.message || \"❌ Organization mismatch.\");\r\n        setMessages((prev) => [\r\n          ...prev,\r\n          {\r\n            id: Date.now(),\r\n            type: \"bot\",\r\n            content: `❌ Organization verification failed.\\n\\nIf you belong to a new organization, please ${signupLink} to register first.`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n\r\n        localStorage.removeItem(\"access\");\r\n      }\r\n    } catch (err) {\r\n      setError(\"Network error during verification.\");\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: \"❌ Network error during organization verification. Please try again.\",\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n      console.error(\"Verification error:\", err);\r\n    } finally {\r\n      setVerifying(false);\r\n    }\r\n  };\r\n\r\n  // Helper function to fetch pending tickets after verification\r\n  async function fetchPendingTickets() {\r\n    try {\r\n      const response = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {\r\n        headers: {\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n      });\r\n      const data = await response.json();\r\n\r\n      if (response.ok) {\r\n        setPendingTickets(data.tickets || []);\r\n        if (data.tickets && data.tickets.length > 0) {\r\n          setAwaitingPendingChoice(true);\r\n          addBot(\r\n            `You have ${data.tickets.length} pending ticket(s). Do you want to continue with any of them? (yes/no)`\r\n          );\r\n        } else {\r\n          setAskRaiseTicket(true);\r\n          addBot(\"No pending tickets found. Would you like to raise a support ticket? (yes/no)\");\r\n        }\r\n      } else {\r\n        setPendingTickets([]);\r\n        setAskRaiseTicket(true);\r\n        addBot(\"Could not fetch pending tickets. Would you like to raise a support ticket? ( Juno/no)\");\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error fetching pending tickets:\", err);\r\n      setPendingTickets([]);\r\n      setAskRaiseTicket(true);\r\n      addBot(\"Error fetching pending tickets. Would you like to raise a support ticket? (yes/no)\");\r\n    }\r\n  }\r\n\r\n  // Submit ticket with latest data\r\n  const submitTicket = async (finalTicketData) => {\r\n    setLoading(true);\r\n    setError(\"\");\r\n\r\n    const validProductTypes = [\"Camera\", \"Frame Grabber\", \"Accessories\", \"Software\"];\r\n    const allFilled = Object.values(finalTicketData).every(\r\n      (v) => v && v.trim() !== \"\"\r\n    );\r\n    if (!allFilled) {\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: \"❌ Please fill in all required fields before submitting the ticket.\",\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n      setLoading(false);\r\n      return;\r\n    }\r\n    if (!validProductTypes.includes(finalTicketData.productType)) {\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: \"❌ Invalid product type. Please select: Camera, Frame Grabber, Accessories, or Software.\",\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n        body: JSON.stringify({\r\n          product_type: finalTicketData.productType,\r\n          purchased_from: finalTicketData.purchasedFrom,\r\n          year_of_purchase: finalTicketData.yearOfPurchase,\r\n          product_name: finalTicketData.productName,\r\n          model: finalTicketData.model,\r\n          serial_no: finalTicketData.serialNo,\r\n          operating_system: finalTicketData.operatingSystem,\r\n        }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok) {\r\n        setCurrentTicketNumber(data.ticket_number);\r\n        setActiveTicket(data.ticket_number);\r\n        setAwaitingProblemDescription(true);\r\n        setMessages((prev) => [\r\n          ...prev,\r\n          {\r\n            id: Date.now(),\r\n            type: \"bot\",\r\n            content: `🎉 Thank you! Your support ticket has been created successfully. Your ticket number is **${data.ticket_number}**.\\n\\nPlease describe your problem so we can assist you.`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n      } else {\r\n        setMessages((prev) => [\r\n          ...prev,\r\n          {\r\n            id: Date.now(),\r\n            type: \"bot\",\r\n            content: `❌ Failed to create ticket: ${JSON.stringify(data.errors || data.message)}`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n      }\r\n    } catch (err) {\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: `❌ Network error while creating ticket: ${err.message}`,\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n    } finally {\r\n      setLoading(false);\r\n      setTicketStep(0);\r\n      setAskRaiseTicket(false);\r\n      setTicketData({\r\n        productType: \"\",\r\n        purchasedFrom: \"\",\r\n        yearOfPurchase: \"\",\r\n        productName: \"\",\r\n        model: \"\",\r\n        serialNo: \"\",\r\n        operatingSystem: \"\",\r\n      });\r\n    }\r\n  };\r\n\r\n  // Helper to add a bot message\r\n  function addBot(text) {\r\n    setMessages((prev) => [\r\n      ...prev,\r\n      { id: Date.now(), type: \"bot\", content: text, timestamp: new Date() },\r\n    ]);\r\n  }\r\n\r\n  // Handle ticket selection from UI\r\n  const handleTicketSelect = async (ticketNumber) => {\r\n    setAwaitingTicketSelect(false);\r\n    setActiveTicket(ticketNumber);\r\n    setCurrentTicketNumber(ticketNumber);\r\n    setShowTickets(false);\r\n\r\n    try {\r\n      const summaryRes = await fetch(`${BACKEND_URL}/api/ticket_summary/`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n        body: JSON.stringify({ ticket_number: ticketNumber }),\r\n      });\r\n\r\n      const summaryData = await summaryRes.json();\r\n\r\n      if (summaryRes.ok) {\r\n        addBot(\r\n          `🔄 Resuming ticket **${ticketNumber}** …\\n\\n` +\r\n            `📝 **Raised problem:** ${summaryData.problem_summary || summaryData.problem_description}\\n\\n` +\r\n            `💡 **Given solution:** ${summaryData.solution_summary || \"No solution yet.\"}\\n\\n` +\r\n            \"✅ You can ask your follow-up query now.\"\r\n        );\r\n      } else {\r\n        addBot(\"⚠️ Error fetching ticket summary.\");\r\n      }\r\n    } catch (err) {\r\n      addBot(\"❌ Network error while fetching ticket summary.\");\r\n    }\r\n  };\r\n\r\n  // FULLY UPDATED handleSubmit\r\nasync function handleSubmit(e) {\r\n  e.preventDefault();\r\n  if (!query.trim() || loading || verifying) return;\r\n\r\n  const currentQuery = query.trim().toLowerCase();\r\n\r\n  // --- New: Check 5 queries per ticket limit ---\r\n  if (\r\n    activeTicket &&            // There is an active ticket\r\n    ticketStep === 0 &&        // Not in ticket creation steps\r\n    !awaitingProblemDescription && // Not waiting for problem description input\r\n    ticketQueryCount >= 5      // Limit reached\r\n  ) {\r\n    addBot(\r\n      \"🛑 You have reached the maximum of five queries for this ticket. It has been automatically escalated to ensure prompt resolution. Kindly create a new ticket for any further inquiries or await our team’s response.\"\r\n    );\r\n    setQuery(\"\");\r\n    return;\r\n  }\r\n  // ---------------------------------------------\r\n\r\n  if (ticketRefused) {\r\n    if (queriesAfterNoTicket >= MAX_QUERIES_AFTER_NO_TICKET) {\r\n      addBot(\r\n        \"⚠️ You have reached the maximum number of free queries. Please raise a support ticket for further assistance.\"\r\n      );\r\n      setQuery(\"\");\r\n      return;\r\n    } else {\r\n      setQueriesAfterNoTicket((n) => n + 1);\r\n    }\r\n  }\r\n\r\n  const userMsg = {\r\n    id: Date.now(),\r\n    type: \"user\",\r\n    content: query.trim(),\r\n    timestamp: new Date(),\r\n  };\r\n  setMessages((prev) => [...prev, userMsg]);\r\n  setQuery(\"\");\r\n  setError(\"\");\r\n\r\n  if (awaitingPendingChoice) {\r\n    setAwaitingPendingChoice(false);\r\n\r\n    if (currentQuery === \"yes\") {\r\n      setAwaitingTicketSelect(true);\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: `Select a ticket by typing its number:`,\r\n          timestamp: new Date(),\r\n          tickets: pendingTickets.map((t, i) => ({\r\n            index: i + 1,\r\n            ticketNumber: t.ticket_number,\r\n            title: t.title || t.short_title || \"No title\",\r\n          })),\r\n        },\r\n      ]);\r\n    } else if (currentQuery === \"no\") {\r\n      setAskRaiseTicket(true);\r\n      addBot(\"Do you want to raise a support ticket? (yes/no)\");\r\n    } else {\r\n      addBot(\"Please answer 'yes' or 'no'. Do you want to continue an open ticket?\");\r\n      setAwaitingPendingChoice(true);\r\n    }\r\n    return;\r\n  }\r\n\r\n  if (awaitingTicketSelect) {\r\n    const picked = pendingTickets.find(\r\n      (t, idx) =>\r\n        currentQuery === String(idx + 1) ||\r\n        currentQuery.includes(t.ticket_number.toLowerCase())\r\n    );\r\n\r\n    if (!picked) {\r\n      addBot(\"Ticket not recognised, please type its number.\");\r\n      return;\r\n    }\r\n\r\n    await handleTicketSelect(picked.ticket_number);\r\n    return;\r\n  }\r\n\r\n  if (!orgVerified) {\r\n    await verifyOrganization(currentQuery);\r\n    return;\r\n  }\r\n\r\n  if (askRaiseTicket) {\r\n    if (currentQuery === \"yes\") {\r\n      setAskRaiseTicket(false);\r\n      setTicketStep(1);\r\n      addBot(ticketQuestions[0]);\r\n      setTicketRefused(false);\r\n      setQueriesAfterNoTicket(0);\r\n\r\n      // --- New: Reset query count when new ticket starts ---\r\n      setTicketQueryCount(0);\r\n      // ------------------------------------------------------\r\n\r\n    } else if (currentQuery === \"no\") {\r\n      setAskRaiseTicket(false);\r\n      addBot(\"👍 Okay, no ticket will be raised. How else can I help you?\");\r\n      setTicketRefused(true);\r\n      setQueriesAfterNoTicket(0);\r\n    } else {\r\n      addBot(\"Please answer 'yes' or 'no'. Do you want to raise a support ticket?\");\r\n    }\r\n    return;\r\n  }\r\n\r\n  if (awaitingUnrelatedQueryResponse) {\r\n    setAwaitingUnrelatedQueryResponse(false);\r\n    if (currentQuery === \"yes\") {\r\n      setTicketStep(1);\r\n      setActiveTicket(null);\r\n      setCurrentTicketNumber(null);\r\n      addBot(ticketQuestions[0]);\r\n\r\n      // --- New: Reset query count when new ticket starts here too ---\r\n      setTicketQueryCount(0);\r\n      // ---------------------------------------------------------------\r\n\r\n    } else if (currentQuery === \"no\") {\r\n      setAwaitingCloseConfirmation(true);\r\n      addBot(\"Can I close this ticket now? (yes/no)\");\r\n    } else {\r\n      setAwaitingUnrelatedQueryResponse(true);\r\n      addBot(\"Please answer 'yes' or 'no'. Do you want to create a new ticket?\");\r\n    }\r\n    return;\r\n  }\r\n\r\n  if (ticketStep > 0 && ticketStep <= ticketQuestions.length) {\r\n    if (ticketStep === 1) {\r\n      const selectedType = query.trim();\r\n      if (productTypeOptions.includes(selectedType)) {\r\n        setTicketData({ ...ticketData, productType: selectedType });\r\n        setMessages((prev) => [\r\n          ...prev,\r\n          {\r\n            id: Date.now(),\r\n            type: \"user\",\r\n            content: `Selected product type: ${selectedType}`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n        setTicketStep(ticketStep + 1);\r\n        addBot(ticketQuestions[ticketStep]);\r\n      } else {\r\n        addBot(\r\n          \"Please select a valid product type from: Camera, Frame Grabber, Accessories, or Software.\"\r\n        );\r\n      }\r\n      return;\r\n    }\r\n    const keys = [\r\n      \"purchasedFrom\",\r\n      \"yearOfPurchase\",\r\n      \"productName\",\r\n      \"model\",\r\n      \"serialNo\",\r\n      \"operatingSystem\",\r\n    ];\r\n    const currentField = keys[ticketStep - 2];\r\n    const updatedTicketData = { ...ticketData, [currentField]: query.trim() };\r\n    setTicketData(updatedTicketData);\r\n\r\n    if (ticketStep < ticketQuestions.length) {\r\n      setTicketStep(ticketStep + 1);\r\n      addBot(ticketQuestions[ticketStep]);\r\n    } else {\r\n      await submitTicket(updatedTicketData);\r\n    }\r\n    return;\r\n  }\r\n\r\n  if (awaitingProblemDescription && currentTicketNumber) {\r\n    setAwaitingProblemDescription(false);\r\n    setLoading(true);\r\n\r\n    try {\r\n      const saveRes = await fetch(`${BACKEND_URL}/api/add_problem_description/`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n        body: JSON.stringify({\r\n          ticket_number: currentTicketNumber,\r\n          problem_description: userMsg.content,\r\n        }),\r\n      });\r\n\r\n      const saveData = await saveRes.json();\r\n      if (!saveRes.ok) {\r\n        throw new Error(saveData.error || \"Failed to save problem description.\");\r\n      }\r\n\r\n      let botContent = saveData.answer || \"No solution available at the moment.\";\r\n\r\n      const rawFiles = saveData.files ?? []; // Updated from saveData.related_files\r\n      const validFiles = rawFiles.filter((f) => {\r\n        if (typeof f === \"string\") {\r\n          return f.trim() !== \"\" && !f.toLowerCase().startsWith(\"none\");\r\n        }\r\n        if (typeof f === \"object\" && f !== null && f.filename) {\r\n          return (\r\n            f.filename.trim() !== \"\" &&\r\n            !f.filename.toLowerCase().startsWith(\"none\")\r\n          );\r\n        }\r\n        return false;\r\n      });\r\n\r\n      if (validFiles.length > 0) {\r\n        setPendingFiles(\r\n          validFiles.map((f) => {\r\n            const filename = typeof f === \"string\" ? f : f.filename;\r\n            const url =\r\n              typeof f === \"string\" || !f.url || !f.url.startsWith(\"http\")\r\n                ? `${BACKEND_URL}/api/files/${encodeURIComponent(\r\n                    filename\r\n                  )}?token=${accessToken}`\r\n                : `${f.url}${f.url.includes(\"?\") ? \"&\" : \"?\"}token=${accessToken}`;\r\n            return { source_file: filename, url };\r\n          })\r\n        );\r\n        botContent += \"\\n\\n💡 For full explanation, do you want the related file? (yes/no)\";\r\n      } else {\r\n        setPendingFiles(null);\r\n      }\r\n\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: botContent,\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n\r\n      setAwaitingOtherQueries(true);\r\n    } catch (err) {\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: `❌ Error processing problem description: ${err.message}`,\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n    return;\r\n  }\r\n\r\n  if (pendingFiles && (currentQuery === \"yes\" || currentQuery === \"no\")) {\r\n    const baseMessages = [];\r\n\r\n    if (currentQuery === \"yes\") {\r\n      const downloadLinks = pendingFiles\r\n        .map((f, idx) => `${idx + 1}. [${f.source_file}](${f.url})`)\r\n        .join(\"\\n\");\r\n\r\n      baseMessages.push({\r\n        id: Date.now() + 1,\r\n        type: \"bot\",\r\n        content: `📎 Here are the related files:\\n\\n${downloadLinks}`,\r\n        timestamp: new Date(),\r\n      });\r\n    } else {\r\n      baseMessages.push({\r\n        id: Date.now() + 1,\r\n        type: \"bot\",\r\n        content: \"👍 Okay, no files will be sent.\",\r\n        timestamp: new Date(),\r\n      });\r\n    }\r\n\r\n    if (activeTicket !== null) {\r\n      baseMessages.push({\r\n        id: Date.now() + 2,\r\n        type: \"bot\",\r\n        content: \"Do you have any other queries? (yes/no)\",\r\n        timestamp: new Date(),\r\n      });\r\n      setAwaitingOtherQueries(true);\r\n    }\r\n\r\n    setMessages((prev) => [...prev, ...baseMessages]);\r\n    setPendingFiles(null);\r\n    return;\r\n  }\r\n\r\n  if (awaitingOtherQueries) {\r\n    if (currentQuery === \"no\") {\r\n      setAwaitingOtherQueries(false);\r\n      setAwaitingCloseConfirmation(true);\r\n      addBot(\"Can I close this ticket now? (yes/no)\");\r\n    } else if (currentQuery === \"yes\") {\r\n      setAwaitingOtherQueries(false);\r\n      addBot(\"Please go ahead and ask your question.\");\r\n    } else {\r\n      addBot(\"Please answer 'yes' or 'no'. Do you have any other queries?\");\r\n    }\r\n    return;\r\n  }\r\n\r\n  if (awaitingCloseConfirmation) {\r\n    if (currentQuery === \"yes\") {\r\n      setAwaitingCloseConfirmation(false);\r\n      setLoading(true);\r\n      try {\r\n        const response = await fetch(`${BACKEND_URL}/api/update_ticket_status/`, {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: `Bearer ${accessToken}`,\r\n          },\r\n          body: JSON.stringify({\r\n            ticket_number: currentTicketNumber,\r\n            status: \"closed\",\r\n          }),\r\n        });\r\n        const data = await response.json();\r\n\r\n        if (response.ok) {\r\n          setActiveTicket(null);\r\n          setCurrentTicketNumber(null);\r\n          setTicketData({\r\n            productType: \"\",\r\n            purchasedFrom: \"\",\r\n            yearOfPurchase: \"\",\r\n            productName: \"\",\r\n            model: \"\",\r\n            serialNo: \"\",\r\n            operatingSystem: \"\",\r\n          });\r\n\r\n          addBot(`✅ Ticket ${currentTicketNumber} has been closed. Thank you!`);\r\n\r\n          // --- New: Reset query count when ticket is closed ---\r\n          setTicketQueryCount(0);\r\n          // -----------------------------------------------------\r\n\r\n        } else {\r\n          throw new Error(data.error || \"Failed to close ticket.\");\r\n        }\r\n      } catch (err) {\r\n        addBot(`❌ Error closing ticket: ${err.message}`);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    } else if (currentQuery === \"no\") {\r\n      setAwaitingCloseConfirmation(false);\r\n      addBot(\"Okay, ticket will remain open.\");\r\n    } else {\r\n      addBot(\"Please answer 'yes' or 'no'. Can I close this ticket now?\");\r\n    }\r\n    return;\r\n  }\r\n\r\n  setLoading(true);\r\n  try {\r\n    const historyText = messages\r\n      .filter((m) => m.type === \"user\" || m.type === \"bot\")\r\n      .map((m) => `${m.type === \"user\" ? \"User\" : \"Bot\"}: ${m.content}`)\r\n      .join(\"\\n\");\r\n\r\n    const finalPrompt = promptTemplate\r\n      ? promptTemplate\r\n          .replace(\"{context_text}\", \"some context text here\")\r\n          .replace(\"{history_text}\", historyText)\r\n          .replace(\"{query}\", query.trim())\r\n      : query.trim();\r\n\r\n    const response = await fetch(`${BACKEND_URL}/api/chat/`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        Authorization: `Bearer ${accessToken}`,\r\n      },\r\n      body: JSON.stringify({\r\n        query: finalPrompt,\r\n        ticket_mode: !!activeTicket,\r\n        ticket_id: activeTicket,\r\n        stage: awaitingUnrelatedQueryResponse ? \"unrelated_query\" : \"\",\r\n      }),\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (response.ok) {\r\n      let botContent = data.answer || \"…\";\r\n\r\n      if (data.stage === \"unrelated_query\") {\r\n        setAwaitingUnrelatedQueryResponse(true);\r\n      } else if (data.stage === \"create_new_ticket\") {\r\n        setTicketStep(1);\r\n        setActiveTicket(null);\r\n        setCurrentTicketNumber(null);\r\n        addBot(ticketQuestions[0]);\r\n\r\n        // --- New: Reset query count on new ticket here too ---\r\n        setTicketQueryCount(0);\r\n        // ------------------------------------------------------\r\n        return;\r\n      }\r\n\r\n      const rawFiles = data.files ?? [];\r\n      const validFiles = rawFiles.filter((f) => {\r\n        if (typeof f === \"string\") {\r\n          return f.trim() !== \"\" && !f.toLowerCase().startsWith(\"none\");\r\n        }\r\n        if (typeof f === \"object\" && f !== null && f.filename) {\r\n          return (\r\n            f.filename.trim() !== \"\" &&\r\n            !f.filename.toLowerCase().startsWith(\"none\")\r\n          );\r\n        }\r\n        return false;\r\n      });\r\n\r\n      if (validFiles.length > 0) {\r\n        setPendingFiles(\r\n          validFiles.map((f) => {\r\n            const filename = typeof f === \"string\" ? f : f.filename;\r\n            const url =\r\n              typeof f === \"string\" || !f.url || !f.url.startsWith(\"http\")\r\n                ? `${BACKEND_URL}/api/files/${encodeURIComponent(filename)}?token=${accessToken}`\r\n                : `${f.url}${f.url.includes(\"?\") ? \"&\" : \"?\"}token=${accessToken}`;\r\n            return {\r\n              source_file: filename,\r\n              url: url,\r\n            };\r\n          })\r\n        );\r\n        if (!botContent.toLowerCase().includes(\"do you want the related file\")) {\r\n          botContent += \"\\n\\n💡 For full explanation, do you want the related file? (yes/no)\";\r\n        }\r\n      } else {\r\n        setPendingFiles(null);\r\n      }\r\n\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: botContent,\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n\r\n      // --- New: Increment query count for ticket queries ---\r\n      if (activeTicket && ticketStep === 0) {\r\n        setTicketQueryCount((prev) => prev + 1);\r\n      }\r\n      // ------------------------------------------------------\r\n\r\n      if (data.stage === \"await_close\") {\r\n        setAwaitingCloseConfirmation(true);\r\n      }\r\n    } else {\r\n      setError(data.error || \"Error processing request\");\r\n    }\r\n  } catch (err) {\r\n    setError(\"Network error: \" + err.message);\r\n  } finally {\r\n    setLoading(false);\r\n  }\r\n}\r\n\r\n\r\n  // Logout handler\r\n  function handleLogout() {\r\n    localStorage.removeItem(\"access\");\r\n    localStorage.removeItem(\"refresh\");\r\n    localStorage.removeItem(\"userData\");\r\n    window.location.href = \"/auth\";\r\n  }\r\n\r\n  // Format message time HH:MM\r\n  const formatTime = (timestamp) => {\r\n    return timestamp.toLocaleTimeString([], { hour: \"2-digit\", minute: \"2-digit\" });\r\n  };\r\n\r\n  // Render chat messages with links parsed\r\n  const renderMessages = () =>\r\n    messages.map((message) => (\r\n      <div\r\n        key={message.id}\r\n        className={`message ${message.type}`}\r\n        style={{ textAlign: message.type === \"user\" ? \"right\" : \"left\" }}\r\n        aria-live=\"polite\"\r\n      >\r\n        <div\r\n          className=\"message-content\"\r\n          style={{\r\n            display: \"inline-block\",\r\n            maxWidth: \"75%\",\r\n            padding: \"8px 12px\",\r\n            borderRadius: \"12px\",\r\n            backgroundColor: message.type === \"user\" ? \"#DCF8C6\" : \"#F1F0F0\",\r\n          }}\r\n        >\r\n          <div\r\n            className=\"message-text\"\r\n            style={{\r\n              whiteSpace: \"pre-wrap\",\r\n              overflowY: \"auto\",\r\n              maxHeight: \"400px\",\r\n              fontSize: \"1em\",\r\n              lineHeight: \"1.5\",\r\n            }}\r\n          >\r\n            {message.content.split(\"\\n\").map((line, idx) => {\r\n              const parts = [];\r\n              let remaining = line;\r\n              let keyIndex = 0;\r\n\r\n              while (remaining.length > 0) {\r\n                const linkMatch = remaining.match(/\\[(.*?)\\]\\((http.*?)\\)/);\r\n                const boldMatch = remaining.match(/\\*\\*(.*?)\\*\\*/);\r\n\r\n                if (linkMatch && (!boldMatch || linkMatch.index < boldMatch.index)) {\r\n                  let href = linkMatch[2];\r\n                  if (!href.startsWith(\"http\")) {\r\n                    href = `${BACKEND_URL}${href.startsWith(\"/\") ? href : \"/\" + href}`;\r\n                  }\r\n                  if (\r\n                    (href.startsWith(`${BACKEND_URL}/api/files/`) || href.startsWith(\"/api/files/\")) &&\r\n                    accessToken &&\r\n                    !href.includes(\"token=\")\r\n                  ) {\r\n                    href += href.includes(\"?\") ? `&token=${accessToken}` : `?token=${accessToken}`;\r\n                  }\r\n                  parts.push(\r\n                    <span key={keyIndex++}>\r\n                      {remaining.slice(0, linkMatch.index)}\r\n                      <a\r\n                        href={href}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        style={{ color: \"#0645AD\", textDecoration: \"underline\" }}\r\n                      >\r\n                        {linkMatch[1]}\r\n                      </a>\r\n                    </span>\r\n                  );\r\n                  remaining = remaining.slice(linkMatch.index + linkMatch[0].length);\r\n                } else if (boldMatch) {\r\n                  parts.push(\r\n                    <span key={keyIndex++}>\r\n                      {remaining.slice(0, boldMatch.index)}\r\n                      <strong>{boldMatch[1]}</strong>\r\n                    </span>\r\n                  );\r\n                  remaining = remaining.slice(boldMatch.index + boldMatch[0].length);\r\n                } else {\r\n                  parts.push(<span key={keyIndex++}>{remaining}</span>);\r\n                  break;\r\n                }\r\n              }\r\n\r\n              return <div key={idx}>{parts}</div>;\r\n            })}\r\n            {message.tickets && (\r\n              <div style={{ marginTop: \"8px\" }}>\r\n                {message.tickets.map((ticket, idx) => (\r\n                  <div\r\n                    key={ticket.ticketNumber}\r\n                    style={{\r\n                      cursor: \"pointer\",\r\n                      padding: \"6px 4px\",\r\n                      borderBottom:\r\n                        idx !== message.tickets.length - 1 ? \"1px solid #eee\" : \"none\",\r\n                    }}\r\n                    onClick={() => handleTicketSelect(ticket.ticketNumber)}\r\n                  >\r\n                    {ticket.index}. <strong>{ticket.ticketNumber}</strong> — {ticket.title}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n          <div\r\n            className=\"message-time\"\r\n            style={{ fontSize: \"0.7em\", color: \"#666\", marginTop: \"6px\" }}\r\n          >\r\n            {formatTime(message.timestamp)}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    ));\r\n\r\n  return (\r\n    <div className=\"chat-container\" role=\"main\" aria-label=\"AI Agent Chatbot\">\r\n      <div className=\"chat-header\" style={{ position: \"relative\" }}>\r\n        <h1>ONLINE SOLUTIONS TECHNICAL SUPPORT</h1>\r\n        <p className=\"subtitle\">Technical Documentation Assistant</p>\r\n        {username && (\r\n          <p style={{ fontSize: \"0.9em\", color: \"#444\" }}>\r\n            Logged in as: <strong>{username}</strong>\r\n          </p>\r\n        )}\r\n        <div style={{ position: \"absolute\", top: 15, right: 15 }}>\r\n          <button\r\n            onClick={handleLogout}\r\n            style={{\r\n              padding: \"6px 12px\",\r\n              cursor: \"pointer\",\r\n              backgroundColor: \"#d9534f\",\r\n              border: \"none\",\r\n              borderRadius: 4,\r\n              color: \"white\",\r\n              fontWeight: \"bold\",\r\n            }}\r\n            aria-label=\"Logout\"\r\n            title=\"Logout\"\r\n          >\r\n            Logout\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* CHAT MESSAGES */}\r\n      <div\r\n        className=\"chat-messages\"\r\n        aria-live=\"polite\"\r\n        aria-relevant=\"additions\"\r\n        style={{\r\n          maxHeight: \"60vh\",\r\n          overflowY: \"auto\",\r\n          padding: \"10px\",\r\n          scrollBehavior: \"smooth\"\r\n        }}\r\n      >\r\n        {renderMessages()}\r\n        {(loading || verifying) && (\r\n          <div className=\"message bot typing\" aria-label=\"Analyzing\">\r\n            <div className=\"message-content\">\r\n              <div className=\"typing-indicator\" aria-hidden=\"true\">\r\n                <span></span>\r\n                <span></span>\r\n                <span></span>\r\n              </div>\r\n              <div className=\"message-text\">\r\n                {verifying ? \"Verifying organization...\" : \"Analyzing...\"}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Yes/No Buttons for File Download */}\r\n        {pendingFiles && pendingFiles.length > 0 && (\r\n          <div style={{ padding: \"10px\", textAlign: \"center\" }}>\r\n            <YesNoButtons\r\n              onYes={() => handleYesNoResponse(true, \"file_download\")}\r\n              onNo={() => handleYesNoResponse(false, \"file_download\")}\r\n              yesText=\"Download File\"\r\n              noText=\"Skip\"\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        {/* Yes/No Buttons for More Queries */}\r\n        {awaitingOtherQueries && (\r\n          <div style={{ padding: \"10px\", textAlign: \"center\" }}>\r\n            <YesNoButtons\r\n              onYes={() => handleYesNoResponse(true, \"more_queries\")}\r\n              onNo={() => handleYesNoResponse(false, \"more_queries\")}\r\n              yesText=\"Yes\"\r\n              noText=\"No\"\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        {/* Yes/No Buttons for Ticket Closure */}\r\n        {awaitingCloseConfirmation && (\r\n          <div style={{ padding: \"10px\", textAlign: \"center\" }}>\r\n            <YesNoButtons\r\n              onYes={() => handleYesNoResponse(true, \"close_ticket\")}\r\n              onNo={() => handleYesNoResponse(false, \"close_ticket\")}\r\n              yesText=\"Close Ticket\"\r\n              noText=\"Keep Open\"\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        {/* Yes/No Buttons for Unrelated Query */}\r\n        {awaitingUnrelatedQueryResponse && (\r\n          <div style={{ padding: \"10px\", textAlign: \"center\" }}>\r\n            <YesNoButtons\r\n              onYes={() => handleYesNoResponse(true, \"unrelated_query\")}\r\n              onNo={() => handleYesNoResponse(false, \"unrelated_query\")}\r\n              yesText=\"Create New Ticket\"\r\n              noText=\"Continue Current\"\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        <div ref={messagesEndRef} />\r\n      </div>\r\n\r\n      {error && (\r\n        <div\r\n          className=\"error-message\"\r\n          onClick={() => setError(\"\")}\r\n          style={{ cursor: \"pointer\" }}\r\n          role=\"alert\"\r\n          aria-live=\"assertive\"\r\n          tabIndex={0}\r\n        >\r\n          {error} (click to dismiss)\r\n        </div>\r\n      )}\r\n\r\n      {/* INPUT DISABLED MESSAGE */}\r\n      {isInputDisabled() && (\r\n        <div style={{\r\n          backgroundColor: \"#fff3cd\",\r\n          color: \"#856404\",\r\n          padding: \"10px\",\r\n          borderRadius: \"6px\",\r\n          margin: \"10px 0\",\r\n          textAlign: \"center\",\r\n          border: \"1px solid #ffeaa7\",\r\n          fontSize: \"14px\",\r\n          fontWeight: \"bold\"\r\n        }}>\r\n          💬 Please use the buttons above to respond. Text input is temporarily disabled.\r\n        </div>\r\n      )}\r\n\r\n      <form className=\"chat-input-form\" onSubmit={handleSubmit} aria-label=\"Send message form\">\r\n        <div className=\"input-container\" style={{ display: \"flex\", alignItems: \"center\", width: \"100%\" }}>\r\n          <input\r\n            type=\"text\"\r\n            value={query}\r\n            onChange={onInputChange}\r\n            placeholder={\r\n              orgVerified\r\n                ? ticketStep > 0\r\n                  ? ticketQuestions[ticketStep - 1]\r\n                  : isInputDisabled()\r\n                  ? \"Please use the buttons above to respond...\"\r\n                  : \"Type your question here...\"\r\n                : \"Enter your organization name...\"\r\n            }\r\n            disabled={loading || verifying || (ticketStep === 1 && !query) || isInputDisabled()}\r\n            autoFocus\r\n            aria-label=\"Chat input\"\r\n            style={{\r\n              flex: \"1\",\r\n              width: \"100%\",\r\n              padding: \"8px\",\r\n              borderRadius: \"4px 0 0 4px\",\r\n              border: \"1px solid #ccc\",\r\n              margin: 0,\r\n              backgroundColor: isInputDisabled() ? \"#f5f5f5\" : \"white\",\r\n              color: isInputDisabled() ? \"#999\" : \"black\",\r\n              cursor: isInputDisabled() ? \"not-allowed\" : \"text\",\r\n            }}\r\n            onKeyDown={(e) => {\r\n              if (e.key === \"Enter\" && !e.shiftKey) {\r\n                e.preventDefault();\r\n                handleSubmit(e);\r\n              }\r\n            }}\r\n          />\r\n          {ticketStep === 1 && (\r\n            <select\r\n              value={ticketData.productType}\r\n              onChange={handleProductTypeChange}\r\n              style={{\r\n                marginLeft: \"0\",\r\n                padding: \"6px\",\r\n                borderRadius: \"0\",\r\n                border: \"1px solid #ccc\",\r\n                borderLeft: \"none\",\r\n                fontSize: \"1em\",\r\n              }}\r\n              aria-label=\"Select product type\"\r\n            >\r\n              <option value=\"\" disabled>\r\n                Select a product type\r\n              </option>\r\n              {productTypeOptions.map((option) => (\r\n                <option key={option} value={option}>\r\n                  {option}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          )}\r\n          <button\r\n            type=\"submit\"\r\n            disabled={loading || !query.trim() || verifying || (ticketStep === 1 && !ticketData.productType)}\r\n            title={loading || verifying ? \"Please wait...\" : \"Send\"}\r\n            aria-label=\"Send message\"\r\n            style={{ padding: \"8px 12px\", borderRadius: \"0 4px 4px 0\", border: \"1px solid #ccc\", borderLeft: \"none\", backgroundColor: \"#4CAF50\", color: \"white\" }}\r\n          >\r\n            {loading || verifying ? <span className=\"spinner\" /> : \"📤\"}\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,YAAY,EAAEC,mBAAmB,EAAEC,kBAAkB,QAAQ,gBAAgB;AACtF,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG,uBAAuB;AAE3C,eAAe,SAASC,IAAIA,CAAC;EAAEC;AAAM,CAAC,EAAE;EAAAC,EAAA;EACtC,MAAMC,WAAW,GAAGF,KAAK,IAAIG,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;;EAE3D;EACA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmB,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,IAAI,EAAEC,OAAO,CAAC,GAAG1B,QAAQ,CAAC,QAAQ,CAAC;EAC1C,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACqC,yBAAyB,EAAEC,4BAA4B,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACjF,MAAM,CAACuC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2C,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC6C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC+C,YAAY,EAAEC,eAAe,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACmD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAACqD,8BAA8B,EAAEC,iCAAiC,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC3F,MAAM,CAACuD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAMyD,2BAA2B,GAAG,EAAE;EACtC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC4D,WAAW,EAAEC,cAAc,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC;;EAG3D;EACA,MAAM,CAACgE,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACkE,UAAU,EAAEC,aAAa,CAAC,GAAGnE,QAAQ,CAAC;IAC3CoE,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,0BAA0B,EAAEC,6BAA6B,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAAC6E,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EAEpE,MAAM+E,eAAe,GAAG,CACtB,0DAA0D,EAC1D,gDAAgD,EAChD,sCAAsC,EACtC,kCAAkC,EAClC,2BAA2B,EAC3B,mCAAmC,EACnC,sCAAsC,CACvC;EAED,MAAMC,kBAAkB,GAAG,CAAC,QAAQ,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,CAAC;;EAEjF;EACA/E,SAAS,CAAC,MAAM;IAAA,IAAAgF,qBAAA;IACd,CAAAA,qBAAA,GAAAC,cAAc,CAACC,OAAO,cAAAF,qBAAA,uBAAtBA,qBAAA,CAAwBG,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAACpE,QAAQ,EAAEI,OAAO,EAAEE,KAAK,CAAC,CAAC;EAE9B,MAAM2D,cAAc,GAAGhF,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACAD,SAAS,CAAC,MAAM;IACdqF,KAAK,CAAC,GAAG9E,WAAW,wBAAwB,CAAC,CAC1C+E,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEG,IAAI,IAAKtE,iBAAiB,CAACsE,IAAI,CAACC,QAAQ,CAAC,CAAC,CAChDC,KAAK,CAAEC,GAAG,IAAK;MACdC,OAAO,CAACvE,KAAK,CAAC,kCAAkC,EAAEsE,GAAG,CAAC;MACtDzE,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnB,SAAS,CAAC,MAAM;IACd,MAAM8F,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,MAAMC,aAAa,GAAGL,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC,KAAK,SAAS;IACzD,MAAMC,eAAe,GAAGP,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC,KAAK,KAAK;IACvD,MAAME,YAAY,GAAGR,SAAS,CAACM,GAAG,CAAC,QAAQ,CAAC;IAE5C,IAAID,aAAa,IAAIG,YAAY,EAAE;MACjC;MACAC,yBAAyB,CAACD,YAAY,CAAC;IACzC,CAAC,MAAM,IAAIH,aAAa,EAAE;MACxB;MACAK,yBAAyB,CAAC,CAAC;IAC7B,CAAC,MAAM,IAAIH,eAAe,IAAIC,YAAY,EAAE;MAC1C;MACAG,aAAa,CAACH,YAAY,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAtG,SAAS,CAAC,MAAM;IACd,IAAI,CAACW,WAAW,EAAE;IAElB,MAAMmF,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,MAAMC,aAAa,GAAGL,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC,KAAK,SAAS;IAEzD,IAAID,aAAa,EAAE;MACjB,OAAO,CAAC;IACV;IAEAd,KAAK,CAAC,GAAG9E,WAAW,iBAAiB,EAAE;MACrCmG,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAE,UAAUhG,WAAW;MACtC;IACF,CAAC,CAAC,CACC2E,IAAI,CAAC,MAAOC,GAAG,IAAK;MACnB,IAAI,CAACA,GAAG,CAACqB,EAAE,EAAE;QACX,MAAMC,SAAS,GAAG,MAAMtB,GAAG,CAACuB,IAAI,CAAC,CAAC;QAClCjB,OAAO,CAACvE,KAAK,CAAC,yBAAyB,EAAEiE,GAAG,CAACwB,MAAM,EAAEF,SAAS,CAAC;QAC/D,MAAM,IAAIG,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MACA,OAAOzB,GAAG,CAACC,IAAI,CAAC,CAAC;IACnB,CAAC,CAAC,CACDF,IAAI,CAAEG,IAAI,IAAK;MACd,MAAMwB,IAAI,GAAGxB,IAAI,CAACwB,IAAI,IAAIxB,IAAI,CAACzD,QAAQ,IAAIyD,IAAI,CAACyB,KAAK;MACrD,IAAI,CAACD,IAAI,EAAE,MAAM,IAAID,KAAK,CAAC,0BAA0B,CAAC;MAEtD/E,WAAW,CAACgF,IAAI,CAAC;MACjBhG,WAAW,CAAC,CACV;QACEkG,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE,eAAeJ,IAAI,+DAA+D;QAC3FK,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;IACJ,CAAC,CAAC,CACD5B,KAAK,CAAEC,GAAG,IAAK;MACdC,OAAO,CAACvE,KAAK,CAAC,4BAA4B,EAAEsE,GAAG,CAAC4B,OAAO,CAAC;MACxD5G,YAAY,CAAC6G,UAAU,CAAC,QAAQ,CAAC;MACjCzB,MAAM,CAACC,QAAQ,CAACyB,IAAI,GAAG,OAAO;IAChC,CAAC,CAAC;EACN,CAAC,EAAE,CAAC/G,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAM8F,aAAa,GAAG,MAAOH,YAAY,IAAK;IAC5C,IAAI,CAAC3F,WAAW,EAAE;IAElB,IAAI;MACF;MACA,MAAMgH,YAAY,GAAG,MAAMtC,KAAK,CAAC,GAAG9E,WAAW,iBAAiB,EAAE;QAChEmG,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUhG,WAAW;QACtC;MACF,CAAC,CAAC;MAEF,IAAI,CAACgH,YAAY,CAACf,EAAE,EAAE;QACpB,MAAM,IAAII,KAAK,CAAC,2BAA2B,CAAC;MAC9C;MAEA,MAAMY,QAAQ,GAAG,MAAMD,YAAY,CAACnC,IAAI,CAAC,CAAC;MAC1C,MAAMyB,IAAI,GAAGW,QAAQ,CAACX,IAAI,IAAIW,QAAQ,CAAC5F,QAAQ,IAAI4F,QAAQ,CAACV,KAAK;MACjEjF,WAAW,CAACgF,IAAI,CAAC;MACjBpF,cAAc,CAAC,IAAI,CAAC;MACpBkB,eAAe,CAACuD,YAAY,CAAC;;MAE7B;MACA,IAAI;QACF,MAAMuB,cAAc,GAAG,MAAMxC,KAAK,CAAC,GAAG9E,WAAW,eAAe+F,YAAY,GAAG,EAAE;UAC/EI,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClCC,aAAa,EAAE,UAAUhG,WAAW;UACtC;QACF,CAAC,CAAC;QAEF,IAAIkH,cAAc,CAACjB,EAAE,EAAE;UACrB,MAAM3C,UAAU,GAAG,MAAM4D,cAAc,CAACrC,IAAI,CAAC,CAAC;UAE9CvE,WAAW,CAAC,CACV;YACEkG,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,KAAK;YACXC,OAAO,EAAE,eAAeJ,IAAI,6BAA6BX,YAAY,iCAAiC;YACtGgB,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,EACD;YACEJ,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,KAAK;YACXC,OAAO,EAAE,0BAA0Bf,YAAY,4BAA4BrC,UAAU,CAAC6D,MAAM,CAACC,WAAW,IAAI,UAAU,0CAA0C9D,UAAU,CAAC6D,MAAM,CAACE,mBAAmB,IAAI,gBAAgB,yCAAyC;YAClQV,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,CACF,CAAC;QACJ,CAAC,MAAM;UACL;UACAtG,WAAW,CAAC,CACV;YACEkG,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,KAAK;YACXC,OAAO,EAAE,eAAeJ,IAAI,6BAA6BX,YAAY,iCAAiC;YACtGgB,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,EACD;YACEJ,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,KAAK;YACXC,OAAO,EAAE,0BAA0Bf,YAAY,yCAAyC;YACxFgB,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,CACF,CAAC;QACJ;MACF,CAAC,CAAC,OAAO3B,GAAG,EAAE;QACZC,OAAO,CAACvE,KAAK,CAAC,iCAAiC,EAAEsE,GAAG,CAAC;QACrD;QACA3E,WAAW,CAAC,CACV;UACEkG,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,eAAeJ,IAAI,6BAA6BX,YAAY,iCAAiC;UACtGgB,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,EACD;UACEJ,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,0BAA0Bf,YAAY,yCAAyC;UACxFgB,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;MACJ;IACF,CAAC,CAAC,OAAO3B,GAAG,EAAE;MACZC,OAAO,CAACvE,KAAK,CAAC,4BAA4B,EAAEsE,GAAG,CAAC;MAChDrE,QAAQ,CAAC,0CAA0C,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMgF,yBAAyB,GAAG,MAAOD,YAAY,IAAK;IACxD,IAAI,CAAC3F,WAAW,EAAE;IAElB,IAAI;MACF;MACA,MAAMgH,YAAY,GAAG,MAAMtC,KAAK,CAAC,GAAG9E,WAAW,iBAAiB,EAAE;QAChEmG,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUhG,WAAW;QACtC;MACF,CAAC,CAAC;MAEF,IAAI,CAACgH,YAAY,CAACf,EAAE,EAAE;QACpB,MAAM,IAAII,KAAK,CAAC,2BAA2B,CAAC;MAC9C;MAEA,MAAMY,QAAQ,GAAG,MAAMD,YAAY,CAACnC,IAAI,CAAC,CAAC;MAC1C,MAAMyB,IAAI,GAAGW,QAAQ,CAACX,IAAI,IAAIW,QAAQ,CAAC5F,QAAQ,IAAI4F,QAAQ,CAACV,KAAK;MACjEjF,WAAW,CAACgF,IAAI,CAAC;MACjBpF,cAAc,CAAC,IAAI,CAAC;MACpBkB,eAAe,CAACuD,YAAY,CAAC;;MAE7B;MACA,MAAMuB,cAAc,GAAG,MAAMxC,KAAK,CAAC,GAAG9E,WAAW,eAAe+F,YAAY,GAAG,EAAE;QAC/EI,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUhG,WAAW;QACtC;MACF,CAAC,CAAC;MAEF,IAAI,CAACkH,cAAc,CAACjB,EAAE,EAAE;QACtB,MAAM,IAAII,KAAK,CAAC,gCAAgC,CAAC;MACnD;MAEA,MAAMc,MAAM,GAAG,MAAMD,cAAc,CAACrC,IAAI,CAAC,CAAC;MAE1C,MAAMxE,QAAQ,GAAG,CACf;QACEmG,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE,oBAAoBJ,IAAI,kCAAkCX,YAAY,EAAE;QACjFgB,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,EACD;QACEJ,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE,sCAAsCS,MAAM,CAACC,WAAW,IAAI,UAAU,kBAAkBD,MAAM,CAACE,mBAAmB,IAAI,0BAA0B,EAAE;QAC3JV,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF;;MAED;MACA,IAAIO,MAAM,CAACG,gBAAgB,IAAIH,MAAM,CAACG,gBAAgB,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,IAAIJ,MAAM,CAACG,gBAAgB,KAAK,kBAAkB,EAAE;QACtHjH,QAAQ,CAACmH,IAAI,CAAC;UACZhB,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,sCAAsCS,MAAM,CAACG,gBAAgB,8DAA8D;UACpIX,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CAAC;MACJ,CAAC,MAAM;QACLvG,QAAQ,CAACmH,IAAI,CAAC;UACZhB,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,sCAAsC;UAC/CC,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CAAC;MACJ;MAEAtG,WAAW,CAACD,QAAQ,CAAC;IACvB,CAAC,CAAC,OAAO4E,GAAG,EAAE;MACZC,OAAO,CAACvE,KAAK,CAAC,yCAAyC,EAAEsE,GAAG,CAAC;MAC7DrE,QAAQ,CAAC,0CAA0C,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMiF,yBAAyB,GAAG,MAAAA,CAAA,KAAY;IAC5C,IAAI,CAAC7F,WAAW,EAAE;IAElB,IAAI;MACF;MACA,MAAMgH,YAAY,GAAG,MAAMtC,KAAK,CAAC,GAAG9E,WAAW,iBAAiB,EAAE;QAChEmG,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUhG,WAAW;QACtC;MACF,CAAC,CAAC;MAEF,IAAI,CAACgH,YAAY,CAACf,EAAE,EAAE;QACpB,MAAM,IAAII,KAAK,CAAC,2BAA2B,CAAC;MAC9C;MAEA,MAAMY,QAAQ,GAAG,MAAMD,YAAY,CAACnC,IAAI,CAAC,CAAC;MAC1C,MAAMyB,IAAI,GAAGW,QAAQ,CAACX,IAAI,IAAIW,QAAQ,CAAC5F,QAAQ,IAAI4F,QAAQ,CAACV,KAAK;MACjEjF,WAAW,CAACgF,IAAI,CAAC;MACjBpF,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,MAAMuG,eAAe,GAAG,MAAM/C,KAAK,CAAC,GAAG9E,WAAW,uBAAuB,EAAE;QACzEmG,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUhG,WAAW;QACtC;MACF,CAAC,CAAC;MAEF,IAAI,CAACyH,eAAe,CAACxB,EAAE,EAAE;QACvB,MAAM,IAAII,KAAK,CAAC,iCAAiC,CAAC;MACpD;MAEA,MAAMqB,WAAW,GAAG,MAAMD,eAAe,CAAC5C,IAAI,CAAC,CAAC;MAEhD,IAAI6C,WAAW,CAAC5E,OAAO,IAAI4E,WAAW,CAAC5E,OAAO,CAAC6E,MAAM,GAAG,CAAC,EAAE;QACzD,MAAMC,YAAY,GAAGF,WAAW,CAAC5E,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7CV,eAAe,CAACwF,YAAY,CAACC,aAAa,CAAC;;QAE3C;QACA,IAAI;UACF,MAAMC,cAAc,GAAG,MAAMpD,KAAK,CAAC,GAAG9E,WAAW,eAAegI,YAAY,CAACC,aAAa,GAAG,EAAE;YAC7F9B,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClCC,aAAa,EAAE,UAAUhG,WAAW;YACtC;UACF,CAAC,CAAC;UAEF,IAAI8H,cAAc,CAAC7B,EAAE,EAAE;YACrB,MAAM8B,UAAU,GAAG,MAAMD,cAAc,CAACjD,IAAI,CAAC,CAAC;YAC9C,MAAMsC,MAAM,GAAGY,UAAU;YAEzB,MAAM1H,QAAQ,GAAG,CACf;cACEmG,EAAE,EAAE,CAAC;cACLC,IAAI,EAAE,KAAK;cACXC,OAAO,EAAE,oBAAoBJ,IAAI,kCAAkCsB,YAAY,CAACC,aAAa,EAAE;cAC/FlB,SAAS,EAAE,IAAIC,IAAI,CAAC;YACtB,CAAC,EACD;cACEJ,EAAE,EAAE,CAAC;cACLC,IAAI,EAAE,KAAK;cACXC,OAAO,EAAE,sCAAsCS,MAAM,CAACC,WAAW,IAAI,UAAU,kBAAkBD,MAAM,CAACE,mBAAmB,IAAI,0BAA0B,EAAE;cAC3JV,SAAS,EAAE,IAAIC,IAAI,CAAC;YACtB,CAAC,CACF;;YAED;YACA,IAAIO,MAAM,CAACG,gBAAgB,IAAIH,MAAM,CAACG,gBAAgB,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,IAAIJ,MAAM,CAACG,gBAAgB,KAAK,kBAAkB,EAAE;cACtHjH,QAAQ,CAACmH,IAAI,CAAC;gBACZhB,EAAE,EAAE,CAAC;gBACLC,IAAI,EAAE,KAAK;gBACXC,OAAO,EAAE,sCAAsCS,MAAM,CAACG,gBAAgB,8DAA8D;gBACpIX,SAAS,EAAE,IAAIC,IAAI,CAAC;cACtB,CAAC,CAAC;YACJ,CAAC,MAAM;cACLvG,QAAQ,CAACmH,IAAI,CAAC;gBACZhB,EAAE,EAAE,CAAC;gBACLC,IAAI,EAAE,KAAK;gBACXC,OAAO,EAAE,sCAAsC;gBAC/CC,SAAS,EAAE,IAAIC,IAAI,CAAC;cACtB,CAAC,CAAC;YACJ;YAEAtG,WAAW,CAACD,QAAQ,CAAC;UACvB,CAAC,MAAM;YACL;YACAC,WAAW,CAAC,CACV;cACEkG,EAAE,EAAE,CAAC;cACLC,IAAI,EAAE,KAAK;cACXC,OAAO,EAAE,oBAAoBJ,IAAI,kCAAkCsB,YAAY,CAACC,aAAa,EAAE;cAC/FlB,SAAS,EAAE,IAAIC,IAAI,CAAC;YACtB,CAAC,EACD;cACEJ,EAAE,EAAE,CAAC;cACLC,IAAI,EAAE,KAAK;cACXC,OAAO,EAAE,sCAAsCkB,YAAY,CAACI,KAAK,IAAIJ,YAAY,CAACR,WAAW,IAAI,UAAU,kBAAkBQ,YAAY,CAACP,mBAAmB,IAAI,0BAA0B,0CAA0C;cACrOV,SAAS,EAAE,IAAIC,IAAI,CAAC;YACtB,CAAC,CACF,CAAC;UACJ;QACF,CAAC,CAAC,OAAO3B,GAAG,EAAE;UACZC,OAAO,CAACvE,KAAK,CAAC,uCAAuC,EAAEsE,GAAG,CAAC;UAC3D;UACA3E,WAAW,CAAC,CACV;YACEkG,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,KAAK;YACXC,OAAO,EAAE,oBAAoBJ,IAAI,kCAAkCsB,YAAY,CAACC,aAAa,EAAE;YAC/FlB,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,EACD;YACEJ,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,KAAK;YACXC,OAAO,EAAE,sCAAsCkB,YAAY,CAACI,KAAK,IAAIJ,YAAY,CAACR,WAAW,IAAI,UAAU,kBAAkBQ,YAAY,CAACP,mBAAmB,IAAI,0BAA0B,0CAA0C;YACrOV,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,CACF,CAAC;QACJ;MACF,CAAC,MAAM;QACLtG,WAAW,CAAC,CACV;UACEkG,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,eAAeJ,IAAI,oEAAoE;UAChGK,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;MACJ;IACF,CAAC,CAAC,OAAO3B,GAAG,EAAE;MACZC,OAAO,CAACvE,KAAK,CAAC,gCAAgC,EAAEsE,GAAG,CAAC;MACpDrE,QAAQ,CAAC,kDAAkD,CAAC;IAC9D;EACF,CAAC;;EAED;EACA,MAAMqH,kBAAkB,GAAIC,IAAI,IAAK;IACnC;IACA7C,MAAM,CAAC8C,IAAI,CAACD,IAAI,CAACE,GAAG,EAAE,QAAQ,CAAC;;IAE/B;IACA9H,WAAW,CAAC+H,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,GAAG,IAAI;MAClC,IAAIA,GAAG,CAACC,KAAK,IAAID,GAAG,CAACC,KAAK,CAACb,MAAM,GAAG,CAAC,EAAE;QACrC;QACA,OAAO;UACL,GAAGY,GAAG;UACN7B,OAAO,EAAE6B,GAAG,CAAC7B,OAAO,CAAC+B,OAAO,CAAC,iEAAiE,EAAE,EAAE,CAAC;UACnGD,KAAK,EAAE;QACT,CAAC;MACH;MACA,OAAOD,GAAG;IACZ,CAAC,CAAC,CAAC;IAEHvH,eAAe,CAAC,IAAI,CAAC;;IAErB;IACA0H,MAAM,CAAC,6EAA6E,CAAC;IACrF9G,uBAAuB,CAAC,IAAI,CAAC;EAC/B,CAAC;;EAED;EACA,MAAM+G,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACxG,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMyG,QAAQ,GAAG,MAAMlE,KAAK,CAAC,GAAG9E,WAAW,4BAA4B,EAAE;QACvEiJ,MAAM,EAAE,MAAM;QACd9C,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUhG,WAAW;QACtC,CAAC;QACD8I,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBnB,aAAa,EAAE1F,YAAY;UAC3BiE,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAIwC,QAAQ,CAAC3C,EAAE,EAAE;QACfyC,MAAM,CAAC,YAAYvG,YAAY,wEAAwE,CAAC;;QAExG;QACA8G,UAAU,CAAC,MAAM;UACfC,YAAY,CAAC,CAAC;QAChB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLR,MAAM,CAAC,iDAAiD,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOzD,GAAG,EAAE;MACZC,OAAO,CAACvE,KAAK,CAAC,uBAAuB,EAAEsE,GAAG,CAAC;MAC3CyD,MAAM,CAAC,8DAA8D,CAAC;IACxE;EACF,CAAC;;EAED;EACA,MAAMS,mBAAmB,GAAGA,CAACP,QAAQ,EAAEQ,OAAO,KAAK;IACjD,MAAMC,OAAO,GAAG;MACd7C,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC;MACd7C,IAAI,EAAE,MAAM;MACZC,OAAO,EAAEkC,QAAQ,GAAG,KAAK,GAAG,IAAI;MAChCjC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IACDtG,WAAW,CAAE+H,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEgB,OAAO,CAAC,CAAC;IAEzC,IAAID,OAAO,KAAK,eAAe,EAAE;MAC/B,IAAIR,QAAQ,IAAI7H,YAAY,IAAIA,YAAY,CAAC4G,MAAM,GAAG,CAAC,EAAE;QACvDM,kBAAkB,CAAClH,YAAY,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,MAAM;QACLC,eAAe,CAAC,IAAI,CAAC;QACrB0H,MAAM,CAAC,mDAAmD,CAAC;QAC3D9G,uBAAuB,CAAC,IAAI,CAAC;MAC/B;IACF,CAAC,MAAM,IAAIwH,OAAO,KAAK,cAAc,EAAE;MACrCxH,uBAAuB,CAAC,KAAK,CAAC;MAC9B,IAAIgH,QAAQ,EAAE;QACZF,MAAM,CAAC,2BAA2B,CAAC;MACrC,CAAC,MAAM;QACLA,MAAM,CAAC,mCAAmC,CAAC;QAC3ChH,4BAA4B,CAAC,IAAI,CAAC;MACpC;IACF,CAAC,MAAM,IAAI0H,OAAO,KAAK,cAAc,EAAE;MACrC1H,4BAA4B,CAAC,KAAK,CAAC;MACnC,IAAIkH,QAAQ,EAAE;QACZD,iBAAiB,CAAC,CAAC;MACrB,CAAC,MAAM;QACLD,MAAM,CAAC,+CAA+C,CAAC;MACzD;IACF,CAAC,MAAM,IAAIU,OAAO,KAAK,iBAAiB,EAAE;MACxC1G,iCAAiC,CAAC,KAAK,CAAC;MACxC,IAAIkG,QAAQ,EAAE;QACZ;QACAvD,MAAM,CAACC,QAAQ,CAACyB,IAAI,GAAG,aAAa;MACtC,CAAC,MAAM;QACL2B,MAAM,CAAC,8DAA8D,CAAC;MACxE;IACF;EACF,CAAC;;EAED;EACArJ,SAAS,CAAC,MAAM;IACd,MAAMkK,sBAAsB,GAAG5H,oBAAoB,IAAIF,yBAAyB,IAAIgB,8BAA8B,IAAK1B,YAAY,IAAIA,YAAY,CAAC4G,MAAM,GAAG,CAAE;IAE/J,IAAI4B,sBAAsB,EAAE;MAC1B;MACA,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;QACjC,IAAIzJ,WAAW,EAAE;UACf0E,KAAK,CAAC,GAAG9E,WAAW,iBAAiB,EAAE;YACrCmG,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClCC,aAAa,EAAE,UAAUhG,WAAW;YACtC;UACF,CAAC,CAAC,CAACgF,KAAK,CAAC,MAAM;YACb;UAAA,CACD,CAAC;QACJ;MACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;MAEXpC,mBAAmB,CAAC4G,QAAQ,CAAC;MAE7B,OAAO,MAAM;QACX,IAAIA,QAAQ,EAAE;UACZE,aAAa,CAACF,QAAQ,CAAC;QACzB;MACF,CAAC;IACH,CAAC,MAAM;MACL;MACA,IAAI7G,gBAAgB,EAAE;QACpB+G,aAAa,CAAC/G,gBAAgB,CAAC;QAC/BC,mBAAmB,CAAC,IAAI,CAAC;MAC3B;IACF;EACF,CAAC,EAAE,CAACjB,oBAAoB,EAAEF,yBAAyB,EAAEgB,8BAA8B,EAAE1B,YAAY,EAAEf,WAAW,EAAE2C,gBAAgB,CAAC,CAAC;;EAElI;EACAtD,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIsD,gBAAgB,EAAE;QACpB+G,aAAa,CAAC/G,gBAAgB,CAAC;MACjC;IACF,CAAC;EACH,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMgH,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAOhI,oBAAoB,IAAIF,yBAAyB,IAAIgB,8BAA8B,IAAK1B,YAAY,IAAIA,YAAY,CAAC4G,MAAM,GAAG,CAAE;EACzI,CAAC;;EAED;EACA,MAAMiC,aAAa,GAAIC,CAAC,IAAK;IAC3B,IAAIlJ,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;IACvBR,QAAQ,CAACyJ,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAIH,CAAC,IAAK;IACrC,MAAMI,YAAY,GAAGJ,CAAC,CAACC,MAAM,CAACC,KAAK;IACnCxG,aAAa,CAAC;MAAE,GAAGD,UAAU;MAAEE,WAAW,EAAEyG;IAAa,CAAC,CAAC;IAC3D3J,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;MACE7B,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC;MACd7C,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,0BAA0BuD,YAAY,EAAE;MACjDtD,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC,CACF,CAAC;IACFvD,aAAa,CAACD,UAAU,GAAG,CAAC,CAAC;IAC7BsF,MAAM,CAACvE,eAAe,CAACf,UAAU,CAAC,CAAC;EACrC,CAAC;;EAED;EACA,MAAM8G,kBAAkB,GAAG,MAAOC,OAAO,IAAK;IAC5C/I,YAAY,CAAC,IAAI,CAAC;IAClBR,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMgI,QAAQ,GAAG,MAAMlE,KAAK,CAAC,GAAG9E,WAAW,2BAA2B,EAAE;QACtEiJ,MAAM,EAAE,MAAM;QACd9C,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUhG,WAAW;QACtC,CAAC;QACD8I,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEoB,YAAY,EAAED;QAAQ,CAAC;MAChD,CAAC,CAAC;MAEF,MAAMrF,IAAI,GAAG,MAAM8D,QAAQ,CAAC/D,IAAI,CAAC,CAAC;MAElC,IAAI+D,QAAQ,CAAC3C,EAAE,IAAInB,IAAI,CAACsB,MAAM,KAAK,UAAU,EAAE;QAC7ClF,cAAc,CAAC,IAAI,CAAC;QACpBI,WAAW,CAACwD,IAAI,CAACwB,IAAI,IAAIjF,QAAQ,CAAC;QAElCf,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACE7B,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC;UACd7C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE5B,IAAI,CAAC+B,OAAO,IAAI,0BAA0B;UACnDF,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;QAEF,MAAMyD,mBAAmB,CAAC,CAAC;MAC7B,CAAC,MAAM;QACL,MAAMC,OAAO,GAAGjF,MAAM,CAACC,QAAQ,CAACiF,MAAM;QACtC,MAAMC,UAAU,GAAG,kBAAkBF,OAAO,WAAW;QAEvD1J,QAAQ,CAACkE,IAAI,CAAC+B,OAAO,IAAI,0BAA0B,CAAC;QACpDvG,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACE7B,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC;UACd7C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,sFAAsF8D,UAAU,qBAAqB;UAC9H7D,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;QAEF3G,YAAY,CAAC6G,UAAU,CAAC,QAAQ,CAAC;MACnC;IACF,CAAC,CAAC,OAAO7B,GAAG,EAAE;MACZrE,QAAQ,CAAC,oCAAoC,CAAC;MAC9CN,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QACE7B,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC;QACd7C,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE,qEAAqE;QAC9EC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;MACF1B,OAAO,CAACvE,KAAK,CAAC,qBAAqB,EAAEsE,GAAG,CAAC;IAC3C,CAAC,SAAS;MACR7D,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,eAAeiJ,mBAAmBA,CAAA,EAAG;IACnC,IAAI;MACF,MAAMzB,QAAQ,GAAG,MAAMlE,KAAK,CAAC,GAAG9E,WAAW,uBAAuB,EAAE;QAClEmG,OAAO,EAAE;UACPC,aAAa,EAAE,UAAUhG,WAAW;QACtC;MACF,CAAC,CAAC;MACF,MAAM8E,IAAI,GAAG,MAAM8D,QAAQ,CAAC/D,IAAI,CAAC,CAAC;MAElC,IAAI+D,QAAQ,CAAC3C,EAAE,EAAE;QACfnE,iBAAiB,CAACgD,IAAI,CAAChC,OAAO,IAAI,EAAE,CAAC;QACrC,IAAIgC,IAAI,CAAChC,OAAO,IAAIgC,IAAI,CAAChC,OAAO,CAAC6E,MAAM,GAAG,CAAC,EAAE;UAC3C3F,wBAAwB,CAAC,IAAI,CAAC;UAC9B0G,MAAM,CACJ,YAAY5D,IAAI,CAAChC,OAAO,CAAC6E,MAAM,wEACjC,CAAC;QACH,CAAC,MAAM;UACLnG,iBAAiB,CAAC,IAAI,CAAC;UACvBkH,MAAM,CAAC,8EAA8E,CAAC;QACxF;MACF,CAAC,MAAM;QACL5G,iBAAiB,CAAC,EAAE,CAAC;QACrBN,iBAAiB,CAAC,IAAI,CAAC;QACvBkH,MAAM,CAAC,uFAAuF,CAAC;MACjG;IACF,CAAC,CAAC,OAAOzD,GAAG,EAAE;MACZC,OAAO,CAACvE,KAAK,CAAC,iCAAiC,EAAEsE,GAAG,CAAC;MACrDnD,iBAAiB,CAAC,EAAE,CAAC;MACrBN,iBAAiB,CAAC,IAAI,CAAC;MACvBkH,MAAM,CAAC,oFAAoF,CAAC;IAC9F;EACF;;EAEA;EACA,MAAM+B,YAAY,GAAG,MAAOC,eAAe,IAAK;IAC9ChK,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,MAAM+J,iBAAiB,GAAG,CAAC,QAAQ,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,CAAC;IAChF,MAAMC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACJ,eAAe,CAAC,CAACK,KAAK,CACnDC,CAAC,IAAKA,CAAC,IAAIA,CAAC,CAACzD,IAAI,CAAC,CAAC,KAAK,EAC3B,CAAC;IACD,IAAI,CAACqD,SAAS,EAAE;MACdtK,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QACE7B,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC;QACd7C,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE,oEAAoE;QAC7EC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;MACFlG,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IACA,IAAI,CAACiK,iBAAiB,CAACM,QAAQ,CAACP,eAAe,CAAClH,WAAW,CAAC,EAAE;MAC5DlD,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QACE7B,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC;QACd7C,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE,yFAAyF;QAClGC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;MACFlG,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAI;MACF,MAAMkI,QAAQ,GAAG,MAAMlE,KAAK,CAAC,GAAG9E,WAAW,qBAAqB,EAAE;QAChEiJ,MAAM,EAAE,MAAM;QACd9C,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUhG,WAAW;QACtC,CAAC;QACD8I,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBkC,YAAY,EAAER,eAAe,CAAClH,WAAW;UACzC2H,cAAc,EAAET,eAAe,CAACjH,aAAa;UAC7C2H,gBAAgB,EAAEV,eAAe,CAAChH,cAAc;UAChD2H,YAAY,EAAEX,eAAe,CAAC/G,WAAW;UACzCC,KAAK,EAAE8G,eAAe,CAAC9G,KAAK;UAC5B0H,SAAS,EAAEZ,eAAe,CAAC7G,QAAQ;UACnC0H,gBAAgB,EAAEb,eAAe,CAAC5G;QACpC,CAAC;MACH,CAAC,CAAC;MAEF,MAAMgB,IAAI,GAAG,MAAM8D,QAAQ,CAAC/D,IAAI,CAAC,CAAC;MAElC,IAAI+D,QAAQ,CAAC3C,EAAE,EAAE;QACf/B,sBAAsB,CAACY,IAAI,CAAC+C,aAAa,CAAC;QAC1CzF,eAAe,CAAC0C,IAAI,CAAC+C,aAAa,CAAC;QACnC7D,6BAA6B,CAAC,IAAI,CAAC;QACnC1D,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACE7B,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC;UACd7C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,4FAA4F5B,IAAI,CAAC+C,aAAa,2DAA2D;UAClLlB,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;MACJ,CAAC,MAAM;QACLtG,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACE7B,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC;UACd7C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,8BAA8BqC,IAAI,CAACC,SAAS,CAAClE,IAAI,CAAC0G,MAAM,IAAI1G,IAAI,CAAC+B,OAAO,CAAC,EAAE;UACpFF,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;MACJ;IACF,CAAC,CAAC,OAAO3B,GAAG,EAAE;MACZ3E,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QACE7B,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC;QACd7C,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE,0CAA0CzB,GAAG,CAAC4B,OAAO,EAAE;QAChEF,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;IACJ,CAAC,SAAS;MACRlG,UAAU,CAAC,KAAK,CAAC;MACjB2C,aAAa,CAAC,CAAC,CAAC;MAChB7B,iBAAiB,CAAC,KAAK,CAAC;MACxB+B,aAAa,CAAC;QACZC,WAAW,EAAE,EAAE;QACfC,aAAa,EAAE,EAAE;QACjBC,cAAc,EAAE,EAAE;QAClBC,WAAW,EAAE,EAAE;QACfC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,eAAe,EAAE;MACnB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,SAAS4E,MAAMA,CAACvC,IAAI,EAAE;IACpB7F,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;MAAE7B,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC;MAAE7C,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAEP,IAAI;MAAEQ,SAAS,EAAE,IAAIC,IAAI,CAAC;IAAE,CAAC,CACtE,CAAC;EACJ;;EAEA;EACA,MAAM6E,kBAAkB,GAAG,MAAO9F,YAAY,IAAK;IACjDzD,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,eAAe,CAACuD,YAAY,CAAC;IAC7BzB,sBAAsB,CAACyB,YAAY,CAAC;IACpC1C,cAAc,CAAC,KAAK,CAAC;IAErB,IAAI;MACF,MAAMyI,UAAU,GAAG,MAAMhH,KAAK,CAAC,GAAG9E,WAAW,sBAAsB,EAAE;QACnEiJ,MAAM,EAAE,MAAM;QACd9C,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUhG,WAAW;QACtC,CAAC;QACD8I,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEnB,aAAa,EAAElC;QAAa,CAAC;MACtD,CAAC,CAAC;MAEF,MAAMgG,WAAW,GAAG,MAAMD,UAAU,CAAC7G,IAAI,CAAC,CAAC;MAE3C,IAAI6G,UAAU,CAACzF,EAAE,EAAE;QACjByC,MAAM,CACJ,wBAAwB/C,YAAY,UAAU,GAC5C,0BAA0BgG,WAAW,CAACC,eAAe,IAAID,WAAW,CAACtE,mBAAmB,MAAM,GAC9F,0BAA0BsE,WAAW,CAACrE,gBAAgB,IAAI,kBAAkB,MAAM,GAClF,yCACJ,CAAC;MACH,CAAC,MAAM;QACLoB,MAAM,CAAC,mCAAmC,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOzD,GAAG,EAAE;MACZyD,MAAM,CAAC,gDAAgD,CAAC;IAC1D;EACF,CAAC;;EAED;EACF,eAAemD,YAAYA,CAAChC,CAAC,EAAE;IAC7BA,CAAC,CAACiC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAC3L,KAAK,CAACoH,IAAI,CAAC,CAAC,IAAI9G,OAAO,IAAIU,SAAS,EAAE;IAE3C,MAAM4K,YAAY,GAAG5L,KAAK,CAACoH,IAAI,CAAC,CAAC,CAACyE,WAAW,CAAC,CAAC;;IAE/C;IACA,IACE7J,YAAY;IAAe;IAC3BiB,UAAU,KAAK,CAAC;IAAW;IAC3B,CAACW,0BAA0B;IAAI;IAC/Bb,gBAAgB,IAAI,CAAC,CAAM;IAAA,EAC3B;MACAwF,MAAM,CACJ,sNACF,CAAC;MACDtI,QAAQ,CAAC,EAAE,CAAC;MACZ;IACF;IACA;;IAEA,IAAIiC,aAAa,EAAE;MACjB,IAAIE,oBAAoB,IAAIM,2BAA2B,EAAE;QACvD6F,MAAM,CACJ,+GACF,CAAC;QACDtI,QAAQ,CAAC,EAAE,CAAC;QACZ;MACF,CAAC,MAAM;QACLoC,uBAAuB,CAAEyJ,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;MACvC;IACF;IAEA,MAAM5C,OAAO,GAAG;MACd7C,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC;MACd7C,IAAI,EAAE,MAAM;MACZC,OAAO,EAAEvG,KAAK,CAACoH,IAAI,CAAC,CAAC;MACrBZ,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IACDtG,WAAW,CAAE+H,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEgB,OAAO,CAAC,CAAC;IACzCjJ,QAAQ,CAAC,EAAE,CAAC;IACZQ,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAImB,qBAAqB,EAAE;MACzBC,wBAAwB,CAAC,KAAK,CAAC;MAE/B,IAAI+J,YAAY,KAAK,KAAK,EAAE;QAC1B7J,uBAAuB,CAAC,IAAI,CAAC;QAC7B5B,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACE7B,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC;UACd7C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,uCAAuC;UAChDC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;UACrB9D,OAAO,EAAEjB,cAAc,CAACyG,GAAG,CAAC,CAAC4D,CAAC,EAAEC,CAAC,MAAM;YACrCC,KAAK,EAAED,CAAC,GAAG,CAAC;YACZxG,YAAY,EAAEuG,CAAC,CAACrE,aAAa;YAC7BG,KAAK,EAAEkE,CAAC,CAAClE,KAAK,IAAIkE,CAAC,CAAC9E,WAAW,IAAI;UACrC,CAAC,CAAC;QACJ,CAAC,CACF,CAAC;MACJ,CAAC,MAAM,IAAI2E,YAAY,KAAK,IAAI,EAAE;QAChCvK,iBAAiB,CAAC,IAAI,CAAC;QACvBkH,MAAM,CAAC,iDAAiD,CAAC;MAC3D,CAAC,MAAM;QACLA,MAAM,CAAC,sEAAsE,CAAC;QAC9E1G,wBAAwB,CAAC,IAAI,CAAC;MAChC;MACA;IACF;IAEA,IAAIC,oBAAoB,EAAE;MACxB,MAAMoK,MAAM,GAAGxK,cAAc,CAACyK,IAAI,CAChC,CAACJ,CAAC,EAAEK,GAAG,KACLR,YAAY,KAAKS,MAAM,CAACD,GAAG,GAAG,CAAC,CAAC,IAChCR,YAAY,CAACd,QAAQ,CAACiB,CAAC,CAACrE,aAAa,CAACmE,WAAW,CAAC,CAAC,CACvD,CAAC;MAED,IAAI,CAACK,MAAM,EAAE;QACX3D,MAAM,CAAC,gDAAgD,CAAC;QACxD;MACF;MAEA,MAAM+C,kBAAkB,CAACY,MAAM,CAACxE,aAAa,CAAC;MAC9C;IACF;IAEA,IAAI,CAAC5G,WAAW,EAAE;MAChB,MAAMiJ,kBAAkB,CAAC6B,YAAY,CAAC;MACtC;IACF;IAEA,IAAIxK,cAAc,EAAE;MAClB,IAAIwK,YAAY,KAAK,KAAK,EAAE;QAC1BvK,iBAAiB,CAAC,KAAK,CAAC;QACxB6B,aAAa,CAAC,CAAC,CAAC;QAChBqF,MAAM,CAACvE,eAAe,CAAC,CAAC,CAAC,CAAC;QAC1B7B,gBAAgB,CAAC,KAAK,CAAC;QACvBE,uBAAuB,CAAC,CAAC,CAAC;;QAE1B;QACAW,mBAAmB,CAAC,CAAC,CAAC;QACtB;MAEF,CAAC,MAAM,IAAI4I,YAAY,KAAK,IAAI,EAAE;QAChCvK,iBAAiB,CAAC,KAAK,CAAC;QACxBkH,MAAM,CAAC,6DAA6D,CAAC;QACrEpG,gBAAgB,CAAC,IAAI,CAAC;QACtBE,uBAAuB,CAAC,CAAC,CAAC;MAC5B,CAAC,MAAM;QACLkG,MAAM,CAAC,qEAAqE,CAAC;MAC/E;MACA;IACF;IAEA,IAAIjG,8BAA8B,EAAE;MAClCC,iCAAiC,CAAC,KAAK,CAAC;MACxC,IAAIqJ,YAAY,KAAK,KAAK,EAAE;QAC1B1I,aAAa,CAAC,CAAC,CAAC;QAChBjB,eAAe,CAAC,IAAI,CAAC;QACrB8B,sBAAsB,CAAC,IAAI,CAAC;QAC5BwE,MAAM,CAACvE,eAAe,CAAC,CAAC,CAAC,CAAC;;QAE1B;QACAhB,mBAAmB,CAAC,CAAC,CAAC;QACtB;MAEF,CAAC,MAAM,IAAI4I,YAAY,KAAK,IAAI,EAAE;QAChCrK,4BAA4B,CAAC,IAAI,CAAC;QAClCgH,MAAM,CAAC,uCAAuC,CAAC;MACjD,CAAC,MAAM;QACLhG,iCAAiC,CAAC,IAAI,CAAC;QACvCgG,MAAM,CAAC,kEAAkE,CAAC;MAC5E;MACA;IACF;IAEA,IAAItF,UAAU,GAAG,CAAC,IAAIA,UAAU,IAAIe,eAAe,CAACwD,MAAM,EAAE;MAC1D,IAAIvE,UAAU,KAAK,CAAC,EAAE;QACpB,MAAM6G,YAAY,GAAG9J,KAAK,CAACoH,IAAI,CAAC,CAAC;QACjC,IAAInD,kBAAkB,CAAC6G,QAAQ,CAAChB,YAAY,CAAC,EAAE;UAC7C1G,aAAa,CAAC;YAAE,GAAGD,UAAU;YAAEE,WAAW,EAAEyG;UAAa,CAAC,CAAC;UAC3D3J,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;YACE7B,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC;YACd7C,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE,0BAA0BuD,YAAY,EAAE;YACjDtD,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,CACF,CAAC;UACFvD,aAAa,CAACD,UAAU,GAAG,CAAC,CAAC;UAC7BsF,MAAM,CAACvE,eAAe,CAACf,UAAU,CAAC,CAAC;QACrC,CAAC,MAAM;UACLsF,MAAM,CACJ,2FACF,CAAC;QACH;QACA;MACF;MACA,MAAM+D,IAAI,GAAG,CACX,eAAe,EACf,gBAAgB,EAChB,aAAa,EACb,OAAO,EACP,UAAU,EACV,iBAAiB,CAClB;MACD,MAAMC,YAAY,GAAGD,IAAI,CAACrJ,UAAU,GAAG,CAAC,CAAC;MACzC,MAAMuJ,iBAAiB,GAAG;QAAE,GAAGrJ,UAAU;QAAE,CAACoJ,YAAY,GAAGvM,KAAK,CAACoH,IAAI,CAAC;MAAE,CAAC;MACzEhE,aAAa,CAACoJ,iBAAiB,CAAC;MAEhC,IAAIvJ,UAAU,GAAGe,eAAe,CAACwD,MAAM,EAAE;QACvCtE,aAAa,CAACD,UAAU,GAAG,CAAC,CAAC;QAC7BsF,MAAM,CAACvE,eAAe,CAACf,UAAU,CAAC,CAAC;MACrC,CAAC,MAAM;QACL,MAAMqH,YAAY,CAACkC,iBAAiB,CAAC;MACvC;MACA;IACF;IAEA,IAAI5I,0BAA0B,IAAIE,mBAAmB,EAAE;MACrDD,6BAA6B,CAAC,KAAK,CAAC;MACpCtD,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAI;QAAA,IAAAkM,eAAA;QACF,MAAMC,OAAO,GAAG,MAAMnI,KAAK,CAAC,GAAG9E,WAAW,+BAA+B,EAAE;UACzEiJ,MAAM,EAAE,MAAM;UACd9C,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClCC,aAAa,EAAE,UAAUhG,WAAW;UACtC,CAAC;UACD8I,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBnB,aAAa,EAAE5D,mBAAmB;YAClCoD,mBAAmB,EAAEgC,OAAO,CAAC3C;UAC/B,CAAC;QACH,CAAC,CAAC;QAEF,MAAMoG,QAAQ,GAAG,MAAMD,OAAO,CAAChI,IAAI,CAAC,CAAC;QACrC,IAAI,CAACgI,OAAO,CAAC5G,EAAE,EAAE;UACf,MAAM,IAAII,KAAK,CAACyG,QAAQ,CAACnM,KAAK,IAAI,qCAAqC,CAAC;QAC1E;QAEA,IAAIoM,UAAU,GAAGD,QAAQ,CAACE,MAAM,IAAI,sCAAsC;QAE1E,MAAMC,QAAQ,IAAAL,eAAA,GAAGE,QAAQ,CAACtE,KAAK,cAAAoE,eAAA,cAAAA,eAAA,GAAI,EAAE,CAAC,CAAC;QACvC,MAAMM,UAAU,GAAGD,QAAQ,CAACE,MAAM,CAAEC,CAAC,IAAK;UACxC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;YACzB,OAAOA,CAAC,CAAC7F,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC6F,CAAC,CAACpB,WAAW,CAAC,CAAC,CAACqB,UAAU,CAAC,MAAM,CAAC;UAC/D;UACA,IAAI,OAAOD,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,CAACE,QAAQ,EAAE;YACrD,OACEF,CAAC,CAACE,QAAQ,CAAC/F,IAAI,CAAC,CAAC,KAAK,EAAE,IACxB,CAAC6F,CAAC,CAACE,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAACqB,UAAU,CAAC,MAAM,CAAC;UAEhD;UACA,OAAO,KAAK;QACd,CAAC,CAAC;QAEF,IAAIH,UAAU,CAACvF,MAAM,GAAG,CAAC,EAAE;UACzB3G,eAAe,CACbkM,UAAU,CAAC5E,GAAG,CAAE8E,CAAC,IAAK;YACpB,MAAME,QAAQ,GAAG,OAAOF,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGA,CAAC,CAACE,QAAQ;YACvD,MAAMlF,GAAG,GACP,OAAOgF,CAAC,KAAK,QAAQ,IAAI,CAACA,CAAC,CAAChF,GAAG,IAAI,CAACgF,CAAC,CAAChF,GAAG,CAACiF,UAAU,CAAC,MAAM,CAAC,GACxD,GAAGzN,WAAW,cAAc2N,kBAAkB,CAC5CD,QACF,CAAC,UAAUtN,WAAW,EAAE,GACxB,GAAGoN,CAAC,CAAChF,GAAG,GAAGgF,CAAC,CAAChF,GAAG,CAAC6C,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,SAASjL,WAAW,EAAE;YACtE,OAAO;cAAEwN,WAAW,EAAEF,QAAQ;cAAElF;YAAI,CAAC;UACvC,CAAC,CACH,CAAC;UACD2E,UAAU,IAAI,qEAAqE;QACrF,CAAC,MAAM;UACL/L,eAAe,CAAC,IAAI,CAAC;QACvB;QAEAV,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACE7B,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC;UACd7C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAEqG,UAAU;UACnBpG,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;QAEFhF,uBAAuB,CAAC,IAAI,CAAC;MAC/B,CAAC,CAAC,OAAOqD,GAAG,EAAE;QACZ3E,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACE7B,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC;UACd7C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,2CAA2CzB,GAAG,CAAC4B,OAAO,EAAE;UACjEF,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;MACJ,CAAC,SAAS;QACRlG,UAAU,CAAC,KAAK,CAAC;MACnB;MACA;IACF;IAEA,IAAIK,YAAY,KAAKgL,YAAY,KAAK,KAAK,IAAIA,YAAY,KAAK,IAAI,CAAC,EAAE;MACrE,MAAM0B,YAAY,GAAG,EAAE;MAEvB,IAAI1B,YAAY,KAAK,KAAK,EAAE;QAC1B,MAAM2B,aAAa,GAAG3M,YAAY,CAC/BuH,GAAG,CAAC,CAAC8E,CAAC,EAAEb,GAAG,KAAK,GAAGA,GAAG,GAAG,CAAC,MAAMa,CAAC,CAACI,WAAW,KAAKJ,CAAC,CAAChF,GAAG,GAAG,CAAC,CAC3DuF,IAAI,CAAC,IAAI,CAAC;QAEbF,YAAY,CAACjG,IAAI,CAAC;UAChBhB,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC,GAAG,CAAC;UAClB7C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,qCAAqCgH,aAAa,EAAE;UAC7D/G,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL6G,YAAY,CAACjG,IAAI,CAAC;UAChBhB,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC,GAAG,CAAC;UAClB7C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,iCAAiC;UAC1CC,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CAAC;MACJ;MAEA,IAAIzE,YAAY,KAAK,IAAI,EAAE;QACzBsL,YAAY,CAACjG,IAAI,CAAC;UAChBhB,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC,GAAG,CAAC;UAClB7C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,yCAAyC;UAClDC,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CAAC;QACFhF,uBAAuB,CAAC,IAAI,CAAC;MAC/B;MAEAtB,WAAW,CAAE+H,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAE,GAAGoF,YAAY,CAAC,CAAC;MACjDzM,eAAe,CAAC,IAAI,CAAC;MACrB;IACF;IAEA,IAAIW,oBAAoB,EAAE;MACxB,IAAIoK,YAAY,KAAK,IAAI,EAAE;QACzBnK,uBAAuB,CAAC,KAAK,CAAC;QAC9BF,4BAA4B,CAAC,IAAI,CAAC;QAClCgH,MAAM,CAAC,uCAAuC,CAAC;MACjD,CAAC,MAAM,IAAIqD,YAAY,KAAK,KAAK,EAAE;QACjCnK,uBAAuB,CAAC,KAAK,CAAC;QAC9B8G,MAAM,CAAC,wCAAwC,CAAC;MAClD,CAAC,MAAM;QACLA,MAAM,CAAC,6DAA6D,CAAC;MACvE;MACA;IACF;IAEA,IAAIjH,yBAAyB,EAAE;MAC7B,IAAIsK,YAAY,KAAK,KAAK,EAAE;QAC1BrK,4BAA4B,CAAC,KAAK,CAAC;QACnChB,UAAU,CAAC,IAAI,CAAC;QAChB,IAAI;UACF,MAAMkI,QAAQ,GAAG,MAAMlE,KAAK,CAAC,GAAG9E,WAAW,4BAA4B,EAAE;YACvEiJ,MAAM,EAAE,MAAM;YACd9C,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClCC,aAAa,EAAE,UAAUhG,WAAW;YACtC,CAAC;YACD8I,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBnB,aAAa,EAAE5D,mBAAmB;cAClCmC,MAAM,EAAE;YACV,CAAC;UACH,CAAC,CAAC;UACF,MAAMtB,IAAI,GAAG,MAAM8D,QAAQ,CAAC/D,IAAI,CAAC,CAAC;UAElC,IAAI+D,QAAQ,CAAC3C,EAAE,EAAE;YACf7D,eAAe,CAAC,IAAI,CAAC;YACrB8B,sBAAsB,CAAC,IAAI,CAAC;YAC5BX,aAAa,CAAC;cACZC,WAAW,EAAE,EAAE;cACfC,aAAa,EAAE,EAAE;cACjBC,cAAc,EAAE,EAAE;cAClBC,WAAW,EAAE,EAAE;cACfC,KAAK,EAAE,EAAE;cACTC,QAAQ,EAAE,EAAE;cACZC,eAAe,EAAE;YACnB,CAAC,CAAC;YAEF4E,MAAM,CAAC,YAAYzE,mBAAmB,8BAA8B,CAAC;;YAErE;YACAd,mBAAmB,CAAC,CAAC,CAAC;YACtB;UAEF,CAAC,MAAM;YACL,MAAM,IAAIkD,KAAK,CAACvB,IAAI,CAACnE,KAAK,IAAI,yBAAyB,CAAC;UAC1D;QACF,CAAC,CAAC,OAAOsE,GAAG,EAAE;UACZyD,MAAM,CAAC,2BAA2BzD,GAAG,CAAC4B,OAAO,EAAE,CAAC;QAClD,CAAC,SAAS;UACRnG,UAAU,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,MAAM,IAAIqL,YAAY,KAAK,IAAI,EAAE;QAChCrK,4BAA4B,CAAC,KAAK,CAAC;QACnCgH,MAAM,CAAC,gCAAgC,CAAC;MAC1C,CAAC,MAAM;QACLA,MAAM,CAAC,2DAA2D,CAAC;MACrE;MACA;IACF;IAEAhI,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMkN,WAAW,GAAGvN,QAAQ,CACzB8M,MAAM,CAAEU,CAAC,IAAKA,CAAC,CAACpH,IAAI,KAAK,MAAM,IAAIoH,CAAC,CAACpH,IAAI,KAAK,KAAK,CAAC,CACpD6B,GAAG,CAAEuF,CAAC,IAAK,GAAGA,CAAC,CAACpH,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,KAAK,KAAKoH,CAAC,CAACnH,OAAO,EAAE,CAAC,CACjEiH,IAAI,CAAC,IAAI,CAAC;MAEb,MAAMG,WAAW,GAAGvN,cAAc,GAC9BA,cAAc,CACXkI,OAAO,CAAC,gBAAgB,EAAE,wBAAwB,CAAC,CACnDA,OAAO,CAAC,gBAAgB,EAAEmF,WAAW,CAAC,CACtCnF,OAAO,CAAC,SAAS,EAAEtI,KAAK,CAACoH,IAAI,CAAC,CAAC,CAAC,GACnCpH,KAAK,CAACoH,IAAI,CAAC,CAAC;MAEhB,MAAMqB,QAAQ,GAAG,MAAMlE,KAAK,CAAC,GAAG9E,WAAW,YAAY,EAAE;QACvDiJ,MAAM,EAAE,MAAM;QACd9C,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUhG,WAAW;QACtC,CAAC;QACD8I,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnB7I,KAAK,EAAE2N,WAAW;UAClBC,WAAW,EAAE,CAAC,CAAC5L,YAAY;UAC3B6L,SAAS,EAAE7L,YAAY;UACvB8L,KAAK,EAAExL,8BAA8B,GAAG,iBAAiB,GAAG;QAC9D,CAAC;MACH,CAAC,CAAC;MAEF,MAAMqC,IAAI,GAAG,MAAM8D,QAAQ,CAAC/D,IAAI,CAAC,CAAC;MAElC,IAAI+D,QAAQ,CAAC3C,EAAE,EAAE;QAAA,IAAAiI,WAAA;QACf,IAAInB,UAAU,GAAGjI,IAAI,CAACkI,MAAM,IAAI,GAAG;QAEnC,IAAIlI,IAAI,CAACmJ,KAAK,KAAK,iBAAiB,EAAE;UACpCvL,iCAAiC,CAAC,IAAI,CAAC;QACzC,CAAC,MAAM,IAAIoC,IAAI,CAACmJ,KAAK,KAAK,mBAAmB,EAAE;UAC7C5K,aAAa,CAAC,CAAC,CAAC;UAChBjB,eAAe,CAAC,IAAI,CAAC;UACrB8B,sBAAsB,CAAC,IAAI,CAAC;UAC5BwE,MAAM,CAACvE,eAAe,CAAC,CAAC,CAAC,CAAC;;UAE1B;UACAhB,mBAAmB,CAAC,CAAC,CAAC;UACtB;UACA;QACF;QAEA,MAAM8J,QAAQ,IAAAiB,WAAA,GAAGpJ,IAAI,CAAC0D,KAAK,cAAA0F,WAAA,cAAAA,WAAA,GAAI,EAAE;QACjC,MAAMhB,UAAU,GAAGD,QAAQ,CAACE,MAAM,CAAEC,CAAC,IAAK;UACxC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;YACzB,OAAOA,CAAC,CAAC7F,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC6F,CAAC,CAACpB,WAAW,CAAC,CAAC,CAACqB,UAAU,CAAC,MAAM,CAAC;UAC/D;UACA,IAAI,OAAOD,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,CAACE,QAAQ,EAAE;YACrD,OACEF,CAAC,CAACE,QAAQ,CAAC/F,IAAI,CAAC,CAAC,KAAK,EAAE,IACxB,CAAC6F,CAAC,CAACE,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAACqB,UAAU,CAAC,MAAM,CAAC;UAEhD;UACA,OAAO,KAAK;QACd,CAAC,CAAC;QAEF,IAAIH,UAAU,CAACvF,MAAM,GAAG,CAAC,EAAE;UACzB3G,eAAe,CACbkM,UAAU,CAAC5E,GAAG,CAAE8E,CAAC,IAAK;YACpB,MAAME,QAAQ,GAAG,OAAOF,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGA,CAAC,CAACE,QAAQ;YACvD,MAAMlF,GAAG,GACP,OAAOgF,CAAC,KAAK,QAAQ,IAAI,CAACA,CAAC,CAAChF,GAAG,IAAI,CAACgF,CAAC,CAAChF,GAAG,CAACiF,UAAU,CAAC,MAAM,CAAC,GACxD,GAAGzN,WAAW,cAAc2N,kBAAkB,CAACD,QAAQ,CAAC,UAAUtN,WAAW,EAAE,GAC/E,GAAGoN,CAAC,CAAChF,GAAG,GAAGgF,CAAC,CAAChF,GAAG,CAAC6C,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,SAASjL,WAAW,EAAE;YACtE,OAAO;cACLwN,WAAW,EAAEF,QAAQ;cACrBlF,GAAG,EAAEA;YACP,CAAC;UACH,CAAC,CACH,CAAC;UACD,IAAI,CAAC2E,UAAU,CAACf,WAAW,CAAC,CAAC,CAACf,QAAQ,CAAC,8BAA8B,CAAC,EAAE;YACtE8B,UAAU,IAAI,qEAAqE;UACrF;QACF,CAAC,MAAM;UACL/L,eAAe,CAAC,IAAI,CAAC;QACvB;QAEAV,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACE7B,EAAE,EAAEI,IAAI,CAAC0C,GAAG,CAAC,CAAC;UACd7C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAEqG,UAAU;UACnBpG,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;;QAEF;QACA,IAAIzE,YAAY,IAAIiB,UAAU,KAAK,CAAC,EAAE;UACpCD,mBAAmB,CAAEkF,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;QACzC;QACA;;QAEA,IAAIvD,IAAI,CAACmJ,KAAK,KAAK,aAAa,EAAE;UAChCvM,4BAA4B,CAAC,IAAI,CAAC;QACpC;MACF,CAAC,MAAM;QACLd,QAAQ,CAACkE,IAAI,CAACnE,KAAK,IAAI,0BAA0B,CAAC;MACpD;IACF,CAAC,CAAC,OAAOsE,GAAG,EAAE;MACZrE,QAAQ,CAAC,iBAAiB,GAAGqE,GAAG,CAAC4B,OAAO,CAAC;IAC3C,CAAC,SAAS;MACRnG,UAAU,CAAC,KAAK,CAAC;IACnB;EACF;;EAGE;EACA,SAASwI,YAAYA,CAAA,EAAG;IACtBjJ,YAAY,CAAC6G,UAAU,CAAC,QAAQ,CAAC;IACjC7G,YAAY,CAAC6G,UAAU,CAAC,SAAS,CAAC;IAClC7G,YAAY,CAAC6G,UAAU,CAAC,UAAU,CAAC;IACnCzB,MAAM,CAACC,QAAQ,CAACyB,IAAI,GAAG,OAAO;EAChC;;EAEA;EACA,MAAMoH,UAAU,GAAIxH,SAAS,IAAK;IAChC,OAAOA,SAAS,CAACyH,kBAAkB,CAAC,EAAE,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC,CAAC;EACjF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KACrBlO,QAAQ,CAACiI,GAAG,CAAEzB,OAAO,iBACnBlH,OAAA;IAEE6O,SAAS,EAAE,WAAW3H,OAAO,CAACJ,IAAI,EAAG;IACrCgI,KAAK,EAAE;MAAEC,SAAS,EAAE7H,OAAO,CAACJ,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG;IAAO,CAAE;IACjE,aAAU,QAAQ;IAAAkI,QAAA,eAElBhP,OAAA;MACE6O,SAAS,EAAC,iBAAiB;MAC3BC,KAAK,EAAE;QACLG,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,KAAK;QACfC,OAAO,EAAE,UAAU;QACnBC,YAAY,EAAE,MAAM;QACpBC,eAAe,EAAEnI,OAAO,CAACJ,IAAI,KAAK,MAAM,GAAG,SAAS,GAAG;MACzD,CAAE;MAAAkI,QAAA,gBAEFhP,OAAA;QACE6O,SAAS,EAAC,cAAc;QACxBC,KAAK,EAAE;UACLQ,UAAU,EAAE,UAAU;UACtBC,SAAS,EAAE,MAAM;UACjBC,SAAS,EAAE,OAAO;UAClBC,QAAQ,EAAE,KAAK;UACfC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,GAED9H,OAAO,CAACH,OAAO,CAAC4I,KAAK,CAAC,IAAI,CAAC,CAAChH,GAAG,CAAC,CAACiH,IAAI,EAAEhD,GAAG,KAAK;UAC9C,MAAMiD,KAAK,GAAG,EAAE;UAChB,IAAIC,SAAS,GAAGF,IAAI;UACpB,IAAIG,QAAQ,GAAG,CAAC;UAEhB,OAAOD,SAAS,CAAC9H,MAAM,GAAG,CAAC,EAAE;YAC3B,MAAMgI,SAAS,GAAGF,SAAS,CAACG,KAAK,CAAC,wBAAwB,CAAC;YAC3D,MAAMC,SAAS,GAAGJ,SAAS,CAACG,KAAK,CAAC,eAAe,CAAC;YAElD,IAAID,SAAS,KAAK,CAACE,SAAS,IAAIF,SAAS,CAACvD,KAAK,GAAGyD,SAAS,CAACzD,KAAK,CAAC,EAAE;cAClE,IAAIrF,IAAI,GAAG4I,SAAS,CAAC,CAAC,CAAC;cACvB,IAAI,CAAC5I,IAAI,CAACsG,UAAU,CAAC,MAAM,CAAC,EAAE;gBAC5BtG,IAAI,GAAG,GAAGnH,WAAW,GAAGmH,IAAI,CAACsG,UAAU,CAAC,GAAG,CAAC,GAAGtG,IAAI,GAAG,GAAG,GAAGA,IAAI,EAAE;cACpE;cACA,IACE,CAACA,IAAI,CAACsG,UAAU,CAAC,GAAGzN,WAAW,aAAa,CAAC,IAAImH,IAAI,CAACsG,UAAU,CAAC,aAAa,CAAC,KAC/ErN,WAAW,IACX,CAAC+G,IAAI,CAACkE,QAAQ,CAAC,QAAQ,CAAC,EACxB;gBACAlE,IAAI,IAAIA,IAAI,CAACkE,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAUjL,WAAW,EAAE,GAAG,UAAUA,WAAW,EAAE;cAChF;cACAwP,KAAK,CAAChI,IAAI,cACR7H,OAAA;gBAAAgP,QAAA,GACGc,SAAS,CAACK,KAAK,CAAC,CAAC,EAAEH,SAAS,CAACvD,KAAK,CAAC,eACpCzM,OAAA;kBACEoH,IAAI,EAAEA,IAAK;kBACX+C,MAAM,EAAC,QAAQ;kBACfiG,GAAG,EAAC,qBAAqB;kBACzBtB,KAAK,EAAE;oBAAEuB,KAAK,EAAE,SAAS;oBAAEC,cAAc,EAAE;kBAAY,CAAE;kBAAAtB,QAAA,EAExDgB,SAAS,CAAC,CAAC;gBAAC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC;cAAA,GATKX,QAAQ,EAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUf,CACR,CAAC;cACDZ,SAAS,GAAGA,SAAS,CAACK,KAAK,CAACH,SAAS,CAACvD,KAAK,GAAGuD,SAAS,CAAC,CAAC,CAAC,CAAChI,MAAM,CAAC;YACpE,CAAC,MAAM,IAAIkI,SAAS,EAAE;cACpBL,KAAK,CAAChI,IAAI,cACR7H,OAAA;gBAAAgP,QAAA,GACGc,SAAS,CAACK,KAAK,CAAC,CAAC,EAAED,SAAS,CAACzD,KAAK,CAAC,eACpCzM,OAAA;kBAAAgP,QAAA,EAASkB,SAAS,CAAC,CAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA,GAFtBX,QAAQ,EAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGf,CACR,CAAC;cACDZ,SAAS,GAAGA,SAAS,CAACK,KAAK,CAACD,SAAS,CAACzD,KAAK,GAAGyD,SAAS,CAAC,CAAC,CAAC,CAAClI,MAAM,CAAC;YACpE,CAAC,MAAM;cACL6H,KAAK,CAAChI,IAAI,cAAC7H,OAAA;gBAAAgP,QAAA,EAAwBc;cAAS,GAAtBC,QAAQ,EAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAmB,CAAC,CAAC;cACrD;YACF;UACF;UAEA,oBAAO1Q,OAAA;YAAAgP,QAAA,EAAgBa;UAAK,GAAXjD,GAAG;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QACrC,CAAC,CAAC,EACDxJ,OAAO,CAAC/D,OAAO,iBACdnD,OAAA;UAAK8O,KAAK,EAAE;YAAE6B,SAAS,EAAE;UAAM,CAAE;UAAA3B,QAAA,EAC9B9H,OAAO,CAAC/D,OAAO,CAACwF,GAAG,CAAC,CAACnB,MAAM,EAAEoF,GAAG,kBAC/B5M,OAAA;YAEE8O,KAAK,EAAE;cACL8B,MAAM,EAAE,SAAS;cACjBzB,OAAO,EAAE,SAAS;cAClB0B,YAAY,EACVjE,GAAG,KAAK1F,OAAO,CAAC/D,OAAO,CAAC6E,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG;YAC5D,CAAE;YACF8I,OAAO,EAAEA,CAAA,KAAMhF,kBAAkB,CAACtE,MAAM,CAACxB,YAAY,CAAE;YAAAgJ,QAAA,GAEtDxH,MAAM,CAACiF,KAAK,EAAC,IAAE,eAAAzM,OAAA;cAAAgP,QAAA,EAASxH,MAAM,CAACxB;YAAY;cAAAuK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,YAAG,EAAClJ,MAAM,CAACa,KAAK;UAAA,GATjEb,MAAM,CAACxB,YAAY;YAAAuK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUrB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACN1Q,OAAA;QACE6O,SAAS,EAAC,cAAc;QACxBC,KAAK,EAAE;UAAEW,QAAQ,EAAE,OAAO;UAAEY,KAAK,EAAE,MAAM;UAAEM,SAAS,EAAE;QAAM,CAAE;QAAA3B,QAAA,EAE7DR,UAAU,CAACtH,OAAO,CAACF,SAAS;MAAC;QAAAuJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC,GArGDxJ,OAAO,CAACL,EAAE;IAAA0J,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAsGZ,CACN,CAAC;EAEJ,oBACE1Q,OAAA;IAAK6O,SAAS,EAAC,gBAAgB;IAACkC,IAAI,EAAC,MAAM;IAAC,cAAW,kBAAkB;IAAA/B,QAAA,gBACvEhP,OAAA;MAAK6O,SAAS,EAAC,aAAa;MAACC,KAAK,EAAE;QAAEkC,QAAQ,EAAE;MAAW,CAAE;MAAAhC,QAAA,gBAC3DhP,OAAA;QAAAgP,QAAA,EAAI;MAAkC;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3C1Q,OAAA;QAAG6O,SAAS,EAAC,UAAU;QAAAG,QAAA,EAAC;MAAiC;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,EAC5DhP,QAAQ,iBACP1B,OAAA;QAAG8O,KAAK,EAAE;UAAEW,QAAQ,EAAE,OAAO;UAAEY,KAAK,EAAE;QAAO,CAAE;QAAArB,QAAA,GAAC,gBAChC,eAAAhP,OAAA;UAAAgP,QAAA,EAAStN;QAAQ;UAAA6O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CACJ,eACD1Q,OAAA;QAAK8O,KAAK,EAAE;UAAEkC,QAAQ,EAAE,UAAU;UAAEC,GAAG,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAG,CAAE;QAAAlC,QAAA,eACvDhP,OAAA;UACE8Q,OAAO,EAAEvH,YAAa;UACtBuF,KAAK,EAAE;YACLK,OAAO,EAAE,UAAU;YACnByB,MAAM,EAAE,SAAS;YACjBvB,eAAe,EAAE,SAAS;YAC1B8B,MAAM,EAAE,MAAM;YACd/B,YAAY,EAAE,CAAC;YACfiB,KAAK,EAAE,OAAO;YACde,UAAU,EAAE;UACd,CAAE;UACF,cAAW,QAAQ;UACnB/I,KAAK,EAAC,QAAQ;UAAA2G,QAAA,EACf;QAED;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1Q,OAAA;MACE6O,SAAS,EAAC,eAAe;MACzB,aAAU,QAAQ;MAClB,iBAAc,WAAW;MACzBC,KAAK,EAAE;QACLU,SAAS,EAAE,MAAM;QACjBD,SAAS,EAAE,MAAM;QACjBJ,OAAO,EAAE,MAAM;QACfkC,cAAc,EAAE;MAClB,CAAE;MAAArC,QAAA,GAEDJ,cAAc,CAAC,CAAC,EAChB,CAAC9N,OAAO,IAAIU,SAAS,kBACpBxB,OAAA;QAAK6O,SAAS,EAAC,oBAAoB;QAAC,cAAW,WAAW;QAAAG,QAAA,eACxDhP,OAAA;UAAK6O,SAAS,EAAC,iBAAiB;UAAAG,QAAA,gBAC9BhP,OAAA;YAAK6O,SAAS,EAAC,kBAAkB;YAAC,eAAY,MAAM;YAAAG,QAAA,gBAClDhP,OAAA;cAAAuQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb1Q,OAAA;cAAAuQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb1Q,OAAA;cAAAuQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN1Q,OAAA;YAAK6O,SAAS,EAAC,cAAc;YAAAG,QAAA,EAC1BxN,SAAS,GAAG,2BAA2B,GAAG;UAAc;YAAA+O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAtP,YAAY,IAAIA,YAAY,CAAC4G,MAAM,GAAG,CAAC,iBACtChI,OAAA;QAAK8O,KAAK,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEJ,SAAS,EAAE;QAAS,CAAE;QAAAC,QAAA,eACnDhP,OAAA,CAACJ,YAAY;UACX0R,KAAK,EAAEA,CAAA,KAAM9H,mBAAmB,CAAC,IAAI,EAAE,eAAe,CAAE;UACxD+H,IAAI,EAAEA,CAAA,KAAM/H,mBAAmB,CAAC,KAAK,EAAE,eAAe,CAAE;UACxDgI,OAAO,EAAC,eAAe;UACvBC,MAAM,EAAC;QAAM;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGA1O,oBAAoB,iBACnBhC,OAAA;QAAK8O,KAAK,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEJ,SAAS,EAAE;QAAS,CAAE;QAAAC,QAAA,eACnDhP,OAAA,CAACJ,YAAY;UACX0R,KAAK,EAAEA,CAAA,KAAM9H,mBAAmB,CAAC,IAAI,EAAE,cAAc,CAAE;UACvD+H,IAAI,EAAEA,CAAA,KAAM/H,mBAAmB,CAAC,KAAK,EAAE,cAAc,CAAE;UACvDgI,OAAO,EAAC,KAAK;UACbC,MAAM,EAAC;QAAI;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGA5O,yBAAyB,iBACxB9B,OAAA;QAAK8O,KAAK,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEJ,SAAS,EAAE;QAAS,CAAE;QAAAC,QAAA,eACnDhP,OAAA,CAACJ,YAAY;UACX0R,KAAK,EAAEA,CAAA,KAAM9H,mBAAmB,CAAC,IAAI,EAAE,cAAc,CAAE;UACvD+H,IAAI,EAAEA,CAAA,KAAM/H,mBAAmB,CAAC,KAAK,EAAE,cAAc,CAAE;UACvDgI,OAAO,EAAC,cAAc;UACtBC,MAAM,EAAC;QAAW;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGA5N,8BAA8B,iBAC7B9C,OAAA;QAAK8O,KAAK,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEJ,SAAS,EAAE;QAAS,CAAE;QAAAC,QAAA,eACnDhP,OAAA,CAACJ,YAAY;UACX0R,KAAK,EAAEA,CAAA,KAAM9H,mBAAmB,CAAC,IAAI,EAAE,iBAAiB,CAAE;UAC1D+H,IAAI,EAAEA,CAAA,KAAM/H,mBAAmB,CAAC,KAAK,EAAE,iBAAiB,CAAE;UAC1DgI,OAAO,EAAC,mBAAmB;UAC3BC,MAAM,EAAC;QAAkB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,eAED1Q,OAAA;QAAK0R,GAAG,EAAE/M;MAAe;QAAA4L,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,EAEL1P,KAAK,iBACJhB,OAAA;MACE6O,SAAS,EAAC,eAAe;MACzBiC,OAAO,EAAEA,CAAA,KAAM7P,QAAQ,CAAC,EAAE,CAAE;MAC5B6N,KAAK,EAAE;QAAE8B,MAAM,EAAE;MAAU,CAAE;MAC7BG,IAAI,EAAC,OAAO;MACZ,aAAU,WAAW;MACrBY,QAAQ,EAAE,CAAE;MAAA3C,QAAA,GAEXhO,KAAK,EAAC,qBACT;IAAA;MAAAuP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,EAGA1G,eAAe,CAAC,CAAC,iBAChBhK,OAAA;MAAK8O,KAAK,EAAE;QACVO,eAAe,EAAE,SAAS;QAC1BgB,KAAK,EAAE,SAAS;QAChBlB,OAAO,EAAE,MAAM;QACfC,YAAY,EAAE,KAAK;QACnBwC,MAAM,EAAE,QAAQ;QAChB7C,SAAS,EAAE,QAAQ;QACnBoC,MAAM,EAAE,mBAAmB;QAC3B1B,QAAQ,EAAE,MAAM;QAChB2B,UAAU,EAAE;MACd,CAAE;MAAApC,QAAA,EAAC;IAEH;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,eAED1Q,OAAA;MAAM6O,SAAS,EAAC,iBAAiB;MAACgD,QAAQ,EAAE3F,YAAa;MAAC,cAAW,mBAAmB;MAAA8C,QAAA,eACtFhP,OAAA;QAAK6O,SAAS,EAAC,iBAAiB;QAACC,KAAK,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAE6C,UAAU,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAA/C,QAAA,gBAC/FhP,OAAA;UACE8G,IAAI,EAAC,MAAM;UACXsD,KAAK,EAAE5J,KAAM;UACbwR,QAAQ,EAAE/H,aAAc;UACxBgI,WAAW,EACT3Q,WAAW,GACPmC,UAAU,GAAG,CAAC,GACZe,eAAe,CAACf,UAAU,GAAG,CAAC,CAAC,GAC/BuG,eAAe,CAAC,CAAC,GACjB,4CAA4C,GAC5C,4BAA4B,GAC9B,iCACL;UACDkI,QAAQ,EAAEpR,OAAO,IAAIU,SAAS,IAAKiC,UAAU,KAAK,CAAC,IAAI,CAACjD,KAAM,IAAIwJ,eAAe,CAAC,CAAE;UACpFmI,SAAS;UACT,cAAW,YAAY;UACvBrD,KAAK,EAAE;YACLsD,IAAI,EAAE,GAAG;YACTL,KAAK,EAAE,MAAM;YACb5C,OAAO,EAAE,KAAK;YACdC,YAAY,EAAE,aAAa;YAC3B+B,MAAM,EAAE,gBAAgB;YACxBS,MAAM,EAAE,CAAC;YACTvC,eAAe,EAAErF,eAAe,CAAC,CAAC,GAAG,SAAS,GAAG,OAAO;YACxDqG,KAAK,EAAErG,eAAe,CAAC,CAAC,GAAG,MAAM,GAAG,OAAO;YAC3C4G,MAAM,EAAE5G,eAAe,CAAC,CAAC,GAAG,aAAa,GAAG;UAC9C,CAAE;UACFqI,SAAS,EAAGnI,CAAC,IAAK;YAChB,IAAIA,CAAC,CAACoI,GAAG,KAAK,OAAO,IAAI,CAACpI,CAAC,CAACqI,QAAQ,EAAE;cACpCrI,CAAC,CAACiC,cAAc,CAAC,CAAC;cAClBD,YAAY,CAAChC,CAAC,CAAC;YACjB;UACF;QAAE;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACDjN,UAAU,KAAK,CAAC,iBACfzD,OAAA;UACEoK,KAAK,EAAEzG,UAAU,CAACE,WAAY;UAC9BmO,QAAQ,EAAE3H,uBAAwB;UAClCyE,KAAK,EAAE;YACL0D,UAAU,EAAE,GAAG;YACfrD,OAAO,EAAE,KAAK;YACdC,YAAY,EAAE,GAAG;YACjB+B,MAAM,EAAE,gBAAgB;YACxBsB,UAAU,EAAE,MAAM;YAClBhD,QAAQ,EAAE;UACZ,CAAE;UACF,cAAW,qBAAqB;UAAAT,QAAA,gBAEhChP,OAAA;YAAQoK,KAAK,EAAC,EAAE;YAAC8H,QAAQ;YAAAlD,QAAA,EAAC;UAE1B;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACRjM,kBAAkB,CAACkE,GAAG,CAAE+J,MAAM,iBAC7B1S,OAAA;YAAqBoK,KAAK,EAAEsI,MAAO;YAAA1D,QAAA,EAChC0D;UAAM,GADIA,MAAM;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEX,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACT,eACD1Q,OAAA;UACE8G,IAAI,EAAC,QAAQ;UACboL,QAAQ,EAAEpR,OAAO,IAAI,CAACN,KAAK,CAACoH,IAAI,CAAC,CAAC,IAAIpG,SAAS,IAAKiC,UAAU,KAAK,CAAC,IAAI,CAACE,UAAU,CAACE,WAAa;UACjGwE,KAAK,EAAEvH,OAAO,IAAIU,SAAS,GAAG,gBAAgB,GAAG,MAAO;UACxD,cAAW,cAAc;UACzBsN,KAAK,EAAE;YAAEK,OAAO,EAAE,UAAU;YAAEC,YAAY,EAAE,aAAa;YAAE+B,MAAM,EAAE,gBAAgB;YAAEsB,UAAU,EAAE,MAAM;YAAEpD,eAAe,EAAE,SAAS;YAAEgB,KAAK,EAAE;UAAQ,CAAE;UAAArB,QAAA,EAErJlO,OAAO,IAAIU,SAAS,gBAAGxB,OAAA;YAAM6O,SAAS,EAAC;UAAS;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAI;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACtQ,EAAA,CAnpDuBF,IAAI;AAAAyS,EAAA,GAAJzS,IAAI;AAAA,IAAAyS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}