{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AI-Agent-Chatbot-main\\\\frontend\\\\src\\\\Home.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport \"./App.css\";\n\n// Define the backend URL\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BACKEND_URL = \"http://localhost:8000\";\nexport default function Home({\n  token\n}) {\n  _s();\n  const accessToken = token || localStorage.getItem(\"access\");\n\n  // --- States ---\n  const [query, setQuery] = useState(\"\");\n  const [messages, setMessages] = useState([]);\n  const [promptTemplate, setPromptTemplate] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n  const [mode, setMode] = useState(\"strict\");\n  const [pendingFiles, setPendingFiles] = useState(null);\n  const [orgVerified, setOrgVerified] = useState(false);\n  const [verifying, setVerifying] = useState(false);\n  const [username, setUserName] = useState(\"\");\n  const [askRaiseTicket, setAskRaiseTicket] = useState(false);\n  const [awaitingCloseConfirmation, setAwaitingCloseConfirmation] = useState(false);\n  const [awaitingOtherQueries, setAwaitingOtherQueries] = useState(false);\n  const [pendingTickets, setPendingTickets] = useState([]);\n  const [awaitingPendingChoice, setAwaitingPendingChoice] = useState(false);\n  const [awaitingTicketSelect, setAwaitingTicketSelect] = useState(false);\n  const [activeTicket, setActiveTicket] = useState(null);\n  const [ticketRefused, setTicketRefused] = useState(false);\n  const [queriesAfterNoTicket, setQueriesAfterNoTicket] = useState(0);\n  const [awaitingUnrelatedQueryResponse, setAwaitingUnrelatedQueryResponse] = useState(false);\n  const MAX_QUERIES_AFTER_NO_TICKET = 10;\n  const [tickets, setTickets] = useState([]);\n  const [showTickets, setShowTickets] = useState(false);\n  const [ticketQueryCount, setTicketQueryCount] = useState(0);\n\n  // --- New ticket states ---\n  const [ticketStep, setTicketStep] = useState(0);\n  const [ticketData, setTicketData] = useState({\n    productType: \"\",\n    purchasedFrom: \"\",\n    yearOfPurchase: \"\",\n    productName: \"\",\n    model: \"\",\n    serialNo: \"\",\n    operatingSystem: \"\"\n  });\n  const [awaitingProblemDescription, setAwaitingProblemDescription] = useState(false);\n  const [currentTicketNumber, setCurrentTicketNumber] = useState(null);\n  const ticketQuestions = [\"Please select the product type using the dropdown below:\", \"Please enter the 'Purchased From' information:\", \"Please enter the 'Year of Purchase':\", \"Please enter the 'Product Name':\", \"Please enter the 'Model':\", \"Please enter the 'Serial Number':\", \"Please enter the 'Operating System':\"];\n  const productTypeOptions = [\"Camera\", \"Frame Grabber\", \"Accessories\", \"Software\"];\n\n  // Auto-scroll messages\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  }, [messages, loading, error]);\n  const messagesEndRef = useRef(null);\n\n  // Fetch prompt template\n  useEffect(() => {\n    fetch(`${BACKEND_URL}/api/prompt/?type=chat`).then(res => res.json()).then(data => setPromptTemplate(data.template)).catch(err => {\n      console.error(\"Failed to fetch prompt template:\", err);\n      setPromptTemplate(null);\n    });\n  }, []);\n\n  // Check for pending mode from URL\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const isPendingMode = urlParams.get('mode') === 'pending';\n    if (isPendingMode) {\n      // Skip organization verification and directly load pending ticket\n      loadPendingTicketDirectly();\n    }\n  }, []);\n\n  // Fetch username and welcome message\n  useEffect(() => {\n    if (!accessToken) return;\n    const urlParams = new URLSearchParams(window.location.search);\n    const isPendingMode = urlParams.get('mode') === 'pending';\n    if (isPendingMode) {\n      return; // Skip normal flow for pending mode\n    }\n    fetch(`${BACKEND_URL}/api/user_info/`, {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${accessToken}`\n      }\n    }).then(async res => {\n      if (!res.ok) {\n        const errorText = await res.text();\n        console.error(\"User info fetch failed:\", res.status, errorText);\n        throw new Error(\"User info fetch failed\");\n      }\n      return res.json();\n    }).then(data => {\n      const name = data.name || data.username || data.email;\n      if (!name) throw new Error(\"Name missing in response\");\n      setUserName(name);\n      setMessages([{\n        id: 1,\n        type: \"bot\",\n        content: `👋 Welcome, ${name}! Please enter your organization name to verify your account.`,\n        timestamp: new Date()\n      }]);\n    }).catch(err => {\n      console.error(\"Failed to fetch user info:\", err.message);\n      localStorage.removeItem(\"access\");\n      window.location.href = \"/auth\";\n    });\n  }, [accessToken]);\n\n  // Input change handler\n  const onInputChange = e => {\n    if (error) setError(\"\");\n    setQuery(e.target.value);\n  };\n\n  // Dropdown change handler for productType\n  const handleProductTypeChange = e => {\n    const selectedType = e.target.value;\n    setTicketData({\n      ...ticketData,\n      productType: selectedType\n    });\n    setMessages(prev => [...prev, {\n      id: Date.now(),\n      type: \"user\",\n      content: `Selected product type: ${selectedType}`,\n      timestamp: new Date()\n    }]);\n    setTicketStep(ticketStep + 1);\n    addBot(ticketQuestions[ticketStep]);\n  };\n\n  // Verify organization name\n  const verifyOrganization = async orgName => {\n    setVerifying(true);\n    setError(\"\");\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/verify_organization/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        },\n        body: JSON.stringify({\n          organization: orgName\n        })\n      });\n      const data = await response.json();\n      if (response.ok && data.status === \"verified\") {\n        setOrgVerified(true);\n        setUserName(data.name || username);\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: data.message || \"✅ Organization verified.\",\n          timestamp: new Date()\n        }]);\n        await fetchPendingTickets();\n      } else {\n        const baseUrl = window.location.origin;\n        const signupLink = `[sign up here](${baseUrl}/signup/)`;\n        setError(data.message || \"❌ Organization mismatch.\");\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: `❌ Organization verification failed.\\n\\nIf you belong to a new organization, please ${signupLink} to register first.`,\n          timestamp: new Date()\n        }]);\n        localStorage.removeItem(\"access\");\n      }\n    } catch (err) {\n      setError(\"Network error during verification.\");\n      setMessages(prev => [...prev, {\n        id: Date.now(),\n        type: \"bot\",\n        content: \"❌ Network error during organization verification. Please try again.\",\n        timestamp: new Date()\n      }]);\n      console.error(\"Verification error:\", err);\n    } finally {\n      setVerifying(false);\n    }\n  };\n\n  // Helper function to fetch pending tickets after verification\n  async function fetchPendingTickets() {\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {\n        headers: {\n          Authorization: `Bearer ${accessToken}`\n        }\n      });\n      const data = await response.json();\n      if (response.ok) {\n        setPendingTickets(data.tickets || []);\n        if (data.tickets && data.tickets.length > 0) {\n          setAwaitingPendingChoice(true);\n          addBot(`You have ${data.tickets.length} pending ticket(s). Do you want to continue with any of them? (yes/no)`);\n        } else {\n          setAskRaiseTicket(true);\n          addBot(\"No pending tickets found. Would you like to raise a support ticket? (yes/no)\");\n        }\n      } else {\n        setPendingTickets([]);\n        setAskRaiseTicket(true);\n        addBot(\"Could not fetch pending tickets. Would you like to raise a support ticket? ( Juno/no)\");\n      }\n    } catch (err) {\n      console.error(\"Error fetching pending tickets:\", err);\n      setPendingTickets([]);\n      setAskRaiseTicket(true);\n      addBot(\"Error fetching pending tickets. Would you like to raise a support ticket? (yes/no)\");\n    }\n  }\n\n  // Submit ticket with latest data\n  const submitTicket = async finalTicketData => {\n    setLoading(true);\n    setError(\"\");\n    const validProductTypes = [\"Camera\", \"Frame Grabber\", \"Accessories\", \"Software\"];\n    const allFilled = Object.values(finalTicketData).every(v => v && v.trim() !== \"\");\n    if (!allFilled) {\n      setMessages(prev => [...prev, {\n        id: Date.now(),\n        type: \"bot\",\n        content: \"❌ Please fill in all required fields before submitting the ticket.\",\n        timestamp: new Date()\n      }]);\n      setLoading(false);\n      return;\n    }\n    if (!validProductTypes.includes(finalTicketData.productType)) {\n      setMessages(prev => [...prev, {\n        id: Date.now(),\n        type: \"bot\",\n        content: \"❌ Invalid product type. Please select: Camera, Frame Grabber, Accessories, or Software.\",\n        timestamp: new Date()\n      }]);\n      setLoading(false);\n      return;\n    }\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        },\n        body: JSON.stringify({\n          product_type: finalTicketData.productType,\n          purchased_from: finalTicketData.purchasedFrom,\n          year_of_purchase: finalTicketData.yearOfPurchase,\n          product_name: finalTicketData.productName,\n          model: finalTicketData.model,\n          serial_no: finalTicketData.serialNo,\n          operating_system: finalTicketData.operatingSystem\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        setCurrentTicketNumber(data.ticket_number);\n        setActiveTicket(data.ticket_number);\n        setAwaitingProblemDescription(true);\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: `🎉 Thank you! Your support ticket has been created successfully. Your ticket number is **${data.ticket_number}**.\\n\\nPlease describe your problem so we can assist you.`,\n          timestamp: new Date()\n        }]);\n      } else {\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: `❌ Failed to create ticket: ${JSON.stringify(data.errors || data.message)}`,\n          timestamp: new Date()\n        }]);\n      }\n    } catch (err) {\n      setMessages(prev => [...prev, {\n        id: Date.now(),\n        type: \"bot\",\n        content: `❌ Network error while creating ticket: ${err.message}`,\n        timestamp: new Date()\n      }]);\n    } finally {\n      setLoading(false);\n      setTicketStep(0);\n      setAskRaiseTicket(false);\n      setTicketData({\n        productType: \"\",\n        purchasedFrom: \"\",\n        yearOfPurchase: \"\",\n        productName: \"\",\n        model: \"\",\n        serialNo: \"\",\n        operatingSystem: \"\"\n      });\n    }\n  };\n\n  // Helper to add a bot message\n  function addBot(text) {\n    setMessages(prev => [...prev, {\n      id: Date.now(),\n      type: \"bot\",\n      content: text,\n      timestamp: new Date()\n    }]);\n  }\n\n  // Handle ticket selection from UI\n  const handleTicketSelect = async ticketNumber => {\n    setAwaitingTicketSelect(false);\n    setActiveTicket(ticketNumber);\n    setCurrentTicketNumber(ticketNumber);\n    setShowTickets(false);\n    try {\n      const summaryRes = await fetch(`${BACKEND_URL}/api/ticket_summary/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        },\n        body: JSON.stringify({\n          ticket_number: ticketNumber\n        })\n      });\n      const summaryData = await summaryRes.json();\n      if (summaryRes.ok) {\n        addBot(`🔄 Resuming ticket **${ticketNumber}** …\\n\\n` + `📝 **Raised problem:** ${summaryData.problem_summary || summaryData.problem_description}\\n\\n` + `💡 **Given solution:** ${summaryData.solution_summary || \"No solution yet.\"}\\n\\n` + \"✅ You can ask your follow-up query now.\");\n      } else {\n        addBot(\"⚠️ Error fetching ticket summary.\");\n      }\n    } catch (err) {\n      addBot(\"❌ Network error while fetching ticket summary.\");\n    }\n  };\n\n  // FULLY UPDATED handleSubmit\n  async function handleSubmit(e) {\n    e.preventDefault();\n    if (!query.trim() || loading || verifying) return;\n    const currentQuery = query.trim().toLowerCase();\n\n    // --- New: Check 5 queries per ticket limit ---\n    if (activeTicket &&\n    // There is an active ticket\n    ticketStep === 0 &&\n    // Not in ticket creation steps\n    !awaitingProblemDescription &&\n    // Not waiting for problem description input\n    ticketQueryCount >= 5 // Limit reached\n    ) {\n      addBot(\"🛑 You have reached the maximum of five queries for this ticket. It has been automatically escalated to ensure prompt resolution. Kindly create a new ticket for any further inquiries or await our team’s response.\");\n      setQuery(\"\");\n      return;\n    }\n    // ---------------------------------------------\n\n    if (ticketRefused) {\n      if (queriesAfterNoTicket >= MAX_QUERIES_AFTER_NO_TICKET) {\n        addBot(\"⚠️ You have reached the maximum number of free queries. Please raise a support ticket for further assistance.\");\n        setQuery(\"\");\n        return;\n      } else {\n        setQueriesAfterNoTicket(n => n + 1);\n      }\n    }\n    const userMsg = {\n      id: Date.now(),\n      type: \"user\",\n      content: query.trim(),\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, userMsg]);\n    setQuery(\"\");\n    setError(\"\");\n    if (awaitingPendingChoice) {\n      setAwaitingPendingChoice(false);\n      if (currentQuery === \"yes\") {\n        setAwaitingTicketSelect(true);\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: `Select a ticket by typing its number:`,\n          timestamp: new Date(),\n          tickets: pendingTickets.map((t, i) => ({\n            index: i + 1,\n            ticketNumber: t.ticket_number,\n            title: t.title || t.short_title || \"No title\"\n          }))\n        }]);\n      } else if (currentQuery === \"no\") {\n        setAskRaiseTicket(true);\n        addBot(\"Do you want to raise a support ticket? (yes/no)\");\n      } else {\n        addBot(\"Please answer 'yes' or 'no'. Do you want to continue an open ticket?\");\n        setAwaitingPendingChoice(true);\n      }\n      return;\n    }\n    if (awaitingTicketSelect) {\n      const picked = pendingTickets.find((t, idx) => currentQuery === String(idx + 1) || currentQuery.includes(t.ticket_number.toLowerCase()));\n      if (!picked) {\n        addBot(\"Ticket not recognised, please type its number.\");\n        return;\n      }\n      await handleTicketSelect(picked.ticket_number);\n      return;\n    }\n    if (!orgVerified) {\n      await verifyOrganization(currentQuery);\n      return;\n    }\n    if (askRaiseTicket) {\n      if (currentQuery === \"yes\") {\n        setAskRaiseTicket(false);\n        setTicketStep(1);\n        addBot(ticketQuestions[0]);\n        setTicketRefused(false);\n        setQueriesAfterNoTicket(0);\n\n        // --- New: Reset query count when new ticket starts ---\n        setTicketQueryCount(0);\n        // ------------------------------------------------------\n      } else if (currentQuery === \"no\") {\n        setAskRaiseTicket(false);\n        addBot(\"👍 Okay, no ticket will be raised. How else can I help you?\");\n        setTicketRefused(true);\n        setQueriesAfterNoTicket(0);\n      } else {\n        addBot(\"Please answer 'yes' or 'no'. Do you want to raise a support ticket?\");\n      }\n      return;\n    }\n    if (awaitingUnrelatedQueryResponse) {\n      setAwaitingUnrelatedQueryResponse(false);\n      if (currentQuery === \"yes\") {\n        setTicketStep(1);\n        setActiveTicket(null);\n        setCurrentTicketNumber(null);\n        addBot(ticketQuestions[0]);\n\n        // --- New: Reset query count when new ticket starts here too ---\n        setTicketQueryCount(0);\n        // ---------------------------------------------------------------\n      } else if (currentQuery === \"no\") {\n        setAwaitingCloseConfirmation(true);\n        addBot(\"Can I close this ticket now? (yes/no)\");\n      } else {\n        setAwaitingUnrelatedQueryResponse(true);\n        addBot(\"Please answer 'yes' or 'no'. Do you want to create a new ticket?\");\n      }\n      return;\n    }\n    if (ticketStep > 0 && ticketStep <= ticketQuestions.length) {\n      if (ticketStep === 1) {\n        const selectedType = query.trim();\n        if (productTypeOptions.includes(selectedType)) {\n          setTicketData({\n            ...ticketData,\n            productType: selectedType\n          });\n          setMessages(prev => [...prev, {\n            id: Date.now(),\n            type: \"user\",\n            content: `Selected product type: ${selectedType}`,\n            timestamp: new Date()\n          }]);\n          setTicketStep(ticketStep + 1);\n          addBot(ticketQuestions[ticketStep]);\n        } else {\n          addBot(\"Please select a valid product type from: Camera, Frame Grabber, Accessories, or Software.\");\n        }\n        return;\n      }\n      const keys = [\"purchasedFrom\", \"yearOfPurchase\", \"productName\", \"model\", \"serialNo\", \"operatingSystem\"];\n      const currentField = keys[ticketStep - 2];\n      const updatedTicketData = {\n        ...ticketData,\n        [currentField]: query.trim()\n      };\n      setTicketData(updatedTicketData);\n      if (ticketStep < ticketQuestions.length) {\n        setTicketStep(ticketStep + 1);\n        addBot(ticketQuestions[ticketStep]);\n      } else {\n        await submitTicket(updatedTicketData);\n      }\n      return;\n    }\n    if (awaitingProblemDescription && currentTicketNumber) {\n      setAwaitingProblemDescription(false);\n      setLoading(true);\n      try {\n        var _saveData$files;\n        const saveRes = await fetch(`${BACKEND_URL}/api/add_problem_description/`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${accessToken}`\n          },\n          body: JSON.stringify({\n            ticket_number: currentTicketNumber,\n            problem_description: userMsg.content\n          })\n        });\n        const saveData = await saveRes.json();\n        if (!saveRes.ok) {\n          throw new Error(saveData.error || \"Failed to save problem description.\");\n        }\n        let botContent = saveData.answer || \"No solution available at the moment.\";\n        const rawFiles = (_saveData$files = saveData.files) !== null && _saveData$files !== void 0 ? _saveData$files : []; // Updated from saveData.related_files\n        const validFiles = rawFiles.filter(f => {\n          if (typeof f === \"string\") {\n            return f.trim() !== \"\" && !f.toLowerCase().startsWith(\"none\");\n          }\n          if (typeof f === \"object\" && f !== null && f.filename) {\n            return f.filename.trim() !== \"\" && !f.filename.toLowerCase().startsWith(\"none\");\n          }\n          return false;\n        });\n        if (validFiles.length > 0) {\n          setPendingFiles(validFiles.map(f => {\n            const filename = typeof f === \"string\" ? f : f.filename;\n            const url = typeof f === \"string\" || !f.url || !f.url.startsWith(\"http\") ? `${BACKEND_URL}/api/files/${encodeURIComponent(filename)}?token=${accessToken}` : `${f.url}${f.url.includes(\"?\") ? \"&\" : \"?\"}token=${accessToken}`;\n            return {\n              source_file: filename,\n              url\n            };\n          }));\n          botContent += \"\\n\\n💡 For full explanation, do you want the related file? (yes/no)\";\n        } else {\n          setPendingFiles(null);\n        }\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: botContent,\n          timestamp: new Date()\n        }]);\n        setAwaitingOtherQueries(true);\n      } catch (err) {\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: `❌ Error processing problem description: ${err.message}`,\n          timestamp: new Date()\n        }]);\n      } finally {\n        setLoading(false);\n      }\n      return;\n    }\n    if (pendingFiles && (currentQuery === \"yes\" || currentQuery === \"no\")) {\n      const baseMessages = [];\n      if (currentQuery === \"yes\") {\n        const downloadLinks = pendingFiles.map((f, idx) => `${idx + 1}. [${f.source_file}](${f.url})`).join(\"\\n\");\n        baseMessages.push({\n          id: Date.now() + 1,\n          type: \"bot\",\n          content: `📎 Here are the related files:\\n\\n${downloadLinks}`,\n          timestamp: new Date()\n        });\n      } else {\n        baseMessages.push({\n          id: Date.now() + 1,\n          type: \"bot\",\n          content: \"👍 Okay, no files will be sent.\",\n          timestamp: new Date()\n        });\n      }\n      if (activeTicket !== null) {\n        baseMessages.push({\n          id: Date.now() + 2,\n          type: \"bot\",\n          content: \"Do you have any other queries? (yes/no)\",\n          timestamp: new Date()\n        });\n        setAwaitingOtherQueries(true);\n      }\n      setMessages(prev => [...prev, ...baseMessages]);\n      setPendingFiles(null);\n      return;\n    }\n    if (awaitingOtherQueries) {\n      if (currentQuery === \"no\") {\n        setAwaitingOtherQueries(false);\n        setAwaitingCloseConfirmation(true);\n        addBot(\"Can I close this ticket now? (yes/no)\");\n      } else if (currentQuery === \"yes\") {\n        setAwaitingOtherQueries(false);\n        addBot(\"Please go ahead and ask your question.\");\n      } else {\n        addBot(\"Please answer 'yes' or 'no'. Do you have any other queries?\");\n      }\n      return;\n    }\n    if (awaitingCloseConfirmation) {\n      if (currentQuery === \"yes\") {\n        setAwaitingCloseConfirmation(false);\n        setLoading(true);\n        try {\n          const response = await fetch(`${BACKEND_URL}/api/update_ticket_status/`, {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\",\n              Authorization: `Bearer ${accessToken}`\n            },\n            body: JSON.stringify({\n              ticket_number: currentTicketNumber,\n              status: \"closed\"\n            })\n          });\n          const data = await response.json();\n          if (response.ok) {\n            setActiveTicket(null);\n            setCurrentTicketNumber(null);\n            setTicketData({\n              productType: \"\",\n              purchasedFrom: \"\",\n              yearOfPurchase: \"\",\n              productName: \"\",\n              model: \"\",\n              serialNo: \"\",\n              operatingSystem: \"\"\n            });\n            addBot(`✅ Ticket ${currentTicketNumber} has been closed. Thank you!`);\n\n            // --- New: Reset query count when ticket is closed ---\n            setTicketQueryCount(0);\n            // -----------------------------------------------------\n          } else {\n            throw new Error(data.error || \"Failed to close ticket.\");\n          }\n        } catch (err) {\n          addBot(`❌ Error closing ticket: ${err.message}`);\n        } finally {\n          setLoading(false);\n        }\n      } else if (currentQuery === \"no\") {\n        setAwaitingCloseConfirmation(false);\n        addBot(\"Okay, ticket will remain open.\");\n      } else {\n        addBot(\"Please answer 'yes' or 'no'. Can I close this ticket now?\");\n      }\n      return;\n    }\n    setLoading(true);\n    try {\n      const historyText = messages.filter(m => m.type === \"user\" || m.type === \"bot\").map(m => `${m.type === \"user\" ? \"User\" : \"Bot\"}: ${m.content}`).join(\"\\n\");\n      const finalPrompt = promptTemplate ? promptTemplate.replace(\"{context_text}\", \"some context text here\").replace(\"{history_text}\", historyText).replace(\"{query}\", query.trim()) : query.trim();\n      const response = await fetch(`${BACKEND_URL}/api/chat/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        },\n        body: JSON.stringify({\n          query: finalPrompt,\n          ticket_mode: !!activeTicket,\n          ticket_id: activeTicket,\n          stage: awaitingUnrelatedQueryResponse ? \"unrelated_query\" : \"\"\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        var _data$files;\n        let botContent = data.answer || \"…\";\n        if (data.stage === \"unrelated_query\") {\n          setAwaitingUnrelatedQueryResponse(true);\n        } else if (data.stage === \"create_new_ticket\") {\n          setTicketStep(1);\n          setActiveTicket(null);\n          setCurrentTicketNumber(null);\n          addBot(ticketQuestions[0]);\n\n          // --- New: Reset query count on new ticket here too ---\n          setTicketQueryCount(0);\n          // ------------------------------------------------------\n          return;\n        }\n        const rawFiles = (_data$files = data.files) !== null && _data$files !== void 0 ? _data$files : [];\n        const validFiles = rawFiles.filter(f => {\n          if (typeof f === \"string\") {\n            return f.trim() !== \"\" && !f.toLowerCase().startsWith(\"none\");\n          }\n          if (typeof f === \"object\" && f !== null && f.filename) {\n            return f.filename.trim() !== \"\" && !f.filename.toLowerCase().startsWith(\"none\");\n          }\n          return false;\n        });\n        if (validFiles.length > 0) {\n          setPendingFiles(validFiles.map(f => {\n            const filename = typeof f === \"string\" ? f : f.filename;\n            const url = typeof f === \"string\" || !f.url || !f.url.startsWith(\"http\") ? `${BACKEND_URL}/api/files/${encodeURIComponent(filename)}?token=${accessToken}` : `${f.url}${f.url.includes(\"?\") ? \"&\" : \"?\"}token=${accessToken}`;\n            return {\n              source_file: filename,\n              url: url\n            };\n          }));\n          if (!botContent.toLowerCase().includes(\"do you want the related file\")) {\n            botContent += \"\\n\\n💡 For full explanation, do you want the related file? (yes/no)\";\n          }\n        } else {\n          setPendingFiles(null);\n        }\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: botContent,\n          timestamp: new Date()\n        }]);\n\n        // --- New: Increment query count for ticket queries ---\n        if (activeTicket && ticketStep === 0) {\n          setTicketQueryCount(prev => prev + 1);\n        }\n        // ------------------------------------------------------\n\n        if (data.stage === \"await_close\") {\n          setAwaitingCloseConfirmation(true);\n        }\n      } else {\n        setError(data.error || \"Error processing request\");\n      }\n    } catch (err) {\n      setError(\"Network error: \" + err.message);\n    } finally {\n      setLoading(false);\n    }\n  }\n\n  // Logout handler\n  function handleLogout() {\n    localStorage.removeItem(\"access\");\n    localStorage.removeItem(\"refresh\");\n    localStorage.removeItem(\"userData\");\n    window.location.href = \"/auth\";\n  }\n\n  // Format message time HH:MM\n  const formatTime = timestamp => {\n    return timestamp.toLocaleTimeString([], {\n      hour: \"2-digit\",\n      minute: \"2-digit\"\n    });\n  };\n\n  // Render chat messages with links parsed\n  const renderMessages = () => messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `message ${message.type}`,\n    style: {\n      textAlign: message.type === \"user\" ? \"right\" : \"left\"\n    },\n    \"aria-live\": \"polite\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"message-content\",\n      style: {\n        display: \"inline-block\",\n        maxWidth: \"75%\",\n        padding: \"8px 12px\",\n        borderRadius: \"12px\",\n        backgroundColor: message.type === \"user\" ? \"#DCF8C6\" : \"#F1F0F0\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-text\",\n        style: {\n          whiteSpace: \"pre-wrap\",\n          overflowY: \"auto\",\n          maxHeight: \"400px\",\n          fontSize: \"1em\",\n          lineHeight: \"1.5\"\n        },\n        children: [message.content.split(\"\\n\").map((line, idx) => {\n          const parts = [];\n          let remaining = line;\n          let keyIndex = 0;\n          while (remaining.length > 0) {\n            const linkMatch = remaining.match(/\\[(.*?)\\]\\((http.*?)\\)/);\n            const boldMatch = remaining.match(/\\*\\*(.*?)\\*\\*/);\n            if (linkMatch && (!boldMatch || linkMatch.index < boldMatch.index)) {\n              let href = linkMatch[2];\n              if (!href.startsWith(\"http\")) {\n                href = `${BACKEND_URL}${href.startsWith(\"/\") ? href : \"/\" + href}`;\n              }\n              if ((href.startsWith(`${BACKEND_URL}/api/files/`) || href.startsWith(\"/api/files/\")) && accessToken && !href.includes(\"token=\")) {\n                href += href.includes(\"?\") ? `&token=${accessToken}` : `?token=${accessToken}`;\n              }\n              parts.push(/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [remaining.slice(0, linkMatch.index), /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: href,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  style: {\n                    color: \"#0645AD\",\n                    textDecoration: \"underline\"\n                  },\n                  children: linkMatch[1]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 960,\n                  columnNumber: 23\n                }, this)]\n              }, keyIndex++, true, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 21\n              }, this));\n              remaining = remaining.slice(linkMatch.index + linkMatch[0].length);\n            } else if (boldMatch) {\n              parts.push(/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [remaining.slice(0, boldMatch.index), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: boldMatch[1]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 975,\n                  columnNumber: 23\n                }, this)]\n              }, keyIndex++, true, {\n                fileName: _jsxFileName,\n                lineNumber: 973,\n                columnNumber: 21\n              }, this));\n              remaining = remaining.slice(boldMatch.index + boldMatch[0].length);\n            } else {\n              parts.push(/*#__PURE__*/_jsxDEV(\"span\", {\n                children: remaining\n              }, keyIndex++, false, {\n                fileName: _jsxFileName,\n                lineNumber: 980,\n                columnNumber: 30\n              }, this));\n              break;\n            }\n          }\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            children: parts\n          }, idx, false, {\n            fileName: _jsxFileName,\n            lineNumber: 985,\n            columnNumber: 22\n          }, this);\n        }), message.tickets && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: \"8px\"\n          },\n          children: message.tickets.map((ticket, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              cursor: \"pointer\",\n              padding: \"6px 4px\",\n              borderBottom: idx !== message.tickets.length - 1 ? \"1px solid #eee\" : \"none\"\n            },\n            onClick: () => handleTicketSelect(ticket.ticketNumber),\n            children: [ticket.index, \". \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: ticket.ticketNumber\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1000,\n              columnNumber: 37\n            }, this), \" \\u2014 \", ticket.title]\n          }, ticket.ticketNumber, true, {\n            fileName: _jsxFileName,\n            lineNumber: 990,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 988,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 926,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-time\",\n        style: {\n          fontSize: \"0.7em\",\n          color: \"#666\",\n          marginTop: \"6px\"\n        },\n        children: formatTime(message.timestamp)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1006,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 916,\n      columnNumber: 9\n    }, this)\n  }, message.id, false, {\n    fileName: _jsxFileName,\n    lineNumber: 910,\n    columnNumber: 7\n  }, this));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chat-container\",\n    role: \"main\",\n    \"aria-label\": \"AI Agent Chatbot\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-header\",\n      style: {\n        position: \"relative\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"ONLINE SOLUTIONS TECHNICAL SUPPORT\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1019,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"subtitle\",\n        children: \"Technical Documentation Assistant\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1020,\n        columnNumber: 9\n      }, this), username && /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          fontSize: \"0.9em\",\n          color: \"#444\"\n        },\n        children: [\"Logged in as: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: username\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1023,\n          columnNumber: 27\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1022,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: \"absolute\",\n          top: 15,\n          right: 15\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleLogout,\n          style: {\n            padding: \"6px 12px\",\n            cursor: \"pointer\",\n            backgroundColor: \"#d9534f\",\n            border: \"none\",\n            borderRadius: 4,\n            color: \"white\",\n            fontWeight: \"bold\"\n          },\n          \"aria-label\": \"Logout\",\n          title: \"Logout\",\n          children: \"Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1027,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1026,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1018,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-messages\",\n      \"aria-live\": \"polite\",\n      \"aria-relevant\": \"additions\",\n      children: [renderMessages(), (loading || verifying) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message bot typing\",\n        \"aria-label\": \"Analyzing\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"message-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"typing-indicator\",\n            \"aria-hidden\": \"true\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1053,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1054,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1055,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1052,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"message-text\",\n            children: verifying ? \"Verifying organization...\" : \"Analyzing...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1057,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1051,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1050,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1063,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1047,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      onClick: () => setError(\"\"),\n      style: {\n        cursor: \"pointer\"\n      },\n      role: \"alert\",\n      \"aria-live\": \"assertive\",\n      tabIndex: 0,\n      children: [error, \" (click to dismiss)\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1067,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      className: \"chat-input-form\",\n      onSubmit: handleSubmit,\n      \"aria-label\": \"Send message form\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-container\",\n        style: {\n          display: \"flex\",\n          alignItems: \"center\",\n          width: \"100%\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: query,\n          onChange: onInputChange,\n          placeholder: orgVerified ? ticketStep > 0 ? ticketQuestions[ticketStep - 1] : awaitingUnrelatedQueryResponse ? \"Answer 'yes' or 'no' to create a new ticket...\" : awaitingCloseConfirmation ? \"Answer 'yes' or 'no' to close the ticket...\" : \"Type your question here...\" : \"Enter your organization name...\",\n          disabled: loading || verifying || ticketStep === 1 && !query,\n          autoFocus: true,\n          \"aria-label\": \"Chat input\",\n          style: {\n            flex: \"1\",\n            width: \"100%\",\n            padding: \"8px\",\n            borderRadius: \"4px 0 0 4px\",\n            border: \"1px solid #ccc\",\n            margin: 0\n          },\n          onKeyDown: e => {\n            if (e.key === \"Enter\" && !e.shiftKey) {\n              e.preventDefault();\n              handleSubmit(e);\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1081,\n          columnNumber: 11\n        }, this), ticketStep === 1 && /*#__PURE__*/_jsxDEV(\"select\", {\n          value: ticketData.productType,\n          onChange: handleProductTypeChange,\n          style: {\n            marginLeft: \"0\",\n            padding: \"6px\",\n            borderRadius: \"0\",\n            border: \"1px solid #ccc\",\n            borderLeft: \"none\",\n            fontSize: \"1em\"\n          },\n          \"aria-label\": \"Select product type\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            disabled: true,\n            children: \"Select a product type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1121,\n            columnNumber: 15\n          }, this), productTypeOptions.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: option,\n            children: option\n          }, option, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1125,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1108,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: loading || !query.trim() || verifying || ticketStep === 1 && !ticketData.productType,\n          title: loading || verifying ? \"Please wait...\" : \"Send\",\n          \"aria-label\": \"Send message\",\n          style: {\n            padding: \"8px 12px\",\n            borderRadius: \"0 4px 4px 0\",\n            border: \"1px solid #ccc\",\n            borderLeft: \"none\",\n            backgroundColor: \"#4CAF50\",\n            color: \"white\"\n          },\n          children: loading || verifying ? /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1138,\n            columnNumber: 37\n          }, this) : \"📤\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1080,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1079,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1017,\n    columnNumber: 5\n  }, this);\n}\n_s(Home, \"vPLbo8uMWC1f7E0LLqeAAxrHQkQ=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "BACKEND_URL", "Home", "token", "_s", "accessToken", "localStorage", "getItem", "query", "<PERSON><PERSON><PERSON><PERSON>", "messages", "setMessages", "promptTemplate", "setPromptTemplate", "loading", "setLoading", "error", "setError", "mode", "setMode", "pendingFiles", "setPendingFiles", "orgVerified", "setOrgVerified", "verifying", "setVerifying", "username", "setUserName", "askRaiseTicket", "setAskRaiseTicket", "awaitingCloseConfirmation", "setAwaitingCloseConfirmation", "awaiting<PERSON><PERSON><PERSON><PERSON><PERSON>", "setAwaiting<PERSON><PERSON><PERSON><PERSON><PERSON>", "pendingTickets", "setPendingTickets", "awaitingPendingChoice", "setAwaitingPendingChoice", "awaitingTicketSelect", "setAwaitingTicketSelect", "activeTicket", "setActiveTicket", "ticketRefused", "setTicketRefused", "queriesAfterNoTicket", "setQueriesAfterNoTicket", "awaitingUnrelatedQueryResponse", "setAwaitingUnrelatedQueryResponse", "MAX_QUERIES_AFTER_NO_TICKET", "tickets", "setTickets", "showTickets", "setShowTickets", "ticketQueryCount", "setTicketQueryCount", "ticketStep", "setTicketStep", "ticketData", "setTicketData", "productType", "purchasedFrom", "yearOfPurchase", "productName", "model", "serialNo", "operatingSystem", "awaitingProblemDescription", "setAwaitingProblemDescription", "currentTicketNumber", "setCurrentTicketNumber", "ticketQuestions", "productTypeOptions", "_messagesEndRef$curre", "messagesEndRef", "current", "scrollIntoView", "behavior", "fetch", "then", "res", "json", "data", "template", "catch", "err", "console", "urlParams", "URLSearchParams", "window", "location", "search", "isPendingMode", "get", "loadPendingTicketDirectly", "headers", "Authorization", "ok", "errorText", "text", "status", "Error", "name", "email", "id", "type", "content", "timestamp", "Date", "message", "removeItem", "href", "onInputChange", "e", "target", "value", "handleProductTypeChange", "selectedType", "prev", "now", "addBot", "verifyOrganization", "orgName", "response", "method", "body", "JSON", "stringify", "organization", "fetchPendingTickets", "baseUrl", "origin", "signupLink", "length", "submitTicket", "finalTicketData", "validProductTypes", "allFilled", "Object", "values", "every", "v", "trim", "includes", "product_type", "purchased_from", "year_of_purchase", "product_name", "serial_no", "operating_system", "ticket_number", "errors", "handleTicketSelect", "ticketNumber", "summaryRes", "summaryData", "problem_summary", "problem_description", "solution_summary", "handleSubmit", "preventDefault", "<PERSON><PERSON><PERSON><PERSON>", "toLowerCase", "n", "userMsg", "map", "t", "i", "index", "title", "short_title", "picked", "find", "idx", "String", "keys", "current<PERSON><PERSON>", "updatedTicketData", "_saveData$files", "saveRes", "saveData", "botContent", "answer", "rawFiles", "files", "validFiles", "filter", "f", "startsWith", "filename", "url", "encodeURIComponent", "source_file", "baseMessages", "downloadLinks", "join", "push", "historyText", "m", "finalPrompt", "replace", "ticket_mode", "ticket_id", "stage", "_data$files", "handleLogout", "formatTime", "toLocaleTimeString", "hour", "minute", "renderMessages", "className", "style", "textAlign", "children", "display", "max<PERSON><PERSON><PERSON>", "padding", "borderRadius", "backgroundColor", "whiteSpace", "overflowY", "maxHeight", "fontSize", "lineHeight", "split", "line", "parts", "remaining", "keyIndex", "linkMatch", "match", "boldMatch", "slice", "rel", "color", "textDecoration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "ticket", "cursor", "borderBottom", "onClick", "role", "position", "top", "right", "border", "fontWeight", "ref", "tabIndex", "onSubmit", "alignItems", "width", "onChange", "placeholder", "disabled", "autoFocus", "flex", "margin", "onKeyDown", "key", "shift<PERSON>ey", "marginLeft", "borderLeft", "option", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/AI-Agent-Chatbot-main/frontend/src/Home.jsx"], "sourcesContent": ["\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport \"./App.css\";\r\n\r\n// Define the backend URL\r\nconst BACKEND_URL = \"http://localhost:8000\";\r\n\r\nexport default function Home({ token }) {\r\n  const accessToken = token || localStorage.getItem(\"access\");\r\n\r\n  // --- States ---\r\n  const [query, setQuery] = useState(\"\");\r\n  const [messages, setMessages] = useState([]);\r\n  const [promptTemplate, setPromptTemplate] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(\"\");\r\n  const [mode, setMode] = useState(\"strict\");\r\n  const [pendingFiles, setPendingFiles] = useState(null);\r\n  const [orgVerified, setOrgVerified] = useState(false);\r\n  const [verifying, setVerifying] = useState(false);\r\n  const [username, setUserName] = useState(\"\");\r\n  const [askRaiseTicket, setAskRaiseTicket] = useState(false);\r\n  const [awaitingCloseConfirmation, setAwaitingCloseConfirmation] = useState(false);\r\n  const [awaitingOtherQueries, setAwaitingOtherQueries] = useState(false);\r\n  const [pendingTickets, setPendingTickets] = useState([]);\r\n  const [awaitingPendingChoice, setAwaitingPendingChoice] = useState(false);\r\n  const [awaitingTicketSelect, setAwaitingTicketSelect] = useState(false);\r\n  const [activeTicket, setActiveTicket] = useState(null);\r\n  const [ticketRefused, setTicketRefused] = useState(false);\r\n  const [queriesAfterNoTicket, setQueriesAfterNoTicket] = useState(0);\r\n  const [awaitingUnrelatedQueryResponse, setAwaitingUnrelatedQueryResponse] = useState(false);\r\n  const MAX_QUERIES_AFTER_NO_TICKET = 10;\r\n  const [tickets, setTickets] = useState([]);\r\n  const [showTickets, setShowTickets] = useState(false);\r\n  const [ticketQueryCount, setTicketQueryCount] = useState(0);\r\n\r\n\r\n  // --- New ticket states ---\r\n  const [ticketStep, setTicketStep] = useState(0);\r\n  const [ticketData, setTicketData] = useState({\r\n    productType: \"\",\r\n    purchasedFrom: \"\",\r\n    yearOfPurchase: \"\",\r\n    productName: \"\",\r\n    model: \"\",\r\n    serialNo: \"\",\r\n    operatingSystem: \"\",\r\n  });\r\n  const [awaitingProblemDescription, setAwaitingProblemDescription] = useState(false);\r\n  const [currentTicketNumber, setCurrentTicketNumber] = useState(null);\r\n\r\n  const ticketQuestions = [\r\n    \"Please select the product type using the dropdown below:\",\r\n    \"Please enter the 'Purchased From' information:\",\r\n    \"Please enter the 'Year of Purchase':\",\r\n    \"Please enter the 'Product Name':\",\r\n    \"Please enter the 'Model':\",\r\n    \"Please enter the 'Serial Number':\",\r\n    \"Please enter the 'Operating System':\",\r\n  ];\r\n\r\n  const productTypeOptions = [\"Camera\", \"Frame Grabber\", \"Accessories\", \"Software\"];\r\n\r\n  // Auto-scroll messages\r\n  useEffect(() => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  }, [messages, loading, error]);\r\n\r\n  const messagesEndRef = useRef(null);\r\n\r\n  // Fetch prompt template\r\n  useEffect(() => {\r\n    fetch(`${BACKEND_URL}/api/prompt/?type=chat`)\r\n      .then((res) => res.json())\r\n      .then((data) => setPromptTemplate(data.template))\r\n      .catch((err) => {\r\n        console.error(\"Failed to fetch prompt template:\", err);\r\n        setPromptTemplate(null);\r\n      });\r\n  }, []);\r\n\r\n  // Check for pending mode from URL\r\n  useEffect(() => {\r\n    const urlParams = new URLSearchParams(window.location.search);\r\n    const isPendingMode = urlParams.get('mode') === 'pending';\r\n\r\n    if (isPendingMode) {\r\n      // Skip organization verification and directly load pending ticket\r\n      loadPendingTicketDirectly();\r\n    }\r\n  }, []);\r\n\r\n  // Fetch username and welcome message\r\n  useEffect(() => {\r\n    if (!accessToken) return;\r\n\r\n    const urlParams = new URLSearchParams(window.location.search);\r\n    const isPendingMode = urlParams.get('mode') === 'pending';\r\n\r\n    if (isPendingMode) {\r\n      return; // Skip normal flow for pending mode\r\n    }\r\n\r\n    fetch(`${BACKEND_URL}/api/user_info/`, {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        Authorization: `Bearer ${accessToken}`,\r\n      },\r\n    })\r\n      .then(async (res) => {\r\n        if (!res.ok) {\r\n          const errorText = await res.text();\r\n          console.error(\"User info fetch failed:\", res.status, errorText);\r\n          throw new Error(\"User info fetch failed\");\r\n        }\r\n        return res.json();\r\n      })\r\n      .then((data) => {\r\n        const name = data.name || data.username || data.email;\r\n        if (!name) throw new Error(\"Name missing in response\");\r\n\r\n        setUserName(name);\r\n        setMessages([\r\n          {\r\n            id: 1,\r\n            type: \"bot\",\r\n            content: `👋 Welcome, ${name}! Please enter your organization name to verify your account.`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n      })\r\n      .catch((err) => {\r\n        console.error(\"Failed to fetch user info:\", err.message);\r\n        localStorage.removeItem(\"access\");\r\n        window.location.href = \"/auth\";\r\n      });\r\n  }, [accessToken]);\r\n\r\n  // Input change handler\r\n  const onInputChange = (e) => {\r\n    if (error) setError(\"\");\r\n    setQuery(e.target.value);\r\n  };\r\n\r\n  // Dropdown change handler for productType\r\n  const handleProductTypeChange = (e) => {\r\n    const selectedType = e.target.value;\r\n    setTicketData({ ...ticketData, productType: selectedType });\r\n    setMessages((prev) => [\r\n      ...prev,\r\n      {\r\n        id: Date.now(),\r\n        type: \"user\",\r\n        content: `Selected product type: ${selectedType}`,\r\n        timestamp: new Date(),\r\n      },\r\n    ]);\r\n    setTicketStep(ticketStep + 1);\r\n    addBot(ticketQuestions[ticketStep]);\r\n  };\r\n\r\n  // Verify organization name\r\n  const verifyOrganization = async (orgName) => {\r\n    setVerifying(true);\r\n    setError(\"\");\r\n\r\n    try {\r\n      const response = await fetch(`${BACKEND_URL}/api/verify_organization/`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n        body: JSON.stringify({ organization: orgName }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok && data.status === \"verified\") {\r\n        setOrgVerified(true);\r\n        setUserName(data.name || username);\r\n\r\n        setMessages((prev) => [\r\n          ...prev,\r\n          {\r\n            id: Date.now(),\r\n            type: \"bot\",\r\n            content: data.message || \"✅ Organization verified.\",\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n\r\n        await fetchPendingTickets();\r\n      } else {\r\n        const baseUrl = window.location.origin;\r\n        const signupLink = `[sign up here](${baseUrl}/signup/)`;\r\n\r\n        setError(data.message || \"❌ Organization mismatch.\");\r\n        setMessages((prev) => [\r\n          ...prev,\r\n          {\r\n            id: Date.now(),\r\n            type: \"bot\",\r\n            content: `❌ Organization verification failed.\\n\\nIf you belong to a new organization, please ${signupLink} to register first.`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n\r\n        localStorage.removeItem(\"access\");\r\n      }\r\n    } catch (err) {\r\n      setError(\"Network error during verification.\");\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: \"❌ Network error during organization verification. Please try again.\",\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n      console.error(\"Verification error:\", err);\r\n    } finally {\r\n      setVerifying(false);\r\n    }\r\n  };\r\n\r\n  // Helper function to fetch pending tickets after verification\r\n  async function fetchPendingTickets() {\r\n    try {\r\n      const response = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {\r\n        headers: {\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n      });\r\n      const data = await response.json();\r\n\r\n      if (response.ok) {\r\n        setPendingTickets(data.tickets || []);\r\n        if (data.tickets && data.tickets.length > 0) {\r\n          setAwaitingPendingChoice(true);\r\n          addBot(\r\n            `You have ${data.tickets.length} pending ticket(s). Do you want to continue with any of them? (yes/no)`\r\n          );\r\n        } else {\r\n          setAskRaiseTicket(true);\r\n          addBot(\"No pending tickets found. Would you like to raise a support ticket? (yes/no)\");\r\n        }\r\n      } else {\r\n        setPendingTickets([]);\r\n        setAskRaiseTicket(true);\r\n        addBot(\"Could not fetch pending tickets. Would you like to raise a support ticket? ( Juno/no)\");\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error fetching pending tickets:\", err);\r\n      setPendingTickets([]);\r\n      setAskRaiseTicket(true);\r\n      addBot(\"Error fetching pending tickets. Would you like to raise a support ticket? (yes/no)\");\r\n    }\r\n  }\r\n\r\n  // Submit ticket with latest data\r\n  const submitTicket = async (finalTicketData) => {\r\n    setLoading(true);\r\n    setError(\"\");\r\n\r\n    const validProductTypes = [\"Camera\", \"Frame Grabber\", \"Accessories\", \"Software\"];\r\n    const allFilled = Object.values(finalTicketData).every(\r\n      (v) => v && v.trim() !== \"\"\r\n    );\r\n    if (!allFilled) {\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: \"❌ Please fill in all required fields before submitting the ticket.\",\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n      setLoading(false);\r\n      return;\r\n    }\r\n    if (!validProductTypes.includes(finalTicketData.productType)) {\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: \"❌ Invalid product type. Please select: Camera, Frame Grabber, Accessories, or Software.\",\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n        body: JSON.stringify({\r\n          product_type: finalTicketData.productType,\r\n          purchased_from: finalTicketData.purchasedFrom,\r\n          year_of_purchase: finalTicketData.yearOfPurchase,\r\n          product_name: finalTicketData.productName,\r\n          model: finalTicketData.model,\r\n          serial_no: finalTicketData.serialNo,\r\n          operating_system: finalTicketData.operatingSystem,\r\n        }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok) {\r\n        setCurrentTicketNumber(data.ticket_number);\r\n        setActiveTicket(data.ticket_number);\r\n        setAwaitingProblemDescription(true);\r\n        setMessages((prev) => [\r\n          ...prev,\r\n          {\r\n            id: Date.now(),\r\n            type: \"bot\",\r\n            content: `🎉 Thank you! Your support ticket has been created successfully. Your ticket number is **${data.ticket_number}**.\\n\\nPlease describe your problem so we can assist you.`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n      } else {\r\n        setMessages((prev) => [\r\n          ...prev,\r\n          {\r\n            id: Date.now(),\r\n            type: \"bot\",\r\n            content: `❌ Failed to create ticket: ${JSON.stringify(data.errors || data.message)}`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n      }\r\n    } catch (err) {\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: `❌ Network error while creating ticket: ${err.message}`,\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n    } finally {\r\n      setLoading(false);\r\n      setTicketStep(0);\r\n      setAskRaiseTicket(false);\r\n      setTicketData({\r\n        productType: \"\",\r\n        purchasedFrom: \"\",\r\n        yearOfPurchase: \"\",\r\n        productName: \"\",\r\n        model: \"\",\r\n        serialNo: \"\",\r\n        operatingSystem: \"\",\r\n      });\r\n    }\r\n  };\r\n\r\n  // Helper to add a bot message\r\n  function addBot(text) {\r\n    setMessages((prev) => [\r\n      ...prev,\r\n      { id: Date.now(), type: \"bot\", content: text, timestamp: new Date() },\r\n    ]);\r\n  }\r\n\r\n  // Handle ticket selection from UI\r\n  const handleTicketSelect = async (ticketNumber) => {\r\n    setAwaitingTicketSelect(false);\r\n    setActiveTicket(ticketNumber);\r\n    setCurrentTicketNumber(ticketNumber);\r\n    setShowTickets(false);\r\n\r\n    try {\r\n      const summaryRes = await fetch(`${BACKEND_URL}/api/ticket_summary/`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n        body: JSON.stringify({ ticket_number: ticketNumber }),\r\n      });\r\n\r\n      const summaryData = await summaryRes.json();\r\n\r\n      if (summaryRes.ok) {\r\n        addBot(\r\n          `🔄 Resuming ticket **${ticketNumber}** …\\n\\n` +\r\n            `📝 **Raised problem:** ${summaryData.problem_summary || summaryData.problem_description}\\n\\n` +\r\n            `💡 **Given solution:** ${summaryData.solution_summary || \"No solution yet.\"}\\n\\n` +\r\n            \"✅ You can ask your follow-up query now.\"\r\n        );\r\n      } else {\r\n        addBot(\"⚠️ Error fetching ticket summary.\");\r\n      }\r\n    } catch (err) {\r\n      addBot(\"❌ Network error while fetching ticket summary.\");\r\n    }\r\n  };\r\n\r\n  // FULLY UPDATED handleSubmit\r\nasync function handleSubmit(e) {\r\n  e.preventDefault();\r\n  if (!query.trim() || loading || verifying) return;\r\n\r\n  const currentQuery = query.trim().toLowerCase();\r\n\r\n  // --- New: Check 5 queries per ticket limit ---\r\n  if (\r\n    activeTicket &&            // There is an active ticket\r\n    ticketStep === 0 &&        // Not in ticket creation steps\r\n    !awaitingProblemDescription && // Not waiting for problem description input\r\n    ticketQueryCount >= 5      // Limit reached\r\n  ) {\r\n    addBot(\r\n      \"🛑 You have reached the maximum of five queries for this ticket. It has been automatically escalated to ensure prompt resolution. Kindly create a new ticket for any further inquiries or await our team’s response.\"\r\n    );\r\n    setQuery(\"\");\r\n    return;\r\n  }\r\n  // ---------------------------------------------\r\n\r\n  if (ticketRefused) {\r\n    if (queriesAfterNoTicket >= MAX_QUERIES_AFTER_NO_TICKET) {\r\n      addBot(\r\n        \"⚠️ You have reached the maximum number of free queries. Please raise a support ticket for further assistance.\"\r\n      );\r\n      setQuery(\"\");\r\n      return;\r\n    } else {\r\n      setQueriesAfterNoTicket((n) => n + 1);\r\n    }\r\n  }\r\n\r\n  const userMsg = {\r\n    id: Date.now(),\r\n    type: \"user\",\r\n    content: query.trim(),\r\n    timestamp: new Date(),\r\n  };\r\n  setMessages((prev) => [...prev, userMsg]);\r\n  setQuery(\"\");\r\n  setError(\"\");\r\n\r\n  if (awaitingPendingChoice) {\r\n    setAwaitingPendingChoice(false);\r\n\r\n    if (currentQuery === \"yes\") {\r\n      setAwaitingTicketSelect(true);\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: `Select a ticket by typing its number:`,\r\n          timestamp: new Date(),\r\n          tickets: pendingTickets.map((t, i) => ({\r\n            index: i + 1,\r\n            ticketNumber: t.ticket_number,\r\n            title: t.title || t.short_title || \"No title\",\r\n          })),\r\n        },\r\n      ]);\r\n    } else if (currentQuery === \"no\") {\r\n      setAskRaiseTicket(true);\r\n      addBot(\"Do you want to raise a support ticket? (yes/no)\");\r\n    } else {\r\n      addBot(\"Please answer 'yes' or 'no'. Do you want to continue an open ticket?\");\r\n      setAwaitingPendingChoice(true);\r\n    }\r\n    return;\r\n  }\r\n\r\n  if (awaitingTicketSelect) {\r\n    const picked = pendingTickets.find(\r\n      (t, idx) =>\r\n        currentQuery === String(idx + 1) ||\r\n        currentQuery.includes(t.ticket_number.toLowerCase())\r\n    );\r\n\r\n    if (!picked) {\r\n      addBot(\"Ticket not recognised, please type its number.\");\r\n      return;\r\n    }\r\n\r\n    await handleTicketSelect(picked.ticket_number);\r\n    return;\r\n  }\r\n\r\n  if (!orgVerified) {\r\n    await verifyOrganization(currentQuery);\r\n    return;\r\n  }\r\n\r\n  if (askRaiseTicket) {\r\n    if (currentQuery === \"yes\") {\r\n      setAskRaiseTicket(false);\r\n      setTicketStep(1);\r\n      addBot(ticketQuestions[0]);\r\n      setTicketRefused(false);\r\n      setQueriesAfterNoTicket(0);\r\n\r\n      // --- New: Reset query count when new ticket starts ---\r\n      setTicketQueryCount(0);\r\n      // ------------------------------------------------------\r\n\r\n    } else if (currentQuery === \"no\") {\r\n      setAskRaiseTicket(false);\r\n      addBot(\"👍 Okay, no ticket will be raised. How else can I help you?\");\r\n      setTicketRefused(true);\r\n      setQueriesAfterNoTicket(0);\r\n    } else {\r\n      addBot(\"Please answer 'yes' or 'no'. Do you want to raise a support ticket?\");\r\n    }\r\n    return;\r\n  }\r\n\r\n  if (awaitingUnrelatedQueryResponse) {\r\n    setAwaitingUnrelatedQueryResponse(false);\r\n    if (currentQuery === \"yes\") {\r\n      setTicketStep(1);\r\n      setActiveTicket(null);\r\n      setCurrentTicketNumber(null);\r\n      addBot(ticketQuestions[0]);\r\n\r\n      // --- New: Reset query count when new ticket starts here too ---\r\n      setTicketQueryCount(0);\r\n      // ---------------------------------------------------------------\r\n\r\n    } else if (currentQuery === \"no\") {\r\n      setAwaitingCloseConfirmation(true);\r\n      addBot(\"Can I close this ticket now? (yes/no)\");\r\n    } else {\r\n      setAwaitingUnrelatedQueryResponse(true);\r\n      addBot(\"Please answer 'yes' or 'no'. Do you want to create a new ticket?\");\r\n    }\r\n    return;\r\n  }\r\n\r\n  if (ticketStep > 0 && ticketStep <= ticketQuestions.length) {\r\n    if (ticketStep === 1) {\r\n      const selectedType = query.trim();\r\n      if (productTypeOptions.includes(selectedType)) {\r\n        setTicketData({ ...ticketData, productType: selectedType });\r\n        setMessages((prev) => [\r\n          ...prev,\r\n          {\r\n            id: Date.now(),\r\n            type: \"user\",\r\n            content: `Selected product type: ${selectedType}`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n        setTicketStep(ticketStep + 1);\r\n        addBot(ticketQuestions[ticketStep]);\r\n      } else {\r\n        addBot(\r\n          \"Please select a valid product type from: Camera, Frame Grabber, Accessories, or Software.\"\r\n        );\r\n      }\r\n      return;\r\n    }\r\n    const keys = [\r\n      \"purchasedFrom\",\r\n      \"yearOfPurchase\",\r\n      \"productName\",\r\n      \"model\",\r\n      \"serialNo\",\r\n      \"operatingSystem\",\r\n    ];\r\n    const currentField = keys[ticketStep - 2];\r\n    const updatedTicketData = { ...ticketData, [currentField]: query.trim() };\r\n    setTicketData(updatedTicketData);\r\n\r\n    if (ticketStep < ticketQuestions.length) {\r\n      setTicketStep(ticketStep + 1);\r\n      addBot(ticketQuestions[ticketStep]);\r\n    } else {\r\n      await submitTicket(updatedTicketData);\r\n    }\r\n    return;\r\n  }\r\n\r\n  if (awaitingProblemDescription && currentTicketNumber) {\r\n    setAwaitingProblemDescription(false);\r\n    setLoading(true);\r\n\r\n    try {\r\n      const saveRes = await fetch(`${BACKEND_URL}/api/add_problem_description/`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n        body: JSON.stringify({\r\n          ticket_number: currentTicketNumber,\r\n          problem_description: userMsg.content,\r\n        }),\r\n      });\r\n\r\n      const saveData = await saveRes.json();\r\n      if (!saveRes.ok) {\r\n        throw new Error(saveData.error || \"Failed to save problem description.\");\r\n      }\r\n\r\n      let botContent = saveData.answer || \"No solution available at the moment.\";\r\n\r\n      const rawFiles = saveData.files ?? []; // Updated from saveData.related_files\r\n      const validFiles = rawFiles.filter((f) => {\r\n        if (typeof f === \"string\") {\r\n          return f.trim() !== \"\" && !f.toLowerCase().startsWith(\"none\");\r\n        }\r\n        if (typeof f === \"object\" && f !== null && f.filename) {\r\n          return (\r\n            f.filename.trim() !== \"\" &&\r\n            !f.filename.toLowerCase().startsWith(\"none\")\r\n          );\r\n        }\r\n        return false;\r\n      });\r\n\r\n      if (validFiles.length > 0) {\r\n        setPendingFiles(\r\n          validFiles.map((f) => {\r\n            const filename = typeof f === \"string\" ? f : f.filename;\r\n            const url =\r\n              typeof f === \"string\" || !f.url || !f.url.startsWith(\"http\")\r\n                ? `${BACKEND_URL}/api/files/${encodeURIComponent(\r\n                    filename\r\n                  )}?token=${accessToken}`\r\n                : `${f.url}${f.url.includes(\"?\") ? \"&\" : \"?\"}token=${accessToken}`;\r\n            return { source_file: filename, url };\r\n          })\r\n        );\r\n        botContent += \"\\n\\n💡 For full explanation, do you want the related file? (yes/no)\";\r\n      } else {\r\n        setPendingFiles(null);\r\n      }\r\n\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: botContent,\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n\r\n      setAwaitingOtherQueries(true);\r\n    } catch (err) {\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: `❌ Error processing problem description: ${err.message}`,\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n    return;\r\n  }\r\n\r\n  if (pendingFiles && (currentQuery === \"yes\" || currentQuery === \"no\")) {\r\n    const baseMessages = [];\r\n\r\n    if (currentQuery === \"yes\") {\r\n      const downloadLinks = pendingFiles\r\n        .map((f, idx) => `${idx + 1}. [${f.source_file}](${f.url})`)\r\n        .join(\"\\n\");\r\n\r\n      baseMessages.push({\r\n        id: Date.now() + 1,\r\n        type: \"bot\",\r\n        content: `📎 Here are the related files:\\n\\n${downloadLinks}`,\r\n        timestamp: new Date(),\r\n      });\r\n    } else {\r\n      baseMessages.push({\r\n        id: Date.now() + 1,\r\n        type: \"bot\",\r\n        content: \"👍 Okay, no files will be sent.\",\r\n        timestamp: new Date(),\r\n      });\r\n    }\r\n\r\n    if (activeTicket !== null) {\r\n      baseMessages.push({\r\n        id: Date.now() + 2,\r\n        type: \"bot\",\r\n        content: \"Do you have any other queries? (yes/no)\",\r\n        timestamp: new Date(),\r\n      });\r\n      setAwaitingOtherQueries(true);\r\n    }\r\n\r\n    setMessages((prev) => [...prev, ...baseMessages]);\r\n    setPendingFiles(null);\r\n    return;\r\n  }\r\n\r\n  if (awaitingOtherQueries) {\r\n    if (currentQuery === \"no\") {\r\n      setAwaitingOtherQueries(false);\r\n      setAwaitingCloseConfirmation(true);\r\n      addBot(\"Can I close this ticket now? (yes/no)\");\r\n    } else if (currentQuery === \"yes\") {\r\n      setAwaitingOtherQueries(false);\r\n      addBot(\"Please go ahead and ask your question.\");\r\n    } else {\r\n      addBot(\"Please answer 'yes' or 'no'. Do you have any other queries?\");\r\n    }\r\n    return;\r\n  }\r\n\r\n  if (awaitingCloseConfirmation) {\r\n    if (currentQuery === \"yes\") {\r\n      setAwaitingCloseConfirmation(false);\r\n      setLoading(true);\r\n      try {\r\n        const response = await fetch(`${BACKEND_URL}/api/update_ticket_status/`, {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: `Bearer ${accessToken}`,\r\n          },\r\n          body: JSON.stringify({\r\n            ticket_number: currentTicketNumber,\r\n            status: \"closed\",\r\n          }),\r\n        });\r\n        const data = await response.json();\r\n\r\n        if (response.ok) {\r\n          setActiveTicket(null);\r\n          setCurrentTicketNumber(null);\r\n          setTicketData({\r\n            productType: \"\",\r\n            purchasedFrom: \"\",\r\n            yearOfPurchase: \"\",\r\n            productName: \"\",\r\n            model: \"\",\r\n            serialNo: \"\",\r\n            operatingSystem: \"\",\r\n          });\r\n\r\n          addBot(`✅ Ticket ${currentTicketNumber} has been closed. Thank you!`);\r\n\r\n          // --- New: Reset query count when ticket is closed ---\r\n          setTicketQueryCount(0);\r\n          // -----------------------------------------------------\r\n\r\n        } else {\r\n          throw new Error(data.error || \"Failed to close ticket.\");\r\n        }\r\n      } catch (err) {\r\n        addBot(`❌ Error closing ticket: ${err.message}`);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    } else if (currentQuery === \"no\") {\r\n      setAwaitingCloseConfirmation(false);\r\n      addBot(\"Okay, ticket will remain open.\");\r\n    } else {\r\n      addBot(\"Please answer 'yes' or 'no'. Can I close this ticket now?\");\r\n    }\r\n    return;\r\n  }\r\n\r\n  setLoading(true);\r\n  try {\r\n    const historyText = messages\r\n      .filter((m) => m.type === \"user\" || m.type === \"bot\")\r\n      .map((m) => `${m.type === \"user\" ? \"User\" : \"Bot\"}: ${m.content}`)\r\n      .join(\"\\n\");\r\n\r\n    const finalPrompt = promptTemplate\r\n      ? promptTemplate\r\n          .replace(\"{context_text}\", \"some context text here\")\r\n          .replace(\"{history_text}\", historyText)\r\n          .replace(\"{query}\", query.trim())\r\n      : query.trim();\r\n\r\n    const response = await fetch(`${BACKEND_URL}/api/chat/`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        Authorization: `Bearer ${accessToken}`,\r\n      },\r\n      body: JSON.stringify({\r\n        query: finalPrompt,\r\n        ticket_mode: !!activeTicket,\r\n        ticket_id: activeTicket,\r\n        stage: awaitingUnrelatedQueryResponse ? \"unrelated_query\" : \"\",\r\n      }),\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (response.ok) {\r\n      let botContent = data.answer || \"…\";\r\n\r\n      if (data.stage === \"unrelated_query\") {\r\n        setAwaitingUnrelatedQueryResponse(true);\r\n      } else if (data.stage === \"create_new_ticket\") {\r\n        setTicketStep(1);\r\n        setActiveTicket(null);\r\n        setCurrentTicketNumber(null);\r\n        addBot(ticketQuestions[0]);\r\n\r\n        // --- New: Reset query count on new ticket here too ---\r\n        setTicketQueryCount(0);\r\n        // ------------------------------------------------------\r\n        return;\r\n      }\r\n\r\n      const rawFiles = data.files ?? [];\r\n      const validFiles = rawFiles.filter((f) => {\r\n        if (typeof f === \"string\") {\r\n          return f.trim() !== \"\" && !f.toLowerCase().startsWith(\"none\");\r\n        }\r\n        if (typeof f === \"object\" && f !== null && f.filename) {\r\n          return (\r\n            f.filename.trim() !== \"\" &&\r\n            !f.filename.toLowerCase().startsWith(\"none\")\r\n          );\r\n        }\r\n        return false;\r\n      });\r\n\r\n      if (validFiles.length > 0) {\r\n        setPendingFiles(\r\n          validFiles.map((f) => {\r\n            const filename = typeof f === \"string\" ? f : f.filename;\r\n            const url =\r\n              typeof f === \"string\" || !f.url || !f.url.startsWith(\"http\")\r\n                ? `${BACKEND_URL}/api/files/${encodeURIComponent(filename)}?token=${accessToken}`\r\n                : `${f.url}${f.url.includes(\"?\") ? \"&\" : \"?\"}token=${accessToken}`;\r\n            return {\r\n              source_file: filename,\r\n              url: url,\r\n            };\r\n          })\r\n        );\r\n        if (!botContent.toLowerCase().includes(\"do you want the related file\")) {\r\n          botContent += \"\\n\\n💡 For full explanation, do you want the related file? (yes/no)\";\r\n        }\r\n      } else {\r\n        setPendingFiles(null);\r\n      }\r\n\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: botContent,\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n\r\n      // --- New: Increment query count for ticket queries ---\r\n      if (activeTicket && ticketStep === 0) {\r\n        setTicketQueryCount((prev) => prev + 1);\r\n      }\r\n      // ------------------------------------------------------\r\n\r\n      if (data.stage === \"await_close\") {\r\n        setAwaitingCloseConfirmation(true);\r\n      }\r\n    } else {\r\n      setError(data.error || \"Error processing request\");\r\n    }\r\n  } catch (err) {\r\n    setError(\"Network error: \" + err.message);\r\n  } finally {\r\n    setLoading(false);\r\n  }\r\n}\r\n\r\n\r\n  // Logout handler\r\n  function handleLogout() {\r\n    localStorage.removeItem(\"access\");\r\n    localStorage.removeItem(\"refresh\");\r\n    localStorage.removeItem(\"userData\");\r\n    window.location.href = \"/auth\";\r\n  }\r\n\r\n  // Format message time HH:MM\r\n  const formatTime = (timestamp) => {\r\n    return timestamp.toLocaleTimeString([], { hour: \"2-digit\", minute: \"2-digit\" });\r\n  };\r\n\r\n  // Render chat messages with links parsed\r\n  const renderMessages = () =>\r\n    messages.map((message) => (\r\n      <div\r\n        key={message.id}\r\n        className={`message ${message.type}`}\r\n        style={{ textAlign: message.type === \"user\" ? \"right\" : \"left\" }}\r\n        aria-live=\"polite\"\r\n      >\r\n        <div\r\n          className=\"message-content\"\r\n          style={{\r\n            display: \"inline-block\",\r\n            maxWidth: \"75%\",\r\n            padding: \"8px 12px\",\r\n            borderRadius: \"12px\",\r\n            backgroundColor: message.type === \"user\" ? \"#DCF8C6\" : \"#F1F0F0\",\r\n          }}\r\n        >\r\n          <div\r\n            className=\"message-text\"\r\n            style={{\r\n              whiteSpace: \"pre-wrap\",\r\n              overflowY: \"auto\",\r\n              maxHeight: \"400px\",\r\n              fontSize: \"1em\",\r\n              lineHeight: \"1.5\",\r\n            }}\r\n          >\r\n            {message.content.split(\"\\n\").map((line, idx) => {\r\n              const parts = [];\r\n              let remaining = line;\r\n              let keyIndex = 0;\r\n\r\n              while (remaining.length > 0) {\r\n                const linkMatch = remaining.match(/\\[(.*?)\\]\\((http.*?)\\)/);\r\n                const boldMatch = remaining.match(/\\*\\*(.*?)\\*\\*/);\r\n\r\n                if (linkMatch && (!boldMatch || linkMatch.index < boldMatch.index)) {\r\n                  let href = linkMatch[2];\r\n                  if (!href.startsWith(\"http\")) {\r\n                    href = `${BACKEND_URL}${href.startsWith(\"/\") ? href : \"/\" + href}`;\r\n                  }\r\n                  if (\r\n                    (href.startsWith(`${BACKEND_URL}/api/files/`) || href.startsWith(\"/api/files/\")) &&\r\n                    accessToken &&\r\n                    !href.includes(\"token=\")\r\n                  ) {\r\n                    href += href.includes(\"?\") ? `&token=${accessToken}` : `?token=${accessToken}`;\r\n                  }\r\n                  parts.push(\r\n                    <span key={keyIndex++}>\r\n                      {remaining.slice(0, linkMatch.index)}\r\n                      <a\r\n                        href={href}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        style={{ color: \"#0645AD\", textDecoration: \"underline\" }}\r\n                      >\r\n                        {linkMatch[1]}\r\n                      </a>\r\n                    </span>\r\n                  );\r\n                  remaining = remaining.slice(linkMatch.index + linkMatch[0].length);\r\n                } else if (boldMatch) {\r\n                  parts.push(\r\n                    <span key={keyIndex++}>\r\n                      {remaining.slice(0, boldMatch.index)}\r\n                      <strong>{boldMatch[1]}</strong>\r\n                    </span>\r\n                  );\r\n                  remaining = remaining.slice(boldMatch.index + boldMatch[0].length);\r\n                } else {\r\n                  parts.push(<span key={keyIndex++}>{remaining}</span>);\r\n                  break;\r\n                }\r\n              }\r\n\r\n              return <div key={idx}>{parts}</div>;\r\n            })}\r\n            {message.tickets && (\r\n              <div style={{ marginTop: \"8px\" }}>\r\n                {message.tickets.map((ticket, idx) => (\r\n                  <div\r\n                    key={ticket.ticketNumber}\r\n                    style={{\r\n                      cursor: \"pointer\",\r\n                      padding: \"6px 4px\",\r\n                      borderBottom:\r\n                        idx !== message.tickets.length - 1 ? \"1px solid #eee\" : \"none\",\r\n                    }}\r\n                    onClick={() => handleTicketSelect(ticket.ticketNumber)}\r\n                  >\r\n                    {ticket.index}. <strong>{ticket.ticketNumber}</strong> — {ticket.title}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n          <div\r\n            className=\"message-time\"\r\n            style={{ fontSize: \"0.7em\", color: \"#666\", marginTop: \"6px\" }}\r\n          >\r\n            {formatTime(message.timestamp)}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    ));\r\n\r\n  return (\r\n    <div className=\"chat-container\" role=\"main\" aria-label=\"AI Agent Chatbot\">\r\n      <div className=\"chat-header\" style={{ position: \"relative\" }}>\r\n        <h1>ONLINE SOLUTIONS TECHNICAL SUPPORT</h1>\r\n        <p className=\"subtitle\">Technical Documentation Assistant</p>\r\n        {username && (\r\n          <p style={{ fontSize: \"0.9em\", color: \"#444\" }}>\r\n            Logged in as: <strong>{username}</strong>\r\n          </p>\r\n        )}\r\n        <div style={{ position: \"absolute\", top: 15, right: 15 }}>\r\n          <button\r\n            onClick={handleLogout}\r\n            style={{\r\n              padding: \"6px 12px\",\r\n              cursor: \"pointer\",\r\n              backgroundColor: \"#d9534f\",\r\n              border: \"none\",\r\n              borderRadius: 4,\r\n              color: \"white\",\r\n              fontWeight: \"bold\",\r\n            }}\r\n            aria-label=\"Logout\"\r\n            title=\"Logout\"\r\n          >\r\n            Logout\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* CHAT MESSAGES */}\r\n      <div className=\"chat-messages\" aria-live=\"polite\" aria-relevant=\"additions\">\r\n        {renderMessages()}\r\n        {(loading || verifying) && (\r\n          <div className=\"message bot typing\" aria-label=\"Analyzing\">\r\n            <div className=\"message-content\">\r\n              <div className=\"typing-indicator\" aria-hidden=\"true\">\r\n                <span></span>\r\n                <span></span>\r\n                <span></span>\r\n              </div>\r\n              <div className=\"message-text\">\r\n                {verifying ? \"Verifying organization...\" : \"Analyzing...\"}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n        <div ref={messagesEndRef} />\r\n      </div>\r\n\r\n      {error && (\r\n        <div\r\n          className=\"error-message\"\r\n          onClick={() => setError(\"\")}\r\n          style={{ cursor: \"pointer\" }}\r\n          role=\"alert\"\r\n          aria-live=\"assertive\"\r\n          tabIndex={0}\r\n        >\r\n          {error} (click to dismiss)\r\n        </div>\r\n      )}\r\n\r\n      <form className=\"chat-input-form\" onSubmit={handleSubmit} aria-label=\"Send message form\">\r\n        <div className=\"input-container\" style={{ display: \"flex\", alignItems: \"center\", width: \"100%\" }}>\r\n          <input\r\n            type=\"text\"\r\n            value={query}\r\n            onChange={onInputChange}\r\n            placeholder={\r\n              orgVerified\r\n                ? ticketStep > 0\r\n                  ? ticketQuestions[ticketStep - 1]\r\n                  : awaitingUnrelatedQueryResponse\r\n                  ? \"Answer 'yes' or 'no' to create a new ticket...\"\r\n                  : awaitingCloseConfirmation\r\n                  ? \"Answer 'yes' or 'no' to close the ticket...\"\r\n                  : \"Type your question here...\"\r\n                : \"Enter your organization name...\"\r\n            }\r\n            disabled={loading || verifying || (ticketStep === 1 && !query)}\r\n            autoFocus\r\n            aria-label=\"Chat input\"\r\n            style={{ flex: \"1\", width: \"100%\", padding: \"8px\", borderRadius: \"4px 0 0 4px\", border: \"1px solid #ccc\", margin: 0 }}\r\n            onKeyDown={(e) => {\r\n              if (e.key === \"Enter\" && !e.shiftKey) {\r\n                e.preventDefault();\r\n                handleSubmit(e);\r\n              }\r\n            }}\r\n          />\r\n          {ticketStep === 1 && (\r\n            <select\r\n              value={ticketData.productType}\r\n              onChange={handleProductTypeChange}\r\n              style={{\r\n                marginLeft: \"0\",\r\n                padding: \"6px\",\r\n                borderRadius: \"0\",\r\n                border: \"1px solid #ccc\",\r\n                borderLeft: \"none\",\r\n                fontSize: \"1em\",\r\n              }}\r\n              aria-label=\"Select product type\"\r\n            >\r\n              <option value=\"\" disabled>\r\n                Select a product type\r\n              </option>\r\n              {productTypeOptions.map((option) => (\r\n                <option key={option} value={option}>\r\n                  {option}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          )}\r\n          <button\r\n            type=\"submit\"\r\n            disabled={loading || !query.trim() || verifying || (ticketStep === 1 && !ticketData.productType)}\r\n            title={loading || verifying ? \"Please wait...\" : \"Send\"}\r\n            aria-label=\"Send message\"\r\n            style={{ padding: \"8px 12px\", borderRadius: \"0 4px 4px 0\", border: \"1px solid #ccc\", borderLeft: \"none\", backgroundColor: \"#4CAF50\", color: \"white\" }}\r\n          >\r\n            {loading || verifying ? <span className=\"spinner\" /> : \"📤\"}\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG,uBAAuB;AAE3C,eAAe,SAASC,IAAIA,CAAC;EAAEC;AAAM,CAAC,EAAE;EAAAC,EAAA;EACtC,MAAMC,WAAW,GAAGF,KAAK,IAAIG,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;;EAE3D;EACA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsB,IAAI,EAAEC,OAAO,CAAC,GAAGvB,QAAQ,CAAC,QAAQ,CAAC;EAC1C,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACkC,yBAAyB,EAAEC,4BAA4B,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACjF,MAAM,CAACoC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACsC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACwC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC0C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAACkD,8BAA8B,EAAEC,iCAAiC,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAC3F,MAAMoD,2BAA2B,GAAG,EAAE;EACtC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC;;EAG3D;EACA,MAAM,CAAC2D,UAAU,EAAEC,aAAa,CAAC,GAAG5D,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC;IAC3C+D,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACwE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAEpE,MAAM0E,eAAe,GAAG,CACtB,0DAA0D,EAC1D,gDAAgD,EAChD,sCAAsC,EACtC,kCAAkC,EAClC,2BAA2B,EAC3B,mCAAmC,EACnC,sCAAsC,CACvC;EAED,MAAMC,kBAAkB,GAAG,CAAC,QAAQ,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,CAAC;;EAEjF;EACA1E,SAAS,CAAC,MAAM;IAAA,IAAA2E,qBAAA;IACd,CAAAA,qBAAA,GAAAC,cAAc,CAACC,OAAO,cAAAF,qBAAA,uBAAtBA,qBAAA,CAAwBG,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAAClE,QAAQ,EAAEI,OAAO,EAAEE,KAAK,CAAC,CAAC;EAE9B,MAAMyD,cAAc,GAAG3E,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACAD,SAAS,CAAC,MAAM;IACdgF,KAAK,CAAC,GAAG5E,WAAW,wBAAwB,CAAC,CAC1C6E,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEG,IAAI,IAAKpE,iBAAiB,CAACoE,IAAI,CAACC,QAAQ,CAAC,CAAC,CAChDC,KAAK,CAAEC,GAAG,IAAK;MACdC,OAAO,CAACrE,KAAK,CAAC,kCAAkC,EAAEoE,GAAG,CAAC;MACtDvE,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAhB,SAAS,CAAC,MAAM;IACd,MAAMyF,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,MAAMC,aAAa,GAAGL,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC,KAAK,SAAS;IAEzD,IAAID,aAAa,EAAE;MACjB;MACAE,yBAAyB,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAhG,SAAS,CAAC,MAAM;IACd,IAAI,CAACQ,WAAW,EAAE;IAElB,MAAMiF,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,MAAMC,aAAa,GAAGL,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC,KAAK,SAAS;IAEzD,IAAID,aAAa,EAAE;MACjB,OAAO,CAAC;IACV;IAEAd,KAAK,CAAC,GAAG5E,WAAW,iBAAiB,EAAE;MACrC6F,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAE,UAAU1F,WAAW;MACtC;IACF,CAAC,CAAC,CACCyE,IAAI,CAAC,MAAOC,GAAG,IAAK;MACnB,IAAI,CAACA,GAAG,CAACiB,EAAE,EAAE;QACX,MAAMC,SAAS,GAAG,MAAMlB,GAAG,CAACmB,IAAI,CAAC,CAAC;QAClCb,OAAO,CAACrE,KAAK,CAAC,yBAAyB,EAAE+D,GAAG,CAACoB,MAAM,EAAEF,SAAS,CAAC;QAC/D,MAAM,IAAIG,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MACA,OAAOrB,GAAG,CAACC,IAAI,CAAC,CAAC;IACnB,CAAC,CAAC,CACDF,IAAI,CAAEG,IAAI,IAAK;MACd,MAAMoB,IAAI,GAAGpB,IAAI,CAACoB,IAAI,IAAIpB,IAAI,CAACvD,QAAQ,IAAIuD,IAAI,CAACqB,KAAK;MACrD,IAAI,CAACD,IAAI,EAAE,MAAM,IAAID,KAAK,CAAC,0BAA0B,CAAC;MAEtDzE,WAAW,CAAC0E,IAAI,CAAC;MACjB1F,WAAW,CAAC,CACV;QACE4F,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE,eAAeJ,IAAI,+DAA+D;QAC3FK,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;IACJ,CAAC,CAAC,CACDxB,KAAK,CAAEC,GAAG,IAAK;MACdC,OAAO,CAACrE,KAAK,CAAC,4BAA4B,EAAEoE,GAAG,CAACwB,OAAO,CAAC;MACxDtG,YAAY,CAACuG,UAAU,CAAC,QAAQ,CAAC;MACjCrB,MAAM,CAACC,QAAQ,CAACqB,IAAI,GAAG,OAAO;IAChC,CAAC,CAAC;EACN,CAAC,EAAE,CAACzG,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAM0G,aAAa,GAAIC,CAAC,IAAK;IAC3B,IAAIhG,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;IACvBR,QAAQ,CAACuG,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAIH,CAAC,IAAK;IACrC,MAAMI,YAAY,GAAGJ,CAAC,CAACC,MAAM,CAACC,KAAK;IACnCxD,aAAa,CAAC;MAAE,GAAGD,UAAU;MAAEE,WAAW,EAAEyD;IAAa,CAAC,CAAC;IAC3DzG,WAAW,CAAE0G,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;MACEd,EAAE,EAAEI,IAAI,CAACW,GAAG,CAAC,CAAC;MACdd,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,0BAA0BW,YAAY,EAAE;MACjDV,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC,CACF,CAAC;IACFnD,aAAa,CAACD,UAAU,GAAG,CAAC,CAAC;IAC7BgE,MAAM,CAACjD,eAAe,CAACf,UAAU,CAAC,CAAC;EACrC,CAAC;;EAED;EACA,MAAMiE,kBAAkB,GAAG,MAAOC,OAAO,IAAK;IAC5ChG,YAAY,CAAC,IAAI,CAAC;IAClBR,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMyG,QAAQ,GAAG,MAAM7C,KAAK,CAAC,GAAG5E,WAAW,2BAA2B,EAAE;QACtE0H,MAAM,EAAE,MAAM;QACd7B,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAU1F,WAAW;QACtC,CAAC;QACDuH,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEC,YAAY,EAAEN;QAAQ,CAAC;MAChD,CAAC,CAAC;MAEF,MAAMxC,IAAI,GAAG,MAAMyC,QAAQ,CAAC1C,IAAI,CAAC,CAAC;MAElC,IAAI0C,QAAQ,CAAC1B,EAAE,IAAIf,IAAI,CAACkB,MAAM,KAAK,UAAU,EAAE;QAC7C5E,cAAc,CAAC,IAAI,CAAC;QACpBI,WAAW,CAACsD,IAAI,CAACoB,IAAI,IAAI3E,QAAQ,CAAC;QAElCf,WAAW,CAAE0G,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACEd,EAAE,EAAEI,IAAI,CAACW,GAAG,CAAC,CAAC;UACdd,IAAI,EAAE,KAAK;UACXC,OAAO,EAAExB,IAAI,CAAC2B,OAAO,IAAI,0BAA0B;UACnDF,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;QAEF,MAAMqB,mBAAmB,CAAC,CAAC;MAC7B,CAAC,MAAM;QACL,MAAMC,OAAO,GAAGzC,MAAM,CAACC,QAAQ,CAACyC,MAAM;QACtC,MAAMC,UAAU,GAAG,kBAAkBF,OAAO,WAAW;QAEvDhH,QAAQ,CAACgE,IAAI,CAAC2B,OAAO,IAAI,0BAA0B,CAAC;QACpDjG,WAAW,CAAE0G,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACEd,EAAE,EAAEI,IAAI,CAACW,GAAG,CAAC,CAAC;UACdd,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,sFAAsF0B,UAAU,qBAAqB;UAC9HzB,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;QAEFrG,YAAY,CAACuG,UAAU,CAAC,QAAQ,CAAC;MACnC;IACF,CAAC,CAAC,OAAOzB,GAAG,EAAE;MACZnE,QAAQ,CAAC,oCAAoC,CAAC;MAC9CN,WAAW,CAAE0G,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QACEd,EAAE,EAAEI,IAAI,CAACW,GAAG,CAAC,CAAC;QACdd,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE,qEAAqE;QAC9EC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;MACFtB,OAAO,CAACrE,KAAK,CAAC,qBAAqB,EAAEoE,GAAG,CAAC;IAC3C,CAAC,SAAS;MACR3D,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,eAAeuG,mBAAmBA,CAAA,EAAG;IACnC,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAM7C,KAAK,CAAC,GAAG5E,WAAW,uBAAuB,EAAE;QAClE6F,OAAO,EAAE;UACPC,aAAa,EAAE,UAAU1F,WAAW;QACtC;MACF,CAAC,CAAC;MACF,MAAM4E,IAAI,GAAG,MAAMyC,QAAQ,CAAC1C,IAAI,CAAC,CAAC;MAElC,IAAI0C,QAAQ,CAAC1B,EAAE,EAAE;QACf7D,iBAAiB,CAAC8C,IAAI,CAAChC,OAAO,IAAI,EAAE,CAAC;QACrC,IAAIgC,IAAI,CAAChC,OAAO,IAAIgC,IAAI,CAAChC,OAAO,CAACmF,MAAM,GAAG,CAAC,EAAE;UAC3C/F,wBAAwB,CAAC,IAAI,CAAC;UAC9BkF,MAAM,CACJ,YAAYtC,IAAI,CAAChC,OAAO,CAACmF,MAAM,wEACjC,CAAC;QACH,CAAC,MAAM;UACLvG,iBAAiB,CAAC,IAAI,CAAC;UACvB0F,MAAM,CAAC,8EAA8E,CAAC;QACxF;MACF,CAAC,MAAM;QACLpF,iBAAiB,CAAC,EAAE,CAAC;QACrBN,iBAAiB,CAAC,IAAI,CAAC;QACvB0F,MAAM,CAAC,uFAAuF,CAAC;MACjG;IACF,CAAC,CAAC,OAAOnC,GAAG,EAAE;MACZC,OAAO,CAACrE,KAAK,CAAC,iCAAiC,EAAEoE,GAAG,CAAC;MACrDjD,iBAAiB,CAAC,EAAE,CAAC;MACrBN,iBAAiB,CAAC,IAAI,CAAC;MACvB0F,MAAM,CAAC,oFAAoF,CAAC;IAC9F;EACF;;EAEA;EACA,MAAMc,YAAY,GAAG,MAAOC,eAAe,IAAK;IAC9CvH,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,MAAMsH,iBAAiB,GAAG,CAAC,QAAQ,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,CAAC;IAChF,MAAMC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACJ,eAAe,CAAC,CAACK,KAAK,CACnDC,CAAC,IAAKA,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,KAAK,EAC3B,CAAC;IACD,IAAI,CAACL,SAAS,EAAE;MACd7H,WAAW,CAAE0G,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QACEd,EAAE,EAAEI,IAAI,CAACW,GAAG,CAAC,CAAC;QACdd,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE,oEAAoE;QAC7EC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;MACF5F,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IACA,IAAI,CAACwH,iBAAiB,CAACO,QAAQ,CAACR,eAAe,CAAC3E,WAAW,CAAC,EAAE;MAC5DhD,WAAW,CAAE0G,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QACEd,EAAE,EAAEI,IAAI,CAACW,GAAG,CAAC,CAAC;QACdd,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE,yFAAyF;QAClGC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;MACF5F,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAI;MACF,MAAM2G,QAAQ,GAAG,MAAM7C,KAAK,CAAC,GAAG5E,WAAW,qBAAqB,EAAE;QAChE0H,MAAM,EAAE,MAAM;QACd7B,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAU1F,WAAW;QACtC,CAAC;QACDuH,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBiB,YAAY,EAAET,eAAe,CAAC3E,WAAW;UACzCqF,cAAc,EAAEV,eAAe,CAAC1E,aAAa;UAC7CqF,gBAAgB,EAAEX,eAAe,CAACzE,cAAc;UAChDqF,YAAY,EAAEZ,eAAe,CAACxE,WAAW;UACzCC,KAAK,EAAEuE,eAAe,CAACvE,KAAK;UAC5BoF,SAAS,EAAEb,eAAe,CAACtE,QAAQ;UACnCoF,gBAAgB,EAAEd,eAAe,CAACrE;QACpC,CAAC;MACH,CAAC,CAAC;MAEF,MAAMgB,IAAI,GAAG,MAAMyC,QAAQ,CAAC1C,IAAI,CAAC,CAAC;MAElC,IAAI0C,QAAQ,CAAC1B,EAAE,EAAE;QACf3B,sBAAsB,CAACY,IAAI,CAACoE,aAAa,CAAC;QAC1C5G,eAAe,CAACwC,IAAI,CAACoE,aAAa,CAAC;QACnClF,6BAA6B,CAAC,IAAI,CAAC;QACnCxD,WAAW,CAAE0G,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACEd,EAAE,EAAEI,IAAI,CAACW,GAAG,CAAC,CAAC;UACdd,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,4FAA4FxB,IAAI,CAACoE,aAAa,2DAA2D;UAClL3C,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;MACJ,CAAC,MAAM;QACLhG,WAAW,CAAE0G,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACEd,EAAE,EAAEI,IAAI,CAACW,GAAG,CAAC,CAAC;UACdd,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,8BAA8BoB,IAAI,CAACC,SAAS,CAAC7C,IAAI,CAACqE,MAAM,IAAIrE,IAAI,CAAC2B,OAAO,CAAC,EAAE;UACpFF,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;MACJ;IACF,CAAC,CAAC,OAAOvB,GAAG,EAAE;MACZzE,WAAW,CAAE0G,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QACEd,EAAE,EAAEI,IAAI,CAACW,GAAG,CAAC,CAAC;QACdd,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE,0CAA0CrB,GAAG,CAACwB,OAAO,EAAE;QAChEF,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;IACJ,CAAC,SAAS;MACR5F,UAAU,CAAC,KAAK,CAAC;MACjByC,aAAa,CAAC,CAAC,CAAC;MAChB3B,iBAAiB,CAAC,KAAK,CAAC;MACxB6B,aAAa,CAAC;QACZC,WAAW,EAAE,EAAE;QACfC,aAAa,EAAE,EAAE;QACjBC,cAAc,EAAE,EAAE;QAClBC,WAAW,EAAE,EAAE;QACfC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,eAAe,EAAE;MACnB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,SAASsD,MAAMA,CAACrB,IAAI,EAAE;IACpBvF,WAAW,CAAE0G,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;MAAEd,EAAE,EAAEI,IAAI,CAACW,GAAG,CAAC,CAAC;MAAEd,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAEP,IAAI;MAAEQ,SAAS,EAAE,IAAIC,IAAI,CAAC;IAAE,CAAC,CACtE,CAAC;EACJ;;EAEA;EACA,MAAM4C,kBAAkB,GAAG,MAAOC,YAAY,IAAK;IACjDjH,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,eAAe,CAAC+G,YAAY,CAAC;IAC7BnF,sBAAsB,CAACmF,YAAY,CAAC;IACpCpG,cAAc,CAAC,KAAK,CAAC;IAErB,IAAI;MACF,MAAMqG,UAAU,GAAG,MAAM5E,KAAK,CAAC,GAAG5E,WAAW,sBAAsB,EAAE;QACnE0H,MAAM,EAAE,MAAM;QACd7B,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAU1F,WAAW;QACtC,CAAC;QACDuH,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEuB,aAAa,EAAEG;QAAa,CAAC;MACtD,CAAC,CAAC;MAEF,MAAME,WAAW,GAAG,MAAMD,UAAU,CAACzE,IAAI,CAAC,CAAC;MAE3C,IAAIyE,UAAU,CAACzD,EAAE,EAAE;QACjBuB,MAAM,CACJ,wBAAwBiC,YAAY,UAAU,GAC5C,0BAA0BE,WAAW,CAACC,eAAe,IAAID,WAAW,CAACE,mBAAmB,MAAM,GAC9F,0BAA0BF,WAAW,CAACG,gBAAgB,IAAI,kBAAkB,MAAM,GAClF,yCACJ,CAAC;MACH,CAAC,MAAM;QACLtC,MAAM,CAAC,mCAAmC,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOnC,GAAG,EAAE;MACZmC,MAAM,CAAC,gDAAgD,CAAC;IAC1D;EACF,CAAC;;EAED;EACF,eAAeuC,YAAYA,CAAC9C,CAAC,EAAE;IAC7BA,CAAC,CAAC+C,cAAc,CAAC,CAAC;IAClB,IAAI,CAACvJ,KAAK,CAACqI,IAAI,CAAC,CAAC,IAAI/H,OAAO,IAAIU,SAAS,EAAE;IAE3C,MAAMwI,YAAY,GAAGxJ,KAAK,CAACqI,IAAI,CAAC,CAAC,CAACoB,WAAW,CAAC,CAAC;;IAE/C;IACA,IACEzH,YAAY;IAAe;IAC3Be,UAAU,KAAK,CAAC;IAAW;IAC3B,CAACW,0BAA0B;IAAI;IAC/Bb,gBAAgB,IAAI,CAAC,CAAM;IAAA,EAC3B;MACAkE,MAAM,CACJ,sNACF,CAAC;MACD9G,QAAQ,CAAC,EAAE,CAAC;MACZ;IACF;IACA;;IAEA,IAAIiC,aAAa,EAAE;MACjB,IAAIE,oBAAoB,IAAII,2BAA2B,EAAE;QACvDuE,MAAM,CACJ,+GACF,CAAC;QACD9G,QAAQ,CAAC,EAAE,CAAC;QACZ;MACF,CAAC,MAAM;QACLoC,uBAAuB,CAAEqH,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;MACvC;IACF;IAEA,MAAMC,OAAO,GAAG;MACd5D,EAAE,EAAEI,IAAI,CAACW,GAAG,CAAC,CAAC;MACdd,IAAI,EAAE,MAAM;MACZC,OAAO,EAAEjG,KAAK,CAACqI,IAAI,CAAC,CAAC;MACrBnC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IACDhG,WAAW,CAAE0G,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAE8C,OAAO,CAAC,CAAC;IACzC1J,QAAQ,CAAC,EAAE,CAAC;IACZQ,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAImB,qBAAqB,EAAE;MACzBC,wBAAwB,CAAC,KAAK,CAAC;MAE/B,IAAI2H,YAAY,KAAK,KAAK,EAAE;QAC1BzH,uBAAuB,CAAC,IAAI,CAAC;QAC7B5B,WAAW,CAAE0G,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACEd,EAAE,EAAEI,IAAI,CAACW,GAAG,CAAC,CAAC;UACdd,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,uCAAuC;UAChDC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;UACrB1D,OAAO,EAAEf,cAAc,CAACkI,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,MAAM;YACrCC,KAAK,EAAED,CAAC,GAAG,CAAC;YACZd,YAAY,EAAEa,CAAC,CAAChB,aAAa;YAC7BmB,KAAK,EAAEH,CAAC,CAACG,KAAK,IAAIH,CAAC,CAACI,WAAW,IAAI;UACrC,CAAC,CAAC;QACJ,CAAC,CACF,CAAC;MACJ,CAAC,MAAM,IAAIT,YAAY,KAAK,IAAI,EAAE;QAChCnI,iBAAiB,CAAC,IAAI,CAAC;QACvB0F,MAAM,CAAC,iDAAiD,CAAC;MAC3D,CAAC,MAAM;QACLA,MAAM,CAAC,sEAAsE,CAAC;QAC9ElF,wBAAwB,CAAC,IAAI,CAAC;MAChC;MACA;IACF;IAEA,IAAIC,oBAAoB,EAAE;MACxB,MAAMoI,MAAM,GAAGxI,cAAc,CAACyI,IAAI,CAChC,CAACN,CAAC,EAAEO,GAAG,KACLZ,YAAY,KAAKa,MAAM,CAACD,GAAG,GAAG,CAAC,CAAC,IAChCZ,YAAY,CAAClB,QAAQ,CAACuB,CAAC,CAAChB,aAAa,CAACY,WAAW,CAAC,CAAC,CACvD,CAAC;MAED,IAAI,CAACS,MAAM,EAAE;QACXnD,MAAM,CAAC,gDAAgD,CAAC;QACxD;MACF;MAEA,MAAMgC,kBAAkB,CAACmB,MAAM,CAACrB,aAAa,CAAC;MAC9C;IACF;IAEA,IAAI,CAAC/H,WAAW,EAAE;MAChB,MAAMkG,kBAAkB,CAACwC,YAAY,CAAC;MACtC;IACF;IAEA,IAAIpI,cAAc,EAAE;MAClB,IAAIoI,YAAY,KAAK,KAAK,EAAE;QAC1BnI,iBAAiB,CAAC,KAAK,CAAC;QACxB2B,aAAa,CAAC,CAAC,CAAC;QAChB+D,MAAM,CAACjD,eAAe,CAAC,CAAC,CAAC,CAAC;QAC1B3B,gBAAgB,CAAC,KAAK,CAAC;QACvBE,uBAAuB,CAAC,CAAC,CAAC;;QAE1B;QACAS,mBAAmB,CAAC,CAAC,CAAC;QACtB;MAEF,CAAC,MAAM,IAAI0G,YAAY,KAAK,IAAI,EAAE;QAChCnI,iBAAiB,CAAC,KAAK,CAAC;QACxB0F,MAAM,CAAC,6DAA6D,CAAC;QACrE5E,gBAAgB,CAAC,IAAI,CAAC;QACtBE,uBAAuB,CAAC,CAAC,CAAC;MAC5B,CAAC,MAAM;QACL0E,MAAM,CAAC,qEAAqE,CAAC;MAC/E;MACA;IACF;IAEA,IAAIzE,8BAA8B,EAAE;MAClCC,iCAAiC,CAAC,KAAK,CAAC;MACxC,IAAIiH,YAAY,KAAK,KAAK,EAAE;QAC1BxG,aAAa,CAAC,CAAC,CAAC;QAChBf,eAAe,CAAC,IAAI,CAAC;QACrB4B,sBAAsB,CAAC,IAAI,CAAC;QAC5BkD,MAAM,CAACjD,eAAe,CAAC,CAAC,CAAC,CAAC;;QAE1B;QACAhB,mBAAmB,CAAC,CAAC,CAAC;QACtB;MAEF,CAAC,MAAM,IAAI0G,YAAY,KAAK,IAAI,EAAE;QAChCjI,4BAA4B,CAAC,IAAI,CAAC;QAClCwF,MAAM,CAAC,uCAAuC,CAAC;MACjD,CAAC,MAAM;QACLxE,iCAAiC,CAAC,IAAI,CAAC;QACvCwE,MAAM,CAAC,kEAAkE,CAAC;MAC5E;MACA;IACF;IAEA,IAAIhE,UAAU,GAAG,CAAC,IAAIA,UAAU,IAAIe,eAAe,CAAC8D,MAAM,EAAE;MAC1D,IAAI7E,UAAU,KAAK,CAAC,EAAE;QACpB,MAAM6D,YAAY,GAAG5G,KAAK,CAACqI,IAAI,CAAC,CAAC;QACjC,IAAItE,kBAAkB,CAACuE,QAAQ,CAAC1B,YAAY,CAAC,EAAE;UAC7C1D,aAAa,CAAC;YAAE,GAAGD,UAAU;YAAEE,WAAW,EAAEyD;UAAa,CAAC,CAAC;UAC3DzG,WAAW,CAAE0G,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;YACEd,EAAE,EAAEI,IAAI,CAACW,GAAG,CAAC,CAAC;YACdd,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE,0BAA0BW,YAAY,EAAE;YACjDV,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,CACF,CAAC;UACFnD,aAAa,CAACD,UAAU,GAAG,CAAC,CAAC;UAC7BgE,MAAM,CAACjD,eAAe,CAACf,UAAU,CAAC,CAAC;QACrC,CAAC,MAAM;UACLgE,MAAM,CACJ,2FACF,CAAC;QACH;QACA;MACF;MACA,MAAMuD,IAAI,GAAG,CACX,eAAe,EACf,gBAAgB,EAChB,aAAa,EACb,OAAO,EACP,UAAU,EACV,iBAAiB,CAClB;MACD,MAAMC,YAAY,GAAGD,IAAI,CAACvH,UAAU,GAAG,CAAC,CAAC;MACzC,MAAMyH,iBAAiB,GAAG;QAAE,GAAGvH,UAAU;QAAE,CAACsH,YAAY,GAAGvK,KAAK,CAACqI,IAAI,CAAC;MAAE,CAAC;MACzEnF,aAAa,CAACsH,iBAAiB,CAAC;MAEhC,IAAIzH,UAAU,GAAGe,eAAe,CAAC8D,MAAM,EAAE;QACvC5E,aAAa,CAACD,UAAU,GAAG,CAAC,CAAC;QAC7BgE,MAAM,CAACjD,eAAe,CAACf,UAAU,CAAC,CAAC;MACrC,CAAC,MAAM;QACL,MAAM8E,YAAY,CAAC2C,iBAAiB,CAAC;MACvC;MACA;IACF;IAEA,IAAI9G,0BAA0B,IAAIE,mBAAmB,EAAE;MACrDD,6BAA6B,CAAC,KAAK,CAAC;MACpCpD,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAI;QAAA,IAAAkK,eAAA;QACF,MAAMC,OAAO,GAAG,MAAMrG,KAAK,CAAC,GAAG5E,WAAW,+BAA+B,EAAE;UACzE0H,MAAM,EAAE,MAAM;UACd7B,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClCC,aAAa,EAAE,UAAU1F,WAAW;UACtC,CAAC;UACDuH,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBuB,aAAa,EAAEjF,mBAAmB;YAClCwF,mBAAmB,EAAEO,OAAO,CAAC1D;UAC/B,CAAC;QACH,CAAC,CAAC;QAEF,MAAM0E,QAAQ,GAAG,MAAMD,OAAO,CAAClG,IAAI,CAAC,CAAC;QACrC,IAAI,CAACkG,OAAO,CAAClF,EAAE,EAAE;UACf,MAAM,IAAII,KAAK,CAAC+E,QAAQ,CAACnK,KAAK,IAAI,qCAAqC,CAAC;QAC1E;QAEA,IAAIoK,UAAU,GAAGD,QAAQ,CAACE,MAAM,IAAI,sCAAsC;QAE1E,MAAMC,QAAQ,IAAAL,eAAA,GAAGE,QAAQ,CAACI,KAAK,cAAAN,eAAA,cAAAA,eAAA,GAAI,EAAE,CAAC,CAAC;QACvC,MAAMO,UAAU,GAAGF,QAAQ,CAACG,MAAM,CAAEC,CAAC,IAAK;UACxC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;YACzB,OAAOA,CAAC,CAAC7C,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC6C,CAAC,CAACzB,WAAW,CAAC,CAAC,CAAC0B,UAAU,CAAC,MAAM,CAAC;UAC/D;UACA,IAAI,OAAOD,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,CAACE,QAAQ,EAAE;YACrD,OACEF,CAAC,CAACE,QAAQ,CAAC/C,IAAI,CAAC,CAAC,KAAK,EAAE,IACxB,CAAC6C,CAAC,CAACE,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC0B,UAAU,CAAC,MAAM,CAAC;UAEhD;UACA,OAAO,KAAK;QACd,CAAC,CAAC;QAEF,IAAIH,UAAU,CAACpD,MAAM,GAAG,CAAC,EAAE;UACzB/G,eAAe,CACbmK,UAAU,CAACpB,GAAG,CAAEsB,CAAC,IAAK;YACpB,MAAME,QAAQ,GAAG,OAAOF,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGA,CAAC,CAACE,QAAQ;YACvD,MAAMC,GAAG,GACP,OAAOH,CAAC,KAAK,QAAQ,IAAI,CAACA,CAAC,CAACG,GAAG,IAAI,CAACH,CAAC,CAACG,GAAG,CAACF,UAAU,CAAC,MAAM,CAAC,GACxD,GAAG1L,WAAW,cAAc6L,kBAAkB,CAC5CF,QACF,CAAC,UAAUvL,WAAW,EAAE,GACxB,GAAGqL,CAAC,CAACG,GAAG,GAAGH,CAAC,CAACG,GAAG,CAAC/C,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,SAASzI,WAAW,EAAE;YACtE,OAAO;cAAE0L,WAAW,EAAEH,QAAQ;cAAEC;YAAI,CAAC;UACvC,CAAC,CACH,CAAC;UACDT,UAAU,IAAI,qEAAqE;QACrF,CAAC,MAAM;UACL/J,eAAe,CAAC,IAAI,CAAC;QACvB;QAEAV,WAAW,CAAE0G,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACEd,EAAE,EAAEI,IAAI,CAACW,GAAG,CAAC,CAAC;UACdd,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE2E,UAAU;UACnB1E,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;QAEF1E,uBAAuB,CAAC,IAAI,CAAC;MAC/B,CAAC,CAAC,OAAOmD,GAAG,EAAE;QACZzE,WAAW,CAAE0G,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACEd,EAAE,EAAEI,IAAI,CAACW,GAAG,CAAC,CAAC;UACdd,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,2CAA2CrB,GAAG,CAACwB,OAAO,EAAE;UACjEF,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;MACJ,CAAC,SAAS;QACR5F,UAAU,CAAC,KAAK,CAAC;MACnB;MACA;IACF;IAEA,IAAIK,YAAY,KAAK4I,YAAY,KAAK,KAAK,IAAIA,YAAY,KAAK,IAAI,CAAC,EAAE;MACrE,MAAMgC,YAAY,GAAG,EAAE;MAEvB,IAAIhC,YAAY,KAAK,KAAK,EAAE;QAC1B,MAAMiC,aAAa,GAAG7K,YAAY,CAC/BgJ,GAAG,CAAC,CAACsB,CAAC,EAAEd,GAAG,KAAK,GAAGA,GAAG,GAAG,CAAC,MAAMc,CAAC,CAACK,WAAW,KAAKL,CAAC,CAACG,GAAG,GAAG,CAAC,CAC3DK,IAAI,CAAC,IAAI,CAAC;QAEbF,YAAY,CAACG,IAAI,CAAC;UAChB5F,EAAE,EAAEI,IAAI,CAACW,GAAG,CAAC,CAAC,GAAG,CAAC;UAClBd,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,qCAAqCwF,aAAa,EAAE;UAC7DvF,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CAAC;MACJ,CAAC,MAAM;QACLqF,YAAY,CAACG,IAAI,CAAC;UAChB5F,EAAE,EAAEI,IAAI,CAACW,GAAG,CAAC,CAAC,GAAG,CAAC;UAClBd,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,iCAAiC;UAC1CC,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CAAC;MACJ;MAEA,IAAInE,YAAY,KAAK,IAAI,EAAE;QACzBwJ,YAAY,CAACG,IAAI,CAAC;UAChB5F,EAAE,EAAEI,IAAI,CAACW,GAAG,CAAC,CAAC,GAAG,CAAC;UAClBd,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,yCAAyC;UAClDC,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CAAC;QACF1E,uBAAuB,CAAC,IAAI,CAAC;MAC/B;MAEAtB,WAAW,CAAE0G,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAE,GAAG2E,YAAY,CAAC,CAAC;MACjD3K,eAAe,CAAC,IAAI,CAAC;MACrB;IACF;IAEA,IAAIW,oBAAoB,EAAE;MACxB,IAAIgI,YAAY,KAAK,IAAI,EAAE;QACzB/H,uBAAuB,CAAC,KAAK,CAAC;QAC9BF,4BAA4B,CAAC,IAAI,CAAC;QAClCwF,MAAM,CAAC,uCAAuC,CAAC;MACjD,CAAC,MAAM,IAAIyC,YAAY,KAAK,KAAK,EAAE;QACjC/H,uBAAuB,CAAC,KAAK,CAAC;QAC9BsF,MAAM,CAAC,wCAAwC,CAAC;MAClD,CAAC,MAAM;QACLA,MAAM,CAAC,6DAA6D,CAAC;MACvE;MACA;IACF;IAEA,IAAIzF,yBAAyB,EAAE;MAC7B,IAAIkI,YAAY,KAAK,KAAK,EAAE;QAC1BjI,4BAA4B,CAAC,KAAK,CAAC;QACnChB,UAAU,CAAC,IAAI,CAAC;QAChB,IAAI;UACF,MAAM2G,QAAQ,GAAG,MAAM7C,KAAK,CAAC,GAAG5E,WAAW,4BAA4B,EAAE;YACvE0H,MAAM,EAAE,MAAM;YACd7B,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClCC,aAAa,EAAE,UAAU1F,WAAW;YACtC,CAAC;YACDuH,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBuB,aAAa,EAAEjF,mBAAmB;cAClC+B,MAAM,EAAE;YACV,CAAC;UACH,CAAC,CAAC;UACF,MAAMlB,IAAI,GAAG,MAAMyC,QAAQ,CAAC1C,IAAI,CAAC,CAAC;UAElC,IAAI0C,QAAQ,CAAC1B,EAAE,EAAE;YACfvD,eAAe,CAAC,IAAI,CAAC;YACrB4B,sBAAsB,CAAC,IAAI,CAAC;YAC5BX,aAAa,CAAC;cACZC,WAAW,EAAE,EAAE;cACfC,aAAa,EAAE,EAAE;cACjBC,cAAc,EAAE,EAAE;cAClBC,WAAW,EAAE,EAAE;cACfC,KAAK,EAAE,EAAE;cACTC,QAAQ,EAAE,EAAE;cACZC,eAAe,EAAE;YACnB,CAAC,CAAC;YAEFsD,MAAM,CAAC,YAAYnD,mBAAmB,8BAA8B,CAAC;;YAErE;YACAd,mBAAmB,CAAC,CAAC,CAAC;YACtB;UAEF,CAAC,MAAM;YACL,MAAM,IAAI8C,KAAK,CAACnB,IAAI,CAACjE,KAAK,IAAI,yBAAyB,CAAC;UAC1D;QACF,CAAC,CAAC,OAAOoE,GAAG,EAAE;UACZmC,MAAM,CAAC,2BAA2BnC,GAAG,CAACwB,OAAO,EAAE,CAAC;QAClD,CAAC,SAAS;UACR7F,UAAU,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,MAAM,IAAIiJ,YAAY,KAAK,IAAI,EAAE;QAChCjI,4BAA4B,CAAC,KAAK,CAAC;QACnCwF,MAAM,CAAC,gCAAgC,CAAC;MAC1C,CAAC,MAAM;QACLA,MAAM,CAAC,2DAA2D,CAAC;MACrE;MACA;IACF;IAEAxG,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMqL,WAAW,GAAG1L,QAAQ,CACzB+K,MAAM,CAAEY,CAAC,IAAKA,CAAC,CAAC7F,IAAI,KAAK,MAAM,IAAI6F,CAAC,CAAC7F,IAAI,KAAK,KAAK,CAAC,CACpD4D,GAAG,CAAEiC,CAAC,IAAK,GAAGA,CAAC,CAAC7F,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,KAAK,KAAK6F,CAAC,CAAC5F,OAAO,EAAE,CAAC,CACjEyF,IAAI,CAAC,IAAI,CAAC;MAEb,MAAMI,WAAW,GAAG1L,cAAc,GAC9BA,cAAc,CACX2L,OAAO,CAAC,gBAAgB,EAAE,wBAAwB,CAAC,CACnDA,OAAO,CAAC,gBAAgB,EAAEH,WAAW,CAAC,CACtCG,OAAO,CAAC,SAAS,EAAE/L,KAAK,CAACqI,IAAI,CAAC,CAAC,CAAC,GACnCrI,KAAK,CAACqI,IAAI,CAAC,CAAC;MAEhB,MAAMnB,QAAQ,GAAG,MAAM7C,KAAK,CAAC,GAAG5E,WAAW,YAAY,EAAE;QACvD0H,MAAM,EAAE,MAAM;QACd7B,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAU1F,WAAW;QACtC,CAAC;QACDuH,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBtH,KAAK,EAAE8L,WAAW;UAClBE,WAAW,EAAE,CAAC,CAAChK,YAAY;UAC3BiK,SAAS,EAAEjK,YAAY;UACvBkK,KAAK,EAAE5J,8BAA8B,GAAG,iBAAiB,GAAG;QAC9D,CAAC;MACH,CAAC,CAAC;MAEF,MAAMmC,IAAI,GAAG,MAAMyC,QAAQ,CAAC1C,IAAI,CAAC,CAAC;MAElC,IAAI0C,QAAQ,CAAC1B,EAAE,EAAE;QAAA,IAAA2G,WAAA;QACf,IAAIvB,UAAU,GAAGnG,IAAI,CAACoG,MAAM,IAAI,GAAG;QAEnC,IAAIpG,IAAI,CAACyH,KAAK,KAAK,iBAAiB,EAAE;UACpC3J,iCAAiC,CAAC,IAAI,CAAC;QACzC,CAAC,MAAM,IAAIkC,IAAI,CAACyH,KAAK,KAAK,mBAAmB,EAAE;UAC7ClJ,aAAa,CAAC,CAAC,CAAC;UAChBf,eAAe,CAAC,IAAI,CAAC;UACrB4B,sBAAsB,CAAC,IAAI,CAAC;UAC5BkD,MAAM,CAACjD,eAAe,CAAC,CAAC,CAAC,CAAC;;UAE1B;UACAhB,mBAAmB,CAAC,CAAC,CAAC;UACtB;UACA;QACF;QAEA,MAAMgI,QAAQ,IAAAqB,WAAA,GAAG1H,IAAI,CAACsG,KAAK,cAAAoB,WAAA,cAAAA,WAAA,GAAI,EAAE;QACjC,MAAMnB,UAAU,GAAGF,QAAQ,CAACG,MAAM,CAAEC,CAAC,IAAK;UACxC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;YACzB,OAAOA,CAAC,CAAC7C,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC6C,CAAC,CAACzB,WAAW,CAAC,CAAC,CAAC0B,UAAU,CAAC,MAAM,CAAC;UAC/D;UACA,IAAI,OAAOD,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,CAACE,QAAQ,EAAE;YACrD,OACEF,CAAC,CAACE,QAAQ,CAAC/C,IAAI,CAAC,CAAC,KAAK,EAAE,IACxB,CAAC6C,CAAC,CAACE,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC0B,UAAU,CAAC,MAAM,CAAC;UAEhD;UACA,OAAO,KAAK;QACd,CAAC,CAAC;QAEF,IAAIH,UAAU,CAACpD,MAAM,GAAG,CAAC,EAAE;UACzB/G,eAAe,CACbmK,UAAU,CAACpB,GAAG,CAAEsB,CAAC,IAAK;YACpB,MAAME,QAAQ,GAAG,OAAOF,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGA,CAAC,CAACE,QAAQ;YACvD,MAAMC,GAAG,GACP,OAAOH,CAAC,KAAK,QAAQ,IAAI,CAACA,CAAC,CAACG,GAAG,IAAI,CAACH,CAAC,CAACG,GAAG,CAACF,UAAU,CAAC,MAAM,CAAC,GACxD,GAAG1L,WAAW,cAAc6L,kBAAkB,CAACF,QAAQ,CAAC,UAAUvL,WAAW,EAAE,GAC/E,GAAGqL,CAAC,CAACG,GAAG,GAAGH,CAAC,CAACG,GAAG,CAAC/C,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,SAASzI,WAAW,EAAE;YACtE,OAAO;cACL0L,WAAW,EAAEH,QAAQ;cACrBC,GAAG,EAAEA;YACP,CAAC;UACH,CAAC,CACH,CAAC;UACD,IAAI,CAACT,UAAU,CAACnB,WAAW,CAAC,CAAC,CAACnB,QAAQ,CAAC,8BAA8B,CAAC,EAAE;YACtEsC,UAAU,IAAI,qEAAqE;UACrF;QACF,CAAC,MAAM;UACL/J,eAAe,CAAC,IAAI,CAAC;QACvB;QAEAV,WAAW,CAAE0G,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACEd,EAAE,EAAEI,IAAI,CAACW,GAAG,CAAC,CAAC;UACdd,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE2E,UAAU;UACnB1E,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;;QAEF;QACA,IAAInE,YAAY,IAAIe,UAAU,KAAK,CAAC,EAAE;UACpCD,mBAAmB,CAAE+D,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;QACzC;QACA;;QAEA,IAAIpC,IAAI,CAACyH,KAAK,KAAK,aAAa,EAAE;UAChC3K,4BAA4B,CAAC,IAAI,CAAC;QACpC;MACF,CAAC,MAAM;QACLd,QAAQ,CAACgE,IAAI,CAACjE,KAAK,IAAI,0BAA0B,CAAC;MACpD;IACF,CAAC,CAAC,OAAOoE,GAAG,EAAE;MACZnE,QAAQ,CAAC,iBAAiB,GAAGmE,GAAG,CAACwB,OAAO,CAAC;IAC3C,CAAC,SAAS;MACR7F,UAAU,CAAC,KAAK,CAAC;IACnB;EACF;;EAGE;EACA,SAAS6L,YAAYA,CAAA,EAAG;IACtBtM,YAAY,CAACuG,UAAU,CAAC,QAAQ,CAAC;IACjCvG,YAAY,CAACuG,UAAU,CAAC,SAAS,CAAC;IAClCvG,YAAY,CAACuG,UAAU,CAAC,UAAU,CAAC;IACnCrB,MAAM,CAACC,QAAQ,CAACqB,IAAI,GAAG,OAAO;EAChC;;EAEA;EACA,MAAM+F,UAAU,GAAInG,SAAS,IAAK;IAChC,OAAOA,SAAS,CAACoG,kBAAkB,CAAC,EAAE,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC,CAAC;EACjF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KACrBvM,QAAQ,CAAC0J,GAAG,CAAExD,OAAO,iBACnB5G,OAAA;IAEEkN,SAAS,EAAE,WAAWtG,OAAO,CAACJ,IAAI,EAAG;IACrC2G,KAAK,EAAE;MAAEC,SAAS,EAAExG,OAAO,CAACJ,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG;IAAO,CAAE;IACjE,aAAU,QAAQ;IAAA6G,QAAA,eAElBrN,OAAA;MACEkN,SAAS,EAAC,iBAAiB;MAC3BC,KAAK,EAAE;QACLG,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,KAAK;QACfC,OAAO,EAAE,UAAU;QACnBC,YAAY,EAAE,MAAM;QACpBC,eAAe,EAAE9G,OAAO,CAACJ,IAAI,KAAK,MAAM,GAAG,SAAS,GAAG;MACzD,CAAE;MAAA6G,QAAA,gBAEFrN,OAAA;QACEkN,SAAS,EAAC,cAAc;QACxBC,KAAK,EAAE;UACLQ,UAAU,EAAE,UAAU;UACtBC,SAAS,EAAE,MAAM;UACjBC,SAAS,EAAE,OAAO;UAClBC,QAAQ,EAAE,KAAK;UACfC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,GAEDzG,OAAO,CAACH,OAAO,CAACuH,KAAK,CAAC,IAAI,CAAC,CAAC5D,GAAG,CAAC,CAAC6D,IAAI,EAAErD,GAAG,KAAK;UAC9C,MAAMsD,KAAK,GAAG,EAAE;UAChB,IAAIC,SAAS,GAAGF,IAAI;UACpB,IAAIG,QAAQ,GAAG,CAAC;UAEhB,OAAOD,SAAS,CAAC/F,MAAM,GAAG,CAAC,EAAE;YAC3B,MAAMiG,SAAS,GAAGF,SAAS,CAACG,KAAK,CAAC,wBAAwB,CAAC;YAC3D,MAAMC,SAAS,GAAGJ,SAAS,CAACG,KAAK,CAAC,eAAe,CAAC;YAElD,IAAID,SAAS,KAAK,CAACE,SAAS,IAAIF,SAAS,CAAC9D,KAAK,GAAGgE,SAAS,CAAChE,KAAK,CAAC,EAAE;cAClE,IAAIzD,IAAI,GAAGuH,SAAS,CAAC,CAAC,CAAC;cACvB,IAAI,CAACvH,IAAI,CAAC6E,UAAU,CAAC,MAAM,CAAC,EAAE;gBAC5B7E,IAAI,GAAG,GAAG7G,WAAW,GAAG6G,IAAI,CAAC6E,UAAU,CAAC,GAAG,CAAC,GAAG7E,IAAI,GAAG,GAAG,GAAGA,IAAI,EAAE;cACpE;cACA,IACE,CAACA,IAAI,CAAC6E,UAAU,CAAC,GAAG1L,WAAW,aAAa,CAAC,IAAI6G,IAAI,CAAC6E,UAAU,CAAC,aAAa,CAAC,KAC/EtL,WAAW,IACX,CAACyG,IAAI,CAACgC,QAAQ,CAAC,QAAQ,CAAC,EACxB;gBACAhC,IAAI,IAAIA,IAAI,CAACgC,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAUzI,WAAW,EAAE,GAAG,UAAUA,WAAW,EAAE;cAChF;cACA6N,KAAK,CAAC/B,IAAI,cACRnM,OAAA;gBAAAqN,QAAA,GACGc,SAAS,CAACK,KAAK,CAAC,CAAC,EAAEH,SAAS,CAAC9D,KAAK,CAAC,eACpCvK,OAAA;kBACE8G,IAAI,EAAEA,IAAK;kBACXG,MAAM,EAAC,QAAQ;kBACfwH,GAAG,EAAC,qBAAqB;kBACzBtB,KAAK,EAAE;oBAAEuB,KAAK,EAAE,SAAS;oBAAEC,cAAc,EAAE;kBAAY,CAAE;kBAAAtB,QAAA,EAExDgB,SAAS,CAAC,CAAC;gBAAC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC;cAAA,GATKX,QAAQ,EAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUf,CACR,CAAC;cACDZ,SAAS,GAAGA,SAAS,CAACK,KAAK,CAACH,SAAS,CAAC9D,KAAK,GAAG8D,SAAS,CAAC,CAAC,CAAC,CAACjG,MAAM,CAAC;YACpE,CAAC,MAAM,IAAImG,SAAS,EAAE;cACpBL,KAAK,CAAC/B,IAAI,cACRnM,OAAA;gBAAAqN,QAAA,GACGc,SAAS,CAACK,KAAK,CAAC,CAAC,EAAED,SAAS,CAAChE,KAAK,CAAC,eACpCvK,OAAA;kBAAAqN,QAAA,EAASkB,SAAS,CAAC,CAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA,GAFtBX,QAAQ,EAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGf,CACR,CAAC;cACDZ,SAAS,GAAGA,SAAS,CAACK,KAAK,CAACD,SAAS,CAAChE,KAAK,GAAGgE,SAAS,CAAC,CAAC,CAAC,CAACnG,MAAM,CAAC;YACpE,CAAC,MAAM;cACL8F,KAAK,CAAC/B,IAAI,cAACnM,OAAA;gBAAAqN,QAAA,EAAwBc;cAAS,GAAtBC,QAAQ,EAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAmB,CAAC,CAAC;cACrD;YACF;UACF;UAEA,oBAAO/O,OAAA;YAAAqN,QAAA,EAAgBa;UAAK,GAAXtD,GAAG;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QACrC,CAAC,CAAC,EACDnI,OAAO,CAAC3D,OAAO,iBACdjD,OAAA;UAAKmN,KAAK,EAAE;YAAE6B,SAAS,EAAE;UAAM,CAAE;UAAA3B,QAAA,EAC9BzG,OAAO,CAAC3D,OAAO,CAACmH,GAAG,CAAC,CAAC6E,MAAM,EAAErE,GAAG,kBAC/B5K,OAAA;YAEEmN,KAAK,EAAE;cACL+B,MAAM,EAAE,SAAS;cACjB1B,OAAO,EAAE,SAAS;cAClB2B,YAAY,EACVvE,GAAG,KAAKhE,OAAO,CAAC3D,OAAO,CAACmF,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG;YAC5D,CAAE;YACFgH,OAAO,EAAEA,CAAA,KAAM7F,kBAAkB,CAAC0F,MAAM,CAACzF,YAAY,CAAE;YAAA6D,QAAA,GAEtD4B,MAAM,CAAC1E,KAAK,EAAC,IAAE,eAAAvK,OAAA;cAAAqN,QAAA,EAAS4B,MAAM,CAACzF;YAAY;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,YAAG,EAACE,MAAM,CAACzE,KAAK;UAAA,GATjEyE,MAAM,CAACzF,YAAY;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUrB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACN/O,OAAA;QACEkN,SAAS,EAAC,cAAc;QACxBC,KAAK,EAAE;UAAEW,QAAQ,EAAE,OAAO;UAAEY,KAAK,EAAE,MAAM;UAAEM,SAAS,EAAE;QAAM,CAAE;QAAA3B,QAAA,EAE7DR,UAAU,CAACjG,OAAO,CAACF,SAAS;MAAC;QAAAkI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC,GArGDnI,OAAO,CAACL,EAAE;IAAAqI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAsGZ,CACN,CAAC;EAEJ,oBACE/O,OAAA;IAAKkN,SAAS,EAAC,gBAAgB;IAACmC,IAAI,EAAC,MAAM;IAAC,cAAW,kBAAkB;IAAAhC,QAAA,gBACvErN,OAAA;MAAKkN,SAAS,EAAC,aAAa;MAACC,KAAK,EAAE;QAAEmC,QAAQ,EAAE;MAAW,CAAE;MAAAjC,QAAA,gBAC3DrN,OAAA;QAAAqN,QAAA,EAAI;MAAkC;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3C/O,OAAA;QAAGkN,SAAS,EAAC,UAAU;QAAAG,QAAA,EAAC;MAAiC;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,EAC5DrN,QAAQ,iBACP1B,OAAA;QAAGmN,KAAK,EAAE;UAAEW,QAAQ,EAAE,OAAO;UAAEY,KAAK,EAAE;QAAO,CAAE;QAAArB,QAAA,GAAC,gBAChC,eAAArN,OAAA;UAAAqN,QAAA,EAAS3L;QAAQ;UAAAkN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CACJ,eACD/O,OAAA;QAAKmN,KAAK,EAAE;UAAEmC,QAAQ,EAAE,UAAU;UAAEC,GAAG,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAG,CAAE;QAAAnC,QAAA,eACvDrN,OAAA;UACEoP,OAAO,EAAExC,YAAa;UACtBO,KAAK,EAAE;YACLK,OAAO,EAAE,UAAU;YACnB0B,MAAM,EAAE,SAAS;YACjBxB,eAAe,EAAE,SAAS;YAC1B+B,MAAM,EAAE,MAAM;YACdhC,YAAY,EAAE,CAAC;YACfiB,KAAK,EAAE,OAAO;YACdgB,UAAU,EAAE;UACd,CAAE;UACF,cAAW,QAAQ;UACnBlF,KAAK,EAAC,QAAQ;UAAA6C,QAAA,EACf;QAED;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/O,OAAA;MAAKkN,SAAS,EAAC,eAAe;MAAC,aAAU,QAAQ;MAAC,iBAAc,WAAW;MAAAG,QAAA,GACxEJ,cAAc,CAAC,CAAC,EAChB,CAACnM,OAAO,IAAIU,SAAS,kBACpBxB,OAAA;QAAKkN,SAAS,EAAC,oBAAoB;QAAC,cAAW,WAAW;QAAAG,QAAA,eACxDrN,OAAA;UAAKkN,SAAS,EAAC,iBAAiB;UAAAG,QAAA,gBAC9BrN,OAAA;YAAKkN,SAAS,EAAC,kBAAkB;YAAC,eAAY,MAAM;YAAAG,QAAA,gBAClDrN,OAAA;cAAA4O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/O,OAAA;cAAA4O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/O,OAAA;cAAA4O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN/O,OAAA;YAAKkN,SAAS,EAAC,cAAc;YAAAG,QAAA,EAC1B7L,SAAS,GAAG,2BAA2B,GAAG;UAAc;YAAAoN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eACD/O,OAAA;QAAK2P,GAAG,EAAElL;MAAe;QAAAmK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,EAEL/N,KAAK,iBACJhB,OAAA;MACEkN,SAAS,EAAC,eAAe;MACzBkC,OAAO,EAAEA,CAAA,KAAMnO,QAAQ,CAAC,EAAE,CAAE;MAC5BkM,KAAK,EAAE;QAAE+B,MAAM,EAAE;MAAU,CAAE;MAC7BG,IAAI,EAAC,OAAO;MACZ,aAAU,WAAW;MACrBO,QAAQ,EAAE,CAAE;MAAAvC,QAAA,GAEXrM,KAAK,EAAC,qBACT;IAAA;MAAA4N,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,eAED/O,OAAA;MAAMkN,SAAS,EAAC,iBAAiB;MAAC2C,QAAQ,EAAE/F,YAAa;MAAC,cAAW,mBAAmB;MAAAuD,QAAA,eACtFrN,OAAA;QAAKkN,SAAS,EAAC,iBAAiB;QAACC,KAAK,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEwC,UAAU,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAA1C,QAAA,gBAC/FrN,OAAA;UACEwG,IAAI,EAAC,MAAM;UACXU,KAAK,EAAE1G,KAAM;UACbwP,QAAQ,EAAEjJ,aAAc;UACxBkJ,WAAW,EACT3O,WAAW,GACPiC,UAAU,GAAG,CAAC,GACZe,eAAe,CAACf,UAAU,GAAG,CAAC,CAAC,GAC/BT,8BAA8B,GAC9B,gDAAgD,GAChDhB,yBAAyB,GACzB,6CAA6C,GAC7C,4BAA4B,GAC9B,iCACL;UACDoO,QAAQ,EAAEpP,OAAO,IAAIU,SAAS,IAAK+B,UAAU,KAAK,CAAC,IAAI,CAAC/C,KAAO;UAC/D2P,SAAS;UACT,cAAW,YAAY;UACvBhD,KAAK,EAAE;YAAEiD,IAAI,EAAE,GAAG;YAAEL,KAAK,EAAE,MAAM;YAAEvC,OAAO,EAAE,KAAK;YAAEC,YAAY,EAAE,aAAa;YAAEgC,MAAM,EAAE,gBAAgB;YAAEY,MAAM,EAAE;UAAE,CAAE;UACtHC,SAAS,EAAGtJ,CAAC,IAAK;YAChB,IAAIA,CAAC,CAACuJ,GAAG,KAAK,OAAO,IAAI,CAACvJ,CAAC,CAACwJ,QAAQ,EAAE;cACpCxJ,CAAC,CAAC+C,cAAc,CAAC,CAAC;cAClBD,YAAY,CAAC9C,CAAC,CAAC;YACjB;UACF;QAAE;UAAA4H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACDxL,UAAU,KAAK,CAAC,iBACfvD,OAAA;UACEkH,KAAK,EAAEzD,UAAU,CAACE,WAAY;UAC9BqM,QAAQ,EAAE7I,uBAAwB;UAClCgG,KAAK,EAAE;YACLsD,UAAU,EAAE,GAAG;YACfjD,OAAO,EAAE,KAAK;YACdC,YAAY,EAAE,GAAG;YACjBgC,MAAM,EAAE,gBAAgB;YACxBiB,UAAU,EAAE,MAAM;YAClB5C,QAAQ,EAAE;UACZ,CAAE;UACF,cAAW,qBAAqB;UAAAT,QAAA,gBAEhCrN,OAAA;YAAQkH,KAAK,EAAC,EAAE;YAACgJ,QAAQ;YAAA7C,QAAA,EAAC;UAE1B;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACRxK,kBAAkB,CAAC6F,GAAG,CAAEuG,MAAM,iBAC7B3Q,OAAA;YAAqBkH,KAAK,EAAEyJ,MAAO;YAAAtD,QAAA,EAChCsD;UAAM,GADIA,MAAM;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEX,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACT,eACD/O,OAAA;UACEwG,IAAI,EAAC,QAAQ;UACb0J,QAAQ,EAAEpP,OAAO,IAAI,CAACN,KAAK,CAACqI,IAAI,CAAC,CAAC,IAAIrH,SAAS,IAAK+B,UAAU,KAAK,CAAC,IAAI,CAACE,UAAU,CAACE,WAAa;UACjG6G,KAAK,EAAE1J,OAAO,IAAIU,SAAS,GAAG,gBAAgB,GAAG,MAAO;UACxD,cAAW,cAAc;UACzB2L,KAAK,EAAE;YAAEK,OAAO,EAAE,UAAU;YAAEC,YAAY,EAAE,aAAa;YAAEgC,MAAM,EAAE,gBAAgB;YAAEiB,UAAU,EAAE,MAAM;YAAEhD,eAAe,EAAE,SAAS;YAAEgB,KAAK,EAAE;UAAQ,CAAE;UAAArB,QAAA,EAErJvM,OAAO,IAAIU,SAAS,gBAAGxB,OAAA;YAAMkN,SAAS,EAAC;UAAS;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAI;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAAC3O,EAAA,CAhnCuBF,IAAI;AAAA0Q,EAAA,GAAJ1Q,IAAI;AAAA,IAAA0Q,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}