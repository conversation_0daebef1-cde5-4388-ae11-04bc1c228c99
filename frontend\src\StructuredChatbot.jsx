import React, { useState, useEffect, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { YesNoButtons, FileDownloadButtons, TicketCloseButtons } from "./YesNoButtons";
import "./App.css";

const BACKEND_URL = "http://localhost:8000";

export default function StructuredChatbot({ token }) {
  const { ticketId } = useParams(); // Can be ticket number or "general"
  const navigate = useNavigate();
  const accessToken = token || localStorage.getItem("access");
  const messagesEndRef = useRef(null);

  // States
  const [messages, setMessages] = useState([]);
  const [query, setQuery] = useState("");
  const [loading, setLoading] = useState(false);
  const [sessionLoading, setSessionLoading] = useState(true);
  const [error, setError] = useState("");
  const [sessionData, setSessionData] = useState(null);
  const [awaitingResponse, setAwaitingResponse] = useState(false);
  const [responseType, setResponseType] = useState(""); // "close_ticket", "more_queries", "file_download", etc.
  const [pendingFiles, setPendingFiles] = useState([]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, loading]);

  // Initialize session
  useEffect(() => {
    if (!accessToken) {
      navigate("/auth");
      return;
    }

    initializeSession();
  }, [accessToken, ticketId, navigate]);

  const initializeSession = async () => {
    setSessionLoading(true);
    try {
      let endpoint;
      if (ticketId === "general") {
        endpoint = `${BACKEND_URL}/api/start_general_session/`;
      } else {
        endpoint = `${BACKEND_URL}/api/start_ticket_session/${ticketId}/`;
      }

      const response = await fetch(endpoint, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const data = await response.json();

      if (response.ok) {
        setSessionData(data);
        addBotMessage(data.initial_message);
      } else {
        setError(data.error || "Failed to start session");
        setTimeout(() => navigate("/actions"), 3000);
      }
    } catch (err) {
      console.error("Session initialization error:", err);
      setError("Network error. Redirecting...");
      setTimeout(() => navigate("/actions"), 3000);
    } finally {
      setSessionLoading(false);
    }
  };

  const addBotMessage = (content, showButtons = false, buttonType = "") => {
    const newMessage = {
      id: Date.now(),
      type: "bot",
      content,
      timestamp: new Date(),
      showButtons,
      buttonType
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const addUserMessage = (content) => {
    const newMessage = {
      id: Date.now(),
      type: "user",
      content,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const handleSendMessage = async () => {
    if (!query.trim() || loading || awaitingResponse) return;

    const userQuery = query.trim();
    setQuery("");
    addUserMessage(userQuery);
    setLoading(true);
    setError("");

    try {
      const requestBody = {
        query: userQuery,
        ticket_mode: sessionData?.session_type === "ticket",
        ticket_id: sessionData?.ticket?.ticket_number || null,
      };

      // Handle problem description for new tickets
      if (sessionData?.can_add_problem && sessionData?.session_type === "ticket") {
        const response = await fetch(`${BACKEND_URL}/api/add_problem_description/`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
          body: JSON.stringify({
            ticket_number: sessionData.ticket.ticket_number,
            problem_description: userQuery
          }),
        });

        const data = await response.json();

        if (response.ok) {
          addBotMessage(data.answer);
          if (data.files && data.files.length > 0) {
            setPendingFiles(data.files);
            addBotMessage("💡 I found relevant documentation. Would you like me to provide the file for detailed information?", true, "file_download");
            setAwaitingResponse(true);
            setResponseType("file_download");
          } else {
            addBotMessage("🟢 Do you have any other queries about this issue?", true, "more_queries");
            setAwaitingResponse(true);
            setResponseType("more_queries");
          }
          // Update session data to reflect that problem description is added
          setSessionData(prev => ({ ...prev, can_add_problem: false }));
        } else {
          addBotMessage("❌ " + (data.error || "Failed to process your request."));
        }
      } else {
        // Regular chat
        const response = await fetch(`${BACKEND_URL}/api/chat/`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
          body: JSON.stringify(requestBody),
        });

        const data = await response.json();

        if (response.ok) {
          addBotMessage(data.answer);
          
          if (data.files && data.files.length > 0) {
            setPendingFiles(data.files);
            addBotMessage("💡 I found relevant documentation. Would you like me to provide the file for detailed information?", true, "file_download");
            setAwaitingResponse(true);
            setResponseType("file_download");
          } else if (sessionData?.session_type === "ticket") {
            addBotMessage("🟢 Do you have any other queries about this issue?", true, "more_queries");
            setAwaitingResponse(true);
            setResponseType("more_queries");
          }
        } else {
          addBotMessage("❌ " + (data.error || "Failed to get response."));
        }
      }
    } catch (err) {
      console.error("Chat error:", err);
      addBotMessage("❌ Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleYesNoResponse = async (response, type) => {
    setAwaitingResponse(false);
    addUserMessage(response ? "Yes" : "No");

    if (type === "file_download") {
      if (response) {
        // Download file
        if (pendingFiles.length > 0) {
          const file = pendingFiles[0];
          const fileUrl = `${BACKEND_URL}${file.url}?token=${accessToken}`;
          window.open(fileUrl, '_blank');
          addBotMessage("📄 File opened in a new tab. After reviewing, do you have any other queries?", true, "more_queries");
          setResponseType("more_queries");
          setAwaitingResponse(true);
        }
      } else {
        if (sessionData?.session_type === "ticket") {
          addBotMessage("🟢 Do you have any other queries about this issue?", true, "more_queries");
          setResponseType("more_queries");
          setAwaitingResponse(true);
        }
      }
      setPendingFiles([]);
    } else if (type === "more_queries") {
      if (response) {
        addBotMessage("Please go ahead and ask your question.");
      } else {
        if (sessionData?.session_type === "ticket") {
          addBotMessage("Would you like to close this ticket?", true, "close_ticket");
          setResponseType("close_ticket");
          setAwaitingResponse(true);
        } else {
          addBotMessage("Thank you for using our support system. Have a great day!");
          setTimeout(() => handleLogout(), 2000);
        }
      }
    } else if (type === "close_ticket") {
      if (response) {
        await closeTicket();
      } else {
        addBotMessage("Ticket will remain open. You can continue asking questions or return later.");
      }
    }
  };

  const closeTicket = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/update_ticket_status/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          ticket_number: sessionData.ticket.ticket_number,
          status: "closed"
        }),
      });

      if (response.ok) {
        addBotMessage("✅ Ticket has been closed successfully. Thank you for using our support system!");
        setTimeout(() => handleLogout(), 3000);
      } else {
        addBotMessage("❌ Failed to close ticket. Please try again.");
      }
    } catch (err) {
      console.error("Error closing ticket:", err);
      addBotMessage("❌ Network error while closing ticket.");
    }
  };

  const handleLogout = () => {
    localStorage.removeItem("access");
    localStorage.removeItem("refresh");
    localStorage.removeItem("userData");
    navigate("/auth");
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (sessionLoading) {
    return (
      <div style={{ padding: "40px", textAlign: "center" }}>
        <h2>Starting your session...</h2>
        <p>⏳ Please wait...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: "40px", textAlign: "center" }}>
        <h2 style={{ color: "#d32f2f" }}>Error</h2>
        <p>{error}</p>
        <button onClick={() => navigate("/actions")} style={{ marginTop: "20px", padding: "10px 20px" }}>
          Back to Actions
        </button>
      </div>
    );
  }

  return (
    <div style={{ 
      display: "flex", 
      flexDirection: "column", 
      height: "100vh", 
      fontFamily: "Arial, sans-serif" 
    }}>
      {/* Header */}
      <div style={{ 
        padding: "15px 20px", 
        backgroundColor: "#f5f5f5", 
        borderBottom: "1px solid #ddd",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center"
      }}>
        <h2 style={{ margin: 0, color: "#333" }}>
          {sessionData?.session_type === "ticket" 
            ? `Ticket #${sessionData.ticket.ticket_number}` 
            : "General Support Chat"}
        </h2>
        <button
          onClick={() => navigate("/actions")}
          style={{
            padding: "8px 16px",
            backgroundColor: "#6c757d",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer"
          }}
        >
          ← Back to Actions
        </button>
      </div>

      {/* Messages */}
      <div style={{ 
        flex: 1, 
        overflowY: "auto", 
        padding: "20px",
        backgroundColor: "#fafafa"
      }}>
        {messages.map((message) => (
          <div key={message.id} style={{ marginBottom: "15px" }}>
            <div style={{
              display: "flex",
              justifyContent: message.type === "user" ? "flex-end" : "flex-start"
            }}>
              <div style={{
                maxWidth: "70%",
                padding: "12px 16px",
                borderRadius: "12px",
                backgroundColor: message.type === "user" ? "#2196F3" : "#e0e0e0",
                color: message.type === "user" ? "white" : "#333",
                whiteSpace: "pre-wrap"
              }}>
                {message.content}
              </div>
            </div>
            
            {message.showButtons && message.buttonType && awaitingResponse && (
              <div style={{ marginTop: "10px", textAlign: "center" }}>
                {message.buttonType === "file_download" && (
                  <FileDownloadButtons
                    onDownload={() => handleYesNoResponse(true, "file_download")}
                    onSkip={() => handleYesNoResponse(false, "file_download")}
                  />
                )}
                {(message.buttonType === "more_queries" || message.buttonType === "close_ticket") && (
                  <YesNoButtons
                    onYes={() => handleYesNoResponse(true, message.buttonType)}
                    onNo={() => handleYesNoResponse(false, message.buttonType)}
                  />
                )}
              </div>
            )}
          </div>
        ))}
        
        {loading && (
          <div style={{ textAlign: "center", color: "#666" }}>
            <p>🤖 Thinking...</p>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div style={{ 
        padding: "20px", 
        backgroundColor: "white", 
        borderTop: "1px solid #ddd" 
      }}>
        <div style={{ display: "flex", gap: "10px" }}>
          <textarea
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={awaitingResponse ? "Please use the buttons above to respond..." : "Type your message..."}
            disabled={loading || awaitingResponse}
            style={{
              flex: 1,
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "6px",
              resize: "none",
              minHeight: "50px",
              fontSize: "16px"
            }}
          />
          <button
            onClick={handleSendMessage}
            disabled={loading || awaitingResponse || !query.trim()}
            style={{
              padding: "12px 24px",
              backgroundColor: loading || awaitingResponse || !query.trim() ? "#ccc" : "#2196F3",
              color: "white",
              border: "none",
              borderRadius: "6px",
              cursor: loading || awaitingResponse || !query.trim() ? "not-allowed" : "pointer",
              fontSize: "16px"
            }}
          >
            Send
          </button>
        </div>
      </div>
    </div>
  );
}
