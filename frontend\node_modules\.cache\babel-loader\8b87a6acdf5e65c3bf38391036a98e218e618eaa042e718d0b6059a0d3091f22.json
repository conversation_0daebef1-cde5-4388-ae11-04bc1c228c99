{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AI-Agent-Chatbot-main\\\\frontend\\\\src\\\\ActionsPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport \"./App.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BACKEND_URL = \"http://localhost:8000\";\nexport default function ActionsPage({\n  token\n}) {\n  _s();\n  const navigate = useNavigate();\n  const accessToken = token || localStorage.getItem(\"access\");\n  const [userName, setUserName] = useState(\"\");\n  useEffect(() => {\n    if (!accessToken) {\n      navigate(\"/auth\");\n      return;\n    }\n\n    // Fetch user info\n    fetch(`${BACKEND_URL}/api/user_info/`, {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bear<PERSON> ${accessToken}`\n      }\n    }).then(async res => {\n      if (!res.ok) {\n        throw new Error(\"User info fetch failed\");\n      }\n      return res.json();\n    }).then(data => {\n      setUserName(data.name || data.username || data.email);\n    }).catch(err => {\n      console.error(\"Failed to fetch user info:\", err);\n      localStorage.removeItem(\"access\");\n      navigate(\"/auth\");\n    });\n  }, [accessToken, navigate]);\n  const handleRaiseNewTicket = () => {\n    navigate(\"/new-ticket\");\n  };\n  const handleUsePendingTicket = () => {\n    // Navigate to pending tickets list page\n    navigate(\"/pending-tickets\");\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: \"40px\",\n      maxWidth: \"800px\",\n      margin: \"0 auto\",\n      textAlign: \"center\",\n      fontFamily: \"Arial, sans-serif\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      style: {\n        color: \"#333\",\n        marginBottom: \"20px\",\n        fontSize: \"2.5rem\"\n      },\n      children: [\"Welcome, \", userName, \"!\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        fontSize: \"1.2rem\",\n        color: \"#666\",\n        marginBottom: \"40px\",\n        lineHeight: \"1.6\"\n      },\n      children: \"How can we help you today? Please choose one of the options below:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        flexDirection: \"column\",\n        gap: \"20px\",\n        alignItems: \"center\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleRaiseNewTicket,\n        style: {\n          backgroundColor: \"#4CAF50\",\n          color: \"white\",\n          border: \"none\",\n          padding: \"20px 40px\",\n          fontSize: \"1.3rem\",\n          borderRadius: \"8px\",\n          cursor: \"pointer\",\n          width: \"300px\",\n          boxShadow: \"0 4px 8px rgba(0,0,0,0.1)\",\n          transition: \"all 0.3s ease\"\n        },\n        onMouseOver: e => {\n          e.target.style.backgroundColor = \"#45a049\";\n          e.target.style.transform = \"translateY(-2px)\";\n        },\n        onMouseOut: e => {\n          e.target.style.backgroundColor = \"#4CAF50\";\n          e.target.style.transform = \"translateY(0)\";\n        },\n        children: \"\\uD83C\\uDFAB Raise New Ticket\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleUsePendingTicket,\n        style: {\n          backgroundColor: \"#2196F3\",\n          color: \"white\",\n          border: \"none\",\n          padding: \"20px 40px\",\n          fontSize: \"1.3rem\",\n          borderRadius: \"8px\",\n          cursor: \"pointer\",\n          width: \"300px\",\n          boxShadow: \"0 4px 8px rgba(0,0,0,0.1)\",\n          transition: \"all 0.3s ease\"\n        },\n        onMouseOver: e => {\n          e.target.style.backgroundColor = \"#1976D2\";\n          e.target.style.transform = \"translateY(-2px)\";\n        },\n        onMouseOut: e => {\n          e.target.style.backgroundColor = \"#2196F3\";\n          e.target.style.transform = \"translateY(0)\";\n        },\n        children: \"\\uD83D\\uDCCB Use Pending Ticket\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n}\n_s(ActionsPage, \"996fGx/NFzFGdF5o3x3ibNc1g3M=\", false, function () {\n  return [useNavigate];\n});\n_c = ActionsPage;\nvar _c;\n$RefreshReg$(_c, \"ActionsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "jsxDEV", "_jsxDEV", "BACKEND_URL", "ActionsPage", "token", "_s", "navigate", "accessToken", "localStorage", "getItem", "userName", "setUserName", "fetch", "headers", "Authorization", "then", "res", "ok", "Error", "json", "data", "name", "username", "email", "catch", "err", "console", "error", "removeItem", "handleRaiseNewTicket", "handleUsePendingTicket", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "textAlign", "fontFamily", "children", "color", "marginBottom", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "lineHeight", "display", "flexDirection", "gap", "alignItems", "onClick", "backgroundColor", "border", "borderRadius", "cursor", "width", "boxShadow", "transition", "onMouseOver", "e", "target", "transform", "onMouseOut", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/AI-Agent-Chatbot-main/frontend/src/ActionsPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport \"./App.css\";\n\nconst BACKEND_URL = \"http://localhost:8000\";\n\nexport default function ActionsPage({ token }) {\n  const navigate = useNavigate();\n  const accessToken = token || localStorage.getItem(\"access\");\n  const [userName, setUserName] = useState(\"\");\n\n  useEffect(() => {\n    if (!accessToken) {\n      navigate(\"/auth\");\n      return;\n    }\n\n    // Fetch user info\n    fetch(`${BACKEND_URL}/api/user_info/`, {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${accessToken}`,\n      },\n    })\n      .then(async (res) => {\n        if (!res.ok) {\n          throw new Error(\"User info fetch failed\");\n        }\n        return res.json();\n      })\n      .then((data) => {\n        setUserName(data.name || data.username || data.email);\n      })\n      .catch((err) => {\n        console.error(\"Failed to fetch user info:\", err);\n        localStorage.removeItem(\"access\");\n        navigate(\"/auth\");\n      });\n  }, [accessToken, navigate]);\n\n  const handleRaiseNewTicket = () => {\n    navigate(\"/new-ticket\");\n  };\n\n  const handleUsePendingTicket = () => {\n    // Navigate to pending tickets list page\n    navigate(\"/pending-tickets\");\n  };\n\n  return (\n    <div style={{ \n      padding: \"40px\", \n      maxWidth: \"800px\", \n      margin: \"0 auto\", \n      textAlign: \"center\",\n      fontFamily: \"Arial, sans-serif\"\n    }}>\n      <h1 style={{ \n        color: \"#333\", \n        marginBottom: \"20px\",\n        fontSize: \"2.5rem\"\n      }}>\n        Welcome, {userName}!\n      </h1>\n      \n      <p style={{ \n        fontSize: \"1.2rem\", \n        color: \"#666\", \n        marginBottom: \"40px\",\n        lineHeight: \"1.6\"\n      }}>\n        How can we help you today? Please choose one of the options below:\n      </p>\n\n      <div style={{ \n        display: \"flex\", \n        flexDirection: \"column\", \n        gap: \"20px\", \n        alignItems: \"center\" \n      }}>\n        \n        {/* Raise New Ticket Button */}\n        <button\n          onClick={handleRaiseNewTicket}\n          style={{\n            backgroundColor: \"#4CAF50\",\n            color: \"white\",\n            border: \"none\",\n            padding: \"20px 40px\",\n            fontSize: \"1.3rem\",\n            borderRadius: \"8px\",\n            cursor: \"pointer\",\n            width: \"300px\",\n            boxShadow: \"0 4px 8px rgba(0,0,0,0.1)\",\n            transition: \"all 0.3s ease\",\n          }}\n          onMouseOver={(e) => {\n            e.target.style.backgroundColor = \"#45a049\";\n            e.target.style.transform = \"translateY(-2px)\";\n          }}\n          onMouseOut={(e) => {\n            e.target.style.backgroundColor = \"#4CAF50\";\n            e.target.style.transform = \"translateY(0)\";\n          }}\n        >\n          🎫 Raise New Ticket\n        </button>\n\n        {/* Use Pending Ticket Button */}\n        <button\n          onClick={handleUsePendingTicket}\n          style={{\n            backgroundColor: \"#2196F3\",\n            color: \"white\",\n            border: \"none\",\n            padding: \"20px 40px\",\n            fontSize: \"1.3rem\",\n            borderRadius: \"8px\",\n            cursor: \"pointer\",\n            width: \"300px\",\n            boxShadow: \"0 4px 8px rgba(0,0,0,0.1)\",\n            transition: \"all 0.3s ease\",\n          }}\n          onMouseOver={(e) => {\n            e.target.style.backgroundColor = \"#1976D2\";\n            e.target.style.transform = \"translateY(-2px)\";\n          }}\n          onMouseOut={(e) => {\n            e.target.style.backgroundColor = \"#2196F3\";\n            e.target.style.transform = \"translateY(0)\";\n          }}\n        >\n          📋 Use Pending Ticket\n        </button>\n      </div>\n\n\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,MAAMC,WAAW,GAAG,uBAAuB;AAE3C,eAAe,SAASC,WAAWA,CAAC;EAAEC;AAAM,CAAC,EAAE;EAAAC,EAAA;EAC7C,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAMQ,WAAW,GAAGH,KAAK,IAAII,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;EAC3D,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,IAAI,CAACS,WAAW,EAAE;MAChBD,QAAQ,CAAC,OAAO,CAAC;MACjB;IACF;;IAEA;IACAM,KAAK,CAAC,GAAGV,WAAW,iBAAiB,EAAE;MACrCW,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAE,UAAUP,WAAW;MACtC;IACF,CAAC,CAAC,CACCQ,IAAI,CAAC,MAAOC,GAAG,IAAK;MACnB,IAAI,CAACA,GAAG,CAACC,EAAE,EAAE;QACX,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MACA,OAAOF,GAAG,CAACG,IAAI,CAAC,CAAC;IACnB,CAAC,CAAC,CACDJ,IAAI,CAAEK,IAAI,IAAK;MACdT,WAAW,CAACS,IAAI,CAACC,IAAI,IAAID,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAACG,KAAK,CAAC;IACvD,CAAC,CAAC,CACDC,KAAK,CAAEC,GAAG,IAAK;MACdC,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEF,GAAG,CAAC;MAChDjB,YAAY,CAACoB,UAAU,CAAC,QAAQ,CAAC;MACjCtB,QAAQ,CAAC,OAAO,CAAC;IACnB,CAAC,CAAC;EACN,CAAC,EAAE,CAACC,WAAW,EAAED,QAAQ,CAAC,CAAC;EAE3B,MAAMuB,oBAAoB,GAAGA,CAAA,KAAM;IACjCvB,QAAQ,CAAC,aAAa,CAAC;EACzB,CAAC;EAED,MAAMwB,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACAxB,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;EAED,oBACEL,OAAA;IAAK8B,KAAK,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAE,QAAQ;MACnBC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,gBACApC,OAAA;MAAI8B,KAAK,EAAE;QACTO,KAAK,EAAE,MAAM;QACbC,YAAY,EAAE,MAAM;QACpBC,QAAQ,EAAE;MACZ,CAAE;MAAAH,QAAA,GAAC,WACQ,EAAC3B,QAAQ,EAAC,GACrB;IAAA;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEL3C,OAAA;MAAG8B,KAAK,EAAE;QACRS,QAAQ,EAAE,QAAQ;QAClBF,KAAK,EAAE,MAAM;QACbC,YAAY,EAAE,MAAM;QACpBM,UAAU,EAAE;MACd,CAAE;MAAAR,QAAA,EAAC;IAEH;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAEJ3C,OAAA;MAAK8B,KAAK,EAAE;QACVe,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,GAAG,EAAE,MAAM;QACXC,UAAU,EAAE;MACd,CAAE;MAAAZ,QAAA,gBAGApC,OAAA;QACEiD,OAAO,EAAErB,oBAAqB;QAC9BE,KAAK,EAAE;UACLoB,eAAe,EAAE,SAAS;UAC1Bb,KAAK,EAAE,OAAO;UACdc,MAAM,EAAE,MAAM;UACdpB,OAAO,EAAE,WAAW;UACpBQ,QAAQ,EAAE,QAAQ;UAClBa,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE,SAAS;UACjBC,KAAK,EAAE,OAAO;UACdC,SAAS,EAAE,2BAA2B;UACtCC,UAAU,EAAE;QACd,CAAE;QACFC,WAAW,EAAGC,CAAC,IAAK;UAClBA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACoB,eAAe,GAAG,SAAS;UAC1CQ,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC8B,SAAS,GAAG,kBAAkB;QAC/C,CAAE;QACFC,UAAU,EAAGH,CAAC,IAAK;UACjBA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACoB,eAAe,GAAG,SAAS;UAC1CQ,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC8B,SAAS,GAAG,eAAe;QAC5C,CAAE;QAAAxB,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAGT3C,OAAA;QACEiD,OAAO,EAAEpB,sBAAuB;QAChCC,KAAK,EAAE;UACLoB,eAAe,EAAE,SAAS;UAC1Bb,KAAK,EAAE,OAAO;UACdc,MAAM,EAAE,MAAM;UACdpB,OAAO,EAAE,WAAW;UACpBQ,QAAQ,EAAE,QAAQ;UAClBa,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE,SAAS;UACjBC,KAAK,EAAE,OAAO;UACdC,SAAS,EAAE,2BAA2B;UACtCC,UAAU,EAAE;QACd,CAAE;QACFC,WAAW,EAAGC,CAAC,IAAK;UAClBA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACoB,eAAe,GAAG,SAAS;UAC1CQ,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC8B,SAAS,GAAG,kBAAkB;QAC/C,CAAE;QACFC,UAAU,EAAGH,CAAC,IAAK;UACjBA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACoB,eAAe,GAAG,SAAS;UAC1CQ,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC8B,SAAS,GAAG,eAAe;QAC5C,CAAE;QAAAxB,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGH,CAAC;AAEV;AAACvC,EAAA,CArIuBF,WAAW;EAAA,QAChBJ,WAAW;AAAA;AAAAgE,EAAA,GADN5D,WAAW;AAAA,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}