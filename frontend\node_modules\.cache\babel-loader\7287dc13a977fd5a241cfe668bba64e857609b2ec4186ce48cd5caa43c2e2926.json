{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AI-Agent-Chatbot-main\\\\frontend\\\\src\\\\Home.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { YesNoButtons, FileDownloadButtons, TicketCloseButtons } from \"./YesNoButtons\";\nimport \"./App.css\";\n\n// Define the backend URL\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BACKEND_URL = \"http://localhost:8000\";\nexport default function Home({\n  token\n}) {\n  _s();\n  const accessToken = token || localStorage.getItem(\"access\");\n\n  // --- States ---\n  const [query, setQuery] = useState(\"\");\n  const [messages, setMessages] = useState([]);\n  const [promptTemplate, setPromptTemplate] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n  const [mode, setMode] = useState(\"strict\");\n  const [pendingFiles, setPendingFiles] = useState(null);\n  const [orgVerified, setOrgVerified] = useState(false);\n  const [verifying, setVerifying] = useState(false);\n  const [username, setUserName] = useState(\"\");\n  const [askRaiseTicket, setAskRaiseTicket] = useState(false);\n  const [awaitingCloseConfirmation, setAwaitingCloseConfirmation] = useState(false);\n  const [awaitingOtherQueries, setAwaitingOtherQueries] = useState(false);\n  const [pendingTickets, setPendingTickets] = useState([]);\n  const [awaitingPendingChoice, setAwaitingPendingChoice] = useState(false);\n  const [awaitingTicketSelect, setAwaitingTicketSelect] = useState(false);\n  const [activeTicket, setActiveTicket] = useState(null);\n  const [ticketRefused, setTicketRefused] = useState(false);\n  const [queriesAfterNoTicket, setQueriesAfterNoTicket] = useState(0);\n  const [awaitingUnrelatedQueryResponse, setAwaitingUnrelatedQueryResponse] = useState(false);\n  const MAX_QUERIES_AFTER_NO_TICKET = 10;\n  const [tickets, setTickets] = useState([]);\n  const [showTickets, setShowTickets] = useState(false);\n  const [ticketQueryCount, setTicketQueryCount] = useState(0);\n\n  // --- New ticket states ---\n  const [ticketStep, setTicketStep] = useState(0);\n  const [ticketData, setTicketData] = useState({\n    productType: \"\",\n    purchasedFrom: \"\",\n    yearOfPurchase: \"\",\n    productName: \"\",\n    model: \"\",\n    serialNo: \"\",\n    operatingSystem: \"\"\n  });\n  const [awaitingProblemDescription, setAwaitingProblemDescription] = useState(false);\n  const [currentTicketNumber, setCurrentTicketNumber] = useState(null);\n  const ticketQuestions = [\"Please select the product type using the dropdown below:\", \"Please enter the 'Purchased From' information:\", \"Please enter the 'Year of Purchase':\", \"Please enter the 'Product Name':\", \"Please enter the 'Model':\", \"Please enter the 'Serial Number':\", \"Please enter the 'Operating System':\"];\n  const productTypeOptions = [\"Camera\", \"Frame Grabber\", \"Accessories\", \"Software\"];\n\n  // Auto-scroll messages\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  }, [messages, loading, error]);\n  const messagesEndRef = useRef(null);\n\n  // Fetch prompt template\n  useEffect(() => {\n    fetch(`${BACKEND_URL}/api/prompt/?type=chat`).then(res => res.json()).then(data => setPromptTemplate(data.template)).catch(err => {\n      console.error(\"Failed to fetch prompt template:\", err);\n      setPromptTemplate(null);\n    });\n  }, []);\n\n  // Check for pending mode or new ticket mode from URL\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const isPendingMode = urlParams.get('mode') === 'pending';\n    const isNewTicketMode = urlParams.get('mode') === 'new';\n    const ticketNumber = urlParams.get('ticket');\n    if (isPendingMode) {\n      // Skip organization verification and directly load pending ticket\n      loadPendingTicketDirectly();\n    } else if (isNewTicketMode && ticketNumber) {\n      // Load the newly created ticket\n      loadNewTicket(ticketNumber);\n    }\n  }, []);\n\n  // Fetch username and welcome message\n  useEffect(() => {\n    if (!accessToken) return;\n    const urlParams = new URLSearchParams(window.location.search);\n    const isPendingMode = urlParams.get('mode') === 'pending';\n    if (isPendingMode) {\n      return; // Skip normal flow for pending mode\n    }\n    fetch(`${BACKEND_URL}/api/user_info/`, {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${accessToken}`\n      }\n    }).then(async res => {\n      if (!res.ok) {\n        const errorText = await res.text();\n        console.error(\"User info fetch failed:\", res.status, errorText);\n        throw new Error(\"User info fetch failed\");\n      }\n      return res.json();\n    }).then(data => {\n      const name = data.name || data.username || data.email;\n      if (!name) throw new Error(\"Name missing in response\");\n      setUserName(name);\n      setMessages([{\n        id: 1,\n        type: \"bot\",\n        content: `👋 Welcome, ${name}! Please enter your organization name to verify your account.`,\n        timestamp: new Date()\n      }]);\n    }).catch(err => {\n      console.error(\"Failed to fetch user info:\", err.message);\n      localStorage.removeItem(\"access\");\n      window.location.href = \"/auth\";\n    });\n  }, [accessToken]);\n\n  // Load new ticket (for new ticket mode)\n  const loadNewTicket = async ticketNumber => {\n    if (!accessToken) return;\n    try {\n      // Get user info\n      const userResponse = await fetch(`${BACKEND_URL}/api/user_info/`, {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        }\n      });\n      if (!userResponse.ok) {\n        throw new Error(\"Failed to fetch user info\");\n      }\n      const userData = await userResponse.json();\n      const name = userData.name || userData.username || userData.email;\n      setUserName(name);\n      setOrgVerified(true);\n      setActiveTicket(ticketNumber);\n\n      // Fetch the ticket details to show generated content\n      try {\n        const ticketResponse = await fetch(`${BACKEND_URL}/api/ticket/${ticketNumber}/`, {\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${accessToken}`\n          }\n        });\n        if (ticketResponse.ok) {\n          const ticketData = await ticketResponse.json();\n          setMessages([{\n            id: 1,\n            type: \"bot\",\n            content: `👋 Welcome, ${name}! Your new support ticket ${ticketNumber} has been created successfully.`,\n            timestamp: new Date()\n          }, {\n            id: 2,\n            type: \"bot\",\n            content: `🎫 **Ticket Created:** ${ticketNumber}\\n\\n**Generated Title:** ${ticketData.ticket.short_title || 'No title'}\\n\\n**Generated Problem Description:** ${ticketData.ticket.problem_description || 'No description'}\\n\\nHow can I help you with this issue?`,\n            timestamp: new Date()\n          }]);\n        } else {\n          // Fallback if ticket details can't be fetched\n          setMessages([{\n            id: 1,\n            type: \"bot\",\n            content: `👋 Welcome, ${name}! Your new support ticket ${ticketNumber} has been created successfully.`,\n            timestamp: new Date()\n          }, {\n            id: 2,\n            type: \"bot\",\n            content: `🎫 **Ticket Created:** ${ticketNumber}\\n\\nHow can I help you with your issue?`,\n            timestamp: new Date()\n          }]);\n        }\n      } catch (err) {\n        console.error(\"Failed to fetch ticket details:\", err);\n        // Fallback messages\n        setMessages([{\n          id: 1,\n          type: \"bot\",\n          content: `👋 Welcome, ${name}! Your new support ticket ${ticketNumber} has been created successfully.`,\n          timestamp: new Date()\n        }, {\n          id: 2,\n          type: \"bot\",\n          content: `🎫 **Ticket Created:** ${ticketNumber}\\n\\nHow can I help you with your issue?`,\n          timestamp: new Date()\n        }]);\n      }\n    } catch (err) {\n      console.error(\"Failed to load new ticket:\", err);\n      setError(\"Failed to load ticket. Please try again.\");\n    }\n  };\n\n  // Load pending ticket directly (for pending mode)\n  const loadPendingTicketDirectly = async () => {\n    if (!accessToken) return;\n    try {\n      // First get user info\n      const userResponse = await fetch(`${BACKEND_URL}/api/user_info/`, {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        }\n      });\n      if (!userResponse.ok) {\n        throw new Error(\"Failed to fetch user info\");\n      }\n      const userData = await userResponse.json();\n      const name = userData.name || userData.username || userData.email;\n      setUserName(name);\n      setOrgVerified(true);\n\n      // Fetch pending tickets\n      const ticketsResponse = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        }\n      });\n      if (!ticketsResponse.ok) {\n        throw new Error(\"Failed to fetch pending tickets\");\n      }\n      const ticketsData = await ticketsResponse.json();\n      if (ticketsData.tickets && ticketsData.tickets.length > 0) {\n        const latestTicket = ticketsData.tickets[0]; // Get the latest ticket\n        setActiveTicket(latestTicket.ticket_number);\n        setMessages([{\n          id: 1,\n          type: \"bot\",\n          content: `👋 Welcome back, ${name}! Continuing with your ticket: ${latestTicket.ticket_number}`,\n          timestamp: new Date()\n        }, {\n          id: 2,\n          type: \"bot\",\n          content: `📋 **Ticket Details:**\\n**Title:** ${latestTicket.title || latestTicket.short_title || 'No title'}\\n**Problem:** ${latestTicket.problem_description || 'No description available'}\\n\\nHow can I help you with this ticket?`,\n          timestamp: new Date()\n        }]);\n      } else {\n        setMessages([{\n          id: 1,\n          type: \"bot\",\n          content: `👋 Welcome, ${name}! No pending tickets found. Please go back to create a new ticket.`,\n          timestamp: new Date()\n        }]);\n      }\n    } catch (err) {\n      console.error(\"Failed to load pending ticket:\", err);\n      setError(\"Failed to load pending ticket. Please try again.\");\n    }\n  };\n\n  // File download handler\n  const handleFileDownload = file => {\n    // Create a temporary link and trigger download\n    const link = document.createElement('a');\n    link.href = file.url;\n    link.download = file.source_file;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n\n    // Remove the file download message from chat\n    setMessages(prev => prev.filter(msg => !msg.files || msg.files.length === 0));\n    setPendingFiles(null);\n\n    // Add confirmation message\n    addBot(\"✅ File downloaded successfully! Do you have any more questions about this ticket?\");\n    setAwaitingOtherQueries(true);\n  };\n\n  // Ticket closure handler\n  const handleTicketClose = async () => {\n    if (!activeTicket) return;\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/update_ticket_status/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        },\n        body: JSON.stringify({\n          ticket_number: activeTicket,\n          status: \"closed\"\n        })\n      });\n      if (response.ok) {\n        addBot(`✅ Ticket ${activeTicket} has been closed successfully. Thank you for using our support system!`);\n\n        // Auto logout after 3 seconds\n        setTimeout(() => {\n          handleLogout();\n        }, 3000);\n      } else {\n        addBot(\"❌ Failed to close the ticket. Please try again.\");\n      }\n    } catch (err) {\n      console.error(\"Error closing ticket:\", err);\n      addBot(\"❌ Error occurred while closing the ticket. Please try again.\");\n    }\n  };\n\n  // Handle yes/no responses\n  const handleYesNoResponse = (response, context) => {\n    const userMsg = {\n      id: Date.now(),\n      type: \"user\",\n      content: response ? \"Yes\" : \"No\",\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, userMsg]);\n    if (context === \"file_download\") {\n      if (response && pendingFiles && pendingFiles.length > 0) {\n        handleFileDownload(pendingFiles[0]);\n      } else {\n        setPendingFiles(null);\n        addBot(\"Do you have any more questions about this ticket?\");\n        setAwaitingOtherQueries(true);\n      }\n    } else if (context === \"more_queries\") {\n      setAwaitingOtherQueries(false);\n      if (response) {\n        addBot(\"Please ask your question:\");\n      } else {\n        addBot(\"Do you want to close this ticket?\");\n        setAwaitingCloseConfirmation(true);\n      }\n    } else if (context === \"close_ticket\") {\n      setAwaitingCloseConfirmation(false);\n      if (response) {\n        handleTicketClose();\n      } else {\n        addBot(\"Ticket remains open. How else can I help you?\");\n      }\n    }\n  };\n\n  // Input change handler\n  const onInputChange = e => {\n    if (error) setError(\"\");\n    setQuery(e.target.value);\n  };\n\n  // Dropdown change handler for productType\n  const handleProductTypeChange = e => {\n    const selectedType = e.target.value;\n    setTicketData({\n      ...ticketData,\n      productType: selectedType\n    });\n    setMessages(prev => [...prev, {\n      id: Date.now(),\n      type: \"user\",\n      content: `Selected product type: ${selectedType}`,\n      timestamp: new Date()\n    }]);\n    setTicketStep(ticketStep + 1);\n    addBot(ticketQuestions[ticketStep]);\n  };\n\n  // Verify organization name\n  const verifyOrganization = async orgName => {\n    setVerifying(true);\n    setError(\"\");\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/verify_organization/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        },\n        body: JSON.stringify({\n          organization: orgName\n        })\n      });\n      const data = await response.json();\n      if (response.ok && data.status === \"verified\") {\n        setOrgVerified(true);\n        setUserName(data.name || username);\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: data.message || \"✅ Organization verified.\",\n          timestamp: new Date()\n        }]);\n        await fetchPendingTickets();\n      } else {\n        const baseUrl = window.location.origin;\n        const signupLink = `[sign up here](${baseUrl}/signup/)`;\n        setError(data.message || \"❌ Organization mismatch.\");\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: `❌ Organization verification failed.\\n\\nIf you belong to a new organization, please ${signupLink} to register first.`,\n          timestamp: new Date()\n        }]);\n        localStorage.removeItem(\"access\");\n      }\n    } catch (err) {\n      setError(\"Network error during verification.\");\n      setMessages(prev => [...prev, {\n        id: Date.now(),\n        type: \"bot\",\n        content: \"❌ Network error during organization verification. Please try again.\",\n        timestamp: new Date()\n      }]);\n      console.error(\"Verification error:\", err);\n    } finally {\n      setVerifying(false);\n    }\n  };\n\n  // Helper function to fetch pending tickets after verification\n  async function fetchPendingTickets() {\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {\n        headers: {\n          Authorization: `Bearer ${accessToken}`\n        }\n      });\n      const data = await response.json();\n      if (response.ok) {\n        setPendingTickets(data.tickets || []);\n        if (data.tickets && data.tickets.length > 0) {\n          setAwaitingPendingChoice(true);\n          addBot(`You have ${data.tickets.length} pending ticket(s). Do you want to continue with any of them? (yes/no)`);\n        } else {\n          setAskRaiseTicket(true);\n          addBot(\"No pending tickets found. Would you like to raise a support ticket? (yes/no)\");\n        }\n      } else {\n        setPendingTickets([]);\n        setAskRaiseTicket(true);\n        addBot(\"Could not fetch pending tickets. Would you like to raise a support ticket? ( Juno/no)\");\n      }\n    } catch (err) {\n      console.error(\"Error fetching pending tickets:\", err);\n      setPendingTickets([]);\n      setAskRaiseTicket(true);\n      addBot(\"Error fetching pending tickets. Would you like to raise a support ticket? (yes/no)\");\n    }\n  }\n\n  // Submit ticket with latest data\n  const submitTicket = async finalTicketData => {\n    setLoading(true);\n    setError(\"\");\n    const validProductTypes = [\"Camera\", \"Frame Grabber\", \"Accessories\", \"Software\"];\n    const allFilled = Object.values(finalTicketData).every(v => v && v.trim() !== \"\");\n    if (!allFilled) {\n      setMessages(prev => [...prev, {\n        id: Date.now(),\n        type: \"bot\",\n        content: \"❌ Please fill in all required fields before submitting the ticket.\",\n        timestamp: new Date()\n      }]);\n      setLoading(false);\n      return;\n    }\n    if (!validProductTypes.includes(finalTicketData.productType)) {\n      setMessages(prev => [...prev, {\n        id: Date.now(),\n        type: \"bot\",\n        content: \"❌ Invalid product type. Please select: Camera, Frame Grabber, Accessories, or Software.\",\n        timestamp: new Date()\n      }]);\n      setLoading(false);\n      return;\n    }\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        },\n        body: JSON.stringify({\n          product_type: finalTicketData.productType,\n          purchased_from: finalTicketData.purchasedFrom,\n          year_of_purchase: finalTicketData.yearOfPurchase,\n          product_name: finalTicketData.productName,\n          model: finalTicketData.model,\n          serial_no: finalTicketData.serialNo,\n          operating_system: finalTicketData.operatingSystem\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        setCurrentTicketNumber(data.ticket_number);\n        setActiveTicket(data.ticket_number);\n        setAwaitingProblemDescription(true);\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: `🎉 Thank you! Your support ticket has been created successfully. Your ticket number is **${data.ticket_number}**.\\n\\nPlease describe your problem so we can assist you.`,\n          timestamp: new Date()\n        }]);\n      } else {\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: `❌ Failed to create ticket: ${JSON.stringify(data.errors || data.message)}`,\n          timestamp: new Date()\n        }]);\n      }\n    } catch (err) {\n      setMessages(prev => [...prev, {\n        id: Date.now(),\n        type: \"bot\",\n        content: `❌ Network error while creating ticket: ${err.message}`,\n        timestamp: new Date()\n      }]);\n    } finally {\n      setLoading(false);\n      setTicketStep(0);\n      setAskRaiseTicket(false);\n      setTicketData({\n        productType: \"\",\n        purchasedFrom: \"\",\n        yearOfPurchase: \"\",\n        productName: \"\",\n        model: \"\",\n        serialNo: \"\",\n        operatingSystem: \"\"\n      });\n    }\n  };\n\n  // Helper to add a bot message\n  function addBot(text) {\n    setMessages(prev => [...prev, {\n      id: Date.now(),\n      type: \"bot\",\n      content: text,\n      timestamp: new Date()\n    }]);\n  }\n\n  // Handle ticket selection from UI\n  const handleTicketSelect = async ticketNumber => {\n    setAwaitingTicketSelect(false);\n    setActiveTicket(ticketNumber);\n    setCurrentTicketNumber(ticketNumber);\n    setShowTickets(false);\n    try {\n      const summaryRes = await fetch(`${BACKEND_URL}/api/ticket_summary/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        },\n        body: JSON.stringify({\n          ticket_number: ticketNumber\n        })\n      });\n      const summaryData = await summaryRes.json();\n      if (summaryRes.ok) {\n        addBot(`🔄 Resuming ticket **${ticketNumber}** …\\n\\n` + `📝 **Raised problem:** ${summaryData.problem_summary || summaryData.problem_description}\\n\\n` + `💡 **Given solution:** ${summaryData.solution_summary || \"No solution yet.\"}\\n\\n` + \"✅ You can ask your follow-up query now.\");\n      } else {\n        addBot(\"⚠️ Error fetching ticket summary.\");\n      }\n    } catch (err) {\n      addBot(\"❌ Network error while fetching ticket summary.\");\n    }\n  };\n\n  // FULLY UPDATED handleSubmit\n  async function handleSubmit(e) {\n    e.preventDefault();\n    if (!query.trim() || loading || verifying) return;\n    const currentQuery = query.trim().toLowerCase();\n\n    // --- New: Check 5 queries per ticket limit ---\n    if (activeTicket &&\n    // There is an active ticket\n    ticketStep === 0 &&\n    // Not in ticket creation steps\n    !awaitingProblemDescription &&\n    // Not waiting for problem description input\n    ticketQueryCount >= 5 // Limit reached\n    ) {\n      addBot(\"🛑 You have reached the maximum of five queries for this ticket. It has been automatically escalated to ensure prompt resolution. Kindly create a new ticket for any further inquiries or await our team’s response.\");\n      setQuery(\"\");\n      return;\n    }\n    // ---------------------------------------------\n\n    if (ticketRefused) {\n      if (queriesAfterNoTicket >= MAX_QUERIES_AFTER_NO_TICKET) {\n        addBot(\"⚠️ You have reached the maximum number of free queries. Please raise a support ticket for further assistance.\");\n        setQuery(\"\");\n        return;\n      } else {\n        setQueriesAfterNoTicket(n => n + 1);\n      }\n    }\n    const userMsg = {\n      id: Date.now(),\n      type: \"user\",\n      content: query.trim(),\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, userMsg]);\n    setQuery(\"\");\n    setError(\"\");\n    if (awaitingPendingChoice) {\n      setAwaitingPendingChoice(false);\n      if (currentQuery === \"yes\") {\n        setAwaitingTicketSelect(true);\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: `Select a ticket by typing its number:`,\n          timestamp: new Date(),\n          tickets: pendingTickets.map((t, i) => ({\n            index: i + 1,\n            ticketNumber: t.ticket_number,\n            title: t.title || t.short_title || \"No title\"\n          }))\n        }]);\n      } else if (currentQuery === \"no\") {\n        setAskRaiseTicket(true);\n        addBot(\"Do you want to raise a support ticket? (yes/no)\");\n      } else {\n        addBot(\"Please answer 'yes' or 'no'. Do you want to continue an open ticket?\");\n        setAwaitingPendingChoice(true);\n      }\n      return;\n    }\n    if (awaitingTicketSelect) {\n      const picked = pendingTickets.find((t, idx) => currentQuery === String(idx + 1) || currentQuery.includes(t.ticket_number.toLowerCase()));\n      if (!picked) {\n        addBot(\"Ticket not recognised, please type its number.\");\n        return;\n      }\n      await handleTicketSelect(picked.ticket_number);\n      return;\n    }\n    if (!orgVerified) {\n      await verifyOrganization(currentQuery);\n      return;\n    }\n    if (askRaiseTicket) {\n      if (currentQuery === \"yes\") {\n        setAskRaiseTicket(false);\n        setTicketStep(1);\n        addBot(ticketQuestions[0]);\n        setTicketRefused(false);\n        setQueriesAfterNoTicket(0);\n\n        // --- New: Reset query count when new ticket starts ---\n        setTicketQueryCount(0);\n        // ------------------------------------------------------\n      } else if (currentQuery === \"no\") {\n        setAskRaiseTicket(false);\n        addBot(\"👍 Okay, no ticket will be raised. How else can I help you?\");\n        setTicketRefused(true);\n        setQueriesAfterNoTicket(0);\n      } else {\n        addBot(\"Please answer 'yes' or 'no'. Do you want to raise a support ticket?\");\n      }\n      return;\n    }\n    if (awaitingUnrelatedQueryResponse) {\n      setAwaitingUnrelatedQueryResponse(false);\n      if (currentQuery === \"yes\") {\n        setTicketStep(1);\n        setActiveTicket(null);\n        setCurrentTicketNumber(null);\n        addBot(ticketQuestions[0]);\n\n        // --- New: Reset query count when new ticket starts here too ---\n        setTicketQueryCount(0);\n        // ---------------------------------------------------------------\n      } else if (currentQuery === \"no\") {\n        setAwaitingCloseConfirmation(true);\n        addBot(\"Can I close this ticket now? (yes/no)\");\n      } else {\n        setAwaitingUnrelatedQueryResponse(true);\n        addBot(\"Please answer 'yes' or 'no'. Do you want to create a new ticket?\");\n      }\n      return;\n    }\n    if (ticketStep > 0 && ticketStep <= ticketQuestions.length) {\n      if (ticketStep === 1) {\n        const selectedType = query.trim();\n        if (productTypeOptions.includes(selectedType)) {\n          setTicketData({\n            ...ticketData,\n            productType: selectedType\n          });\n          setMessages(prev => [...prev, {\n            id: Date.now(),\n            type: \"user\",\n            content: `Selected product type: ${selectedType}`,\n            timestamp: new Date()\n          }]);\n          setTicketStep(ticketStep + 1);\n          addBot(ticketQuestions[ticketStep]);\n        } else {\n          addBot(\"Please select a valid product type from: Camera, Frame Grabber, Accessories, or Software.\");\n        }\n        return;\n      }\n      const keys = [\"purchasedFrom\", \"yearOfPurchase\", \"productName\", \"model\", \"serialNo\", \"operatingSystem\"];\n      const currentField = keys[ticketStep - 2];\n      const updatedTicketData = {\n        ...ticketData,\n        [currentField]: query.trim()\n      };\n      setTicketData(updatedTicketData);\n      if (ticketStep < ticketQuestions.length) {\n        setTicketStep(ticketStep + 1);\n        addBot(ticketQuestions[ticketStep]);\n      } else {\n        await submitTicket(updatedTicketData);\n      }\n      return;\n    }\n    if (awaitingProblemDescription && currentTicketNumber) {\n      setAwaitingProblemDescription(false);\n      setLoading(true);\n      try {\n        var _saveData$files;\n        const saveRes = await fetch(`${BACKEND_URL}/api/add_problem_description/`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${accessToken}`\n          },\n          body: JSON.stringify({\n            ticket_number: currentTicketNumber,\n            problem_description: userMsg.content\n          })\n        });\n        const saveData = await saveRes.json();\n        if (!saveRes.ok) {\n          throw new Error(saveData.error || \"Failed to save problem description.\");\n        }\n        let botContent = saveData.answer || \"No solution available at the moment.\";\n        const rawFiles = (_saveData$files = saveData.files) !== null && _saveData$files !== void 0 ? _saveData$files : []; // Updated from saveData.related_files\n        const validFiles = rawFiles.filter(f => {\n          if (typeof f === \"string\") {\n            return f.trim() !== \"\" && !f.toLowerCase().startsWith(\"none\");\n          }\n          if (typeof f === \"object\" && f !== null && f.filename) {\n            return f.filename.trim() !== \"\" && !f.filename.toLowerCase().startsWith(\"none\");\n          }\n          return false;\n        });\n        if (validFiles.length > 0) {\n          setPendingFiles(validFiles.map(f => {\n            const filename = typeof f === \"string\" ? f : f.filename;\n            const url = typeof f === \"string\" || !f.url || !f.url.startsWith(\"http\") ? `${BACKEND_URL}/api/files/${encodeURIComponent(filename)}?token=${accessToken}` : `${f.url}${f.url.includes(\"?\") ? \"&\" : \"?\"}token=${accessToken}`;\n            return {\n              source_file: filename,\n              url\n            };\n          }));\n          botContent += \"\\n\\n💡 For full explanation, do you want the related file? (yes/no)\";\n        } else {\n          setPendingFiles(null);\n        }\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: botContent,\n          timestamp: new Date()\n        }]);\n        setAwaitingOtherQueries(true);\n      } catch (err) {\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: `❌ Error processing problem description: ${err.message}`,\n          timestamp: new Date()\n        }]);\n      } finally {\n        setLoading(false);\n      }\n      return;\n    }\n    if (pendingFiles && (currentQuery === \"yes\" || currentQuery === \"no\")) {\n      const baseMessages = [];\n      if (currentQuery === \"yes\") {\n        const downloadLinks = pendingFiles.map((f, idx) => `${idx + 1}. [${f.source_file}](${f.url})`).join(\"\\n\");\n        baseMessages.push({\n          id: Date.now() + 1,\n          type: \"bot\",\n          content: `📎 Here are the related files:\\n\\n${downloadLinks}`,\n          timestamp: new Date()\n        });\n      } else {\n        baseMessages.push({\n          id: Date.now() + 1,\n          type: \"bot\",\n          content: \"👍 Okay, no files will be sent.\",\n          timestamp: new Date()\n        });\n      }\n      if (activeTicket !== null) {\n        baseMessages.push({\n          id: Date.now() + 2,\n          type: \"bot\",\n          content: \"Do you have any other queries? (yes/no)\",\n          timestamp: new Date()\n        });\n        setAwaitingOtherQueries(true);\n      }\n      setMessages(prev => [...prev, ...baseMessages]);\n      setPendingFiles(null);\n      return;\n    }\n    if (awaitingOtherQueries) {\n      if (currentQuery === \"no\") {\n        setAwaitingOtherQueries(false);\n        setAwaitingCloseConfirmation(true);\n        addBot(\"Can I close this ticket now? (yes/no)\");\n      } else if (currentQuery === \"yes\") {\n        setAwaitingOtherQueries(false);\n        addBot(\"Please go ahead and ask your question.\");\n      } else {\n        addBot(\"Please answer 'yes' or 'no'. Do you have any other queries?\");\n      }\n      return;\n    }\n    if (awaitingCloseConfirmation) {\n      if (currentQuery === \"yes\") {\n        setAwaitingCloseConfirmation(false);\n        setLoading(true);\n        try {\n          const response = await fetch(`${BACKEND_URL}/api/update_ticket_status/`, {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\",\n              Authorization: `Bearer ${accessToken}`\n            },\n            body: JSON.stringify({\n              ticket_number: currentTicketNumber,\n              status: \"closed\"\n            })\n          });\n          const data = await response.json();\n          if (response.ok) {\n            setActiveTicket(null);\n            setCurrentTicketNumber(null);\n            setTicketData({\n              productType: \"\",\n              purchasedFrom: \"\",\n              yearOfPurchase: \"\",\n              productName: \"\",\n              model: \"\",\n              serialNo: \"\",\n              operatingSystem: \"\"\n            });\n            addBot(`✅ Ticket ${currentTicketNumber} has been closed. Thank you!`);\n\n            // --- New: Reset query count when ticket is closed ---\n            setTicketQueryCount(0);\n            // -----------------------------------------------------\n          } else {\n            throw new Error(data.error || \"Failed to close ticket.\");\n          }\n        } catch (err) {\n          addBot(`❌ Error closing ticket: ${err.message}`);\n        } finally {\n          setLoading(false);\n        }\n      } else if (currentQuery === \"no\") {\n        setAwaitingCloseConfirmation(false);\n        addBot(\"Okay, ticket will remain open.\");\n      } else {\n        addBot(\"Please answer 'yes' or 'no'. Can I close this ticket now?\");\n      }\n      return;\n    }\n    setLoading(true);\n    try {\n      const historyText = messages.filter(m => m.type === \"user\" || m.type === \"bot\").map(m => `${m.type === \"user\" ? \"User\" : \"Bot\"}: ${m.content}`).join(\"\\n\");\n      const finalPrompt = promptTemplate ? promptTemplate.replace(\"{context_text}\", \"some context text here\").replace(\"{history_text}\", historyText).replace(\"{query}\", query.trim()) : query.trim();\n      const response = await fetch(`${BACKEND_URL}/api/chat/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        },\n        body: JSON.stringify({\n          query: finalPrompt,\n          ticket_mode: !!activeTicket,\n          ticket_id: activeTicket,\n          stage: awaitingUnrelatedQueryResponse ? \"unrelated_query\" : \"\"\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        var _data$files;\n        let botContent = data.answer || \"…\";\n        if (data.stage === \"unrelated_query\") {\n          setAwaitingUnrelatedQueryResponse(true);\n        } else if (data.stage === \"create_new_ticket\") {\n          setTicketStep(1);\n          setActiveTicket(null);\n          setCurrentTicketNumber(null);\n          addBot(ticketQuestions[0]);\n\n          // --- New: Reset query count on new ticket here too ---\n          setTicketQueryCount(0);\n          // ------------------------------------------------------\n          return;\n        }\n        const rawFiles = (_data$files = data.files) !== null && _data$files !== void 0 ? _data$files : [];\n        const validFiles = rawFiles.filter(f => {\n          if (typeof f === \"string\") {\n            return f.trim() !== \"\" && !f.toLowerCase().startsWith(\"none\");\n          }\n          if (typeof f === \"object\" && f !== null && f.filename) {\n            return f.filename.trim() !== \"\" && !f.filename.toLowerCase().startsWith(\"none\");\n          }\n          return false;\n        });\n        if (validFiles.length > 0) {\n          setPendingFiles(validFiles.map(f => {\n            const filename = typeof f === \"string\" ? f : f.filename;\n            const url = typeof f === \"string\" || !f.url || !f.url.startsWith(\"http\") ? `${BACKEND_URL}/api/files/${encodeURIComponent(filename)}?token=${accessToken}` : `${f.url}${f.url.includes(\"?\") ? \"&\" : \"?\"}token=${accessToken}`;\n            return {\n              source_file: filename,\n              url: url\n            };\n          }));\n          if (!botContent.toLowerCase().includes(\"do you want the related file\")) {\n            botContent += \"\\n\\n💡 For full explanation, do you want the related file? (yes/no)\";\n          }\n        } else {\n          setPendingFiles(null);\n        }\n        setMessages(prev => [...prev, {\n          id: Date.now(),\n          type: \"bot\",\n          content: botContent,\n          timestamp: new Date()\n        }]);\n\n        // --- New: Increment query count for ticket queries ---\n        if (activeTicket && ticketStep === 0) {\n          setTicketQueryCount(prev => prev + 1);\n        }\n        // ------------------------------------------------------\n\n        if (data.stage === \"await_close\") {\n          setAwaitingCloseConfirmation(true);\n        }\n      } else {\n        setError(data.error || \"Error processing request\");\n      }\n    } catch (err) {\n      setError(\"Network error: \" + err.message);\n    } finally {\n      setLoading(false);\n    }\n  }\n\n  // Logout handler\n  function handleLogout() {\n    localStorage.removeItem(\"access\");\n    localStorage.removeItem(\"refresh\");\n    localStorage.removeItem(\"userData\");\n    window.location.href = \"/auth\";\n  }\n\n  // Format message time HH:MM\n  const formatTime = timestamp => {\n    return timestamp.toLocaleTimeString([], {\n      hour: \"2-digit\",\n      minute: \"2-digit\"\n    });\n  };\n\n  // Render chat messages with links parsed\n  const renderMessages = () => messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `message ${message.type}`,\n    style: {\n      textAlign: message.type === \"user\" ? \"right\" : \"left\"\n    },\n    \"aria-live\": \"polite\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"message-content\",\n      style: {\n        display: \"inline-block\",\n        maxWidth: \"75%\",\n        padding: \"8px 12px\",\n        borderRadius: \"12px\",\n        backgroundColor: message.type === \"user\" ? \"#DCF8C6\" : \"#F1F0F0\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-text\",\n        style: {\n          whiteSpace: \"pre-wrap\",\n          overflowY: \"auto\",\n          maxHeight: \"400px\",\n          fontSize: \"1em\",\n          lineHeight: \"1.5\"\n        },\n        children: [message.content.split(\"\\n\").map((line, idx) => {\n          const parts = [];\n          let remaining = line;\n          let keyIndex = 0;\n          while (remaining.length > 0) {\n            const linkMatch = remaining.match(/\\[(.*?)\\]\\((http.*?)\\)/);\n            const boldMatch = remaining.match(/\\*\\*(.*?)\\*\\*/);\n            if (linkMatch && (!boldMatch || linkMatch.index < boldMatch.index)) {\n              let href = linkMatch[2];\n              if (!href.startsWith(\"http\")) {\n                href = `${BACKEND_URL}${href.startsWith(\"/\") ? href : \"/\" + href}`;\n              }\n              if ((href.startsWith(`${BACKEND_URL}/api/files/`) || href.startsWith(\"/api/files/\")) && accessToken && !href.includes(\"token=\")) {\n                href += href.includes(\"?\") ? `&token=${accessToken}` : `?token=${accessToken}`;\n              }\n              parts.push(/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [remaining.slice(0, linkMatch.index), /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: href,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  style: {\n                    color: \"#0645AD\",\n                    textDecoration: \"underline\"\n                  },\n                  children: linkMatch[1]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1214,\n                  columnNumber: 23\n                }, this)]\n              }, keyIndex++, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1212,\n                columnNumber: 21\n              }, this));\n              remaining = remaining.slice(linkMatch.index + linkMatch[0].length);\n            } else if (boldMatch) {\n              parts.push(/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [remaining.slice(0, boldMatch.index), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: boldMatch[1]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1229,\n                  columnNumber: 23\n                }, this)]\n              }, keyIndex++, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1227,\n                columnNumber: 21\n              }, this));\n              remaining = remaining.slice(boldMatch.index + boldMatch[0].length);\n            } else {\n              parts.push(/*#__PURE__*/_jsxDEV(\"span\", {\n                children: remaining\n              }, keyIndex++, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1234,\n                columnNumber: 30\n              }, this));\n              break;\n            }\n          }\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            children: parts\n          }, idx, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1239,\n            columnNumber: 22\n          }, this);\n        }), message.tickets && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: \"8px\"\n          },\n          children: message.tickets.map((ticket, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              cursor: \"pointer\",\n              padding: \"6px 4px\",\n              borderBottom: idx !== message.tickets.length - 1 ? \"1px solid #eee\" : \"none\"\n            },\n            onClick: () => handleTicketSelect(ticket.ticketNumber),\n            children: [ticket.index, \". \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: ticket.ticketNumber\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1254,\n              columnNumber: 37\n            }, this), \" \\u2014 \", ticket.title]\n          }, ticket.ticketNumber, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1244,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1242,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1180,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-time\",\n        style: {\n          fontSize: \"0.7em\",\n          color: \"#666\",\n          marginTop: \"6px\"\n        },\n        children: formatTime(message.timestamp)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1260,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1170,\n      columnNumber: 9\n    }, this)\n  }, message.id, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1164,\n    columnNumber: 7\n  }, this));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chat-container\",\n    role: \"main\",\n    \"aria-label\": \"AI Agent Chatbot\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-header\",\n      style: {\n        position: \"relative\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"ONLINE SOLUTIONS TECHNICAL SUPPORT\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1273,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"subtitle\",\n        children: \"Technical Documentation Assistant\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1274,\n        columnNumber: 9\n      }, this), username && /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          fontSize: \"0.9em\",\n          color: \"#444\"\n        },\n        children: [\"Logged in as: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: username\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1277,\n          columnNumber: 27\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1276,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: \"absolute\",\n          top: 15,\n          right: 15\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleLogout,\n          style: {\n            padding: \"6px 12px\",\n            cursor: \"pointer\",\n            backgroundColor: \"#d9534f\",\n            border: \"none\",\n            borderRadius: 4,\n            color: \"white\",\n            fontWeight: \"bold\"\n          },\n          \"aria-label\": \"Logout\",\n          title: \"Logout\",\n          children: \"Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1281,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1280,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1272,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-messages\",\n      \"aria-live\": \"polite\",\n      \"aria-relevant\": \"additions\",\n      children: [renderMessages(), (loading || verifying) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message bot typing\",\n        \"aria-label\": \"Analyzing\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"message-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"typing-indicator\",\n            \"aria-hidden\": \"true\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1307,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1308,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1309,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1306,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"message-text\",\n            children: verifying ? \"Verifying organization...\" : \"Analyzing...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1311,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1305,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1304,\n        columnNumber: 11\n      }, this), pendingFiles && pendingFiles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: \"10px\",\n          textAlign: \"center\"\n        },\n        children: /*#__PURE__*/_jsxDEV(YesNoButtons, {\n          onYes: () => handleYesNoResponse(true, \"file_download\"),\n          onNo: () => handleYesNoResponse(false, \"file_download\"),\n          yesText: \"Download File\",\n          noText: \"Skip\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1321,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1320,\n        columnNumber: 11\n      }, this), awaitingOtherQueries && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: \"10px\",\n          textAlign: \"center\"\n        },\n        children: /*#__PURE__*/_jsxDEV(YesNoButtons, {\n          onYes: () => handleYesNoResponse(true, \"more_queries\"),\n          onNo: () => handleYesNoResponse(false, \"more_queries\"),\n          yesText: \"Yes\",\n          noText: \"No\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1333,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1332,\n        columnNumber: 11\n      }, this), awaitingCloseConfirmation && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: \"10px\",\n          textAlign: \"center\"\n        },\n        children: /*#__PURE__*/_jsxDEV(YesNoButtons, {\n          onYes: () => handleYesNoResponse(true, \"close_ticket\"),\n          onNo: () => handleYesNoResponse(false, \"close_ticket\"),\n          yesText: \"Close Ticket\",\n          noText: \"Keep Open\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1345,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1344,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1354,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1301,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      onClick: () => setError(\"\"),\n      style: {\n        cursor: \"pointer\"\n      },\n      role: \"alert\",\n      \"aria-live\": \"assertive\",\n      tabIndex: 0,\n      children: [error, \" (click to dismiss)\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1358,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      className: \"chat-input-form\",\n      onSubmit: handleSubmit,\n      \"aria-label\": \"Send message form\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-container\",\n        style: {\n          display: \"flex\",\n          alignItems: \"center\",\n          width: \"100%\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: query,\n          onChange: onInputChange,\n          placeholder: orgVerified ? ticketStep > 0 ? ticketQuestions[ticketStep - 1] : awaitingUnrelatedQueryResponse ? \"Answer 'yes' or 'no' to create a new ticket...\" : awaitingCloseConfirmation ? \"Answer 'yes' or 'no' to close the ticket...\" : \"Type your question here...\" : \"Enter your organization name...\",\n          disabled: loading || verifying || ticketStep === 1 && !query,\n          autoFocus: true,\n          \"aria-label\": \"Chat input\",\n          style: {\n            flex: \"1\",\n            width: \"100%\",\n            padding: \"8px\",\n            borderRadius: \"4px 0 0 4px\",\n            border: \"1px solid #ccc\",\n            margin: 0\n          },\n          onKeyDown: e => {\n            if (e.key === \"Enter\" && !e.shiftKey) {\n              e.preventDefault();\n              handleSubmit(e);\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1372,\n          columnNumber: 11\n        }, this), ticketStep === 1 && /*#__PURE__*/_jsxDEV(\"select\", {\n          value: ticketData.productType,\n          onChange: handleProductTypeChange,\n          style: {\n            marginLeft: \"0\",\n            padding: \"6px\",\n            borderRadius: \"0\",\n            border: \"1px solid #ccc\",\n            borderLeft: \"none\",\n            fontSize: \"1em\"\n          },\n          \"aria-label\": \"Select product type\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            disabled: true,\n            children: \"Select a product type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1412,\n            columnNumber: 15\n          }, this), productTypeOptions.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: option,\n            children: option\n          }, option, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1416,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1399,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: loading || !query.trim() || verifying || ticketStep === 1 && !ticketData.productType,\n          title: loading || verifying ? \"Please wait...\" : \"Send\",\n          \"aria-label\": \"Send message\",\n          style: {\n            padding: \"8px 12px\",\n            borderRadius: \"0 4px 4px 0\",\n            border: \"1px solid #ccc\",\n            borderLeft: \"none\",\n            backgroundColor: \"#4CAF50\",\n            color: \"white\"\n          },\n          children: loading || verifying ? /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1429,\n            columnNumber: 37\n          }, this) : \"📤\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1422,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1371,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1370,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1271,\n    columnNumber: 5\n  }, this);\n}\n_s(Home, \"vPLbo8uMWC1f7E0LLqeAAxrHQkQ=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "YesNoButtons", "FileDownloadButtons", "TicketCloseButtons", "jsxDEV", "_jsxDEV", "BACKEND_URL", "Home", "token", "_s", "accessToken", "localStorage", "getItem", "query", "<PERSON><PERSON><PERSON><PERSON>", "messages", "setMessages", "promptTemplate", "setPromptTemplate", "loading", "setLoading", "error", "setError", "mode", "setMode", "pendingFiles", "setPendingFiles", "orgVerified", "setOrgVerified", "verifying", "setVerifying", "username", "setUserName", "askRaiseTicket", "setAskRaiseTicket", "awaitingCloseConfirmation", "setAwaitingCloseConfirmation", "awaiting<PERSON><PERSON><PERSON><PERSON><PERSON>", "setAwaiting<PERSON><PERSON><PERSON><PERSON><PERSON>", "pendingTickets", "setPendingTickets", "awaitingPendingChoice", "setAwaitingPendingChoice", "awaitingTicketSelect", "setAwaitingTicketSelect", "activeTicket", "setActiveTicket", "ticketRefused", "setTicketRefused", "queriesAfterNoTicket", "setQueriesAfterNoTicket", "awaitingUnrelatedQueryResponse", "setAwaitingUnrelatedQueryResponse", "MAX_QUERIES_AFTER_NO_TICKET", "tickets", "setTickets", "showTickets", "setShowTickets", "ticketQueryCount", "setTicketQueryCount", "ticketStep", "setTicketStep", "ticketData", "setTicketData", "productType", "purchasedFrom", "yearOfPurchase", "productName", "model", "serialNo", "operatingSystem", "awaitingProblemDescription", "setAwaitingProblemDescription", "currentTicketNumber", "setCurrentTicketNumber", "ticketQuestions", "productTypeOptions", "_messagesEndRef$curre", "messagesEndRef", "current", "scrollIntoView", "behavior", "fetch", "then", "res", "json", "data", "template", "catch", "err", "console", "urlParams", "URLSearchParams", "window", "location", "search", "isPendingMode", "get", "isNewTicketMode", "ticketNumber", "loadPendingTicketDirectly", "loadNewTicket", "headers", "Authorization", "ok", "errorText", "text", "status", "Error", "name", "email", "id", "type", "content", "timestamp", "Date", "message", "removeItem", "href", "userResponse", "userData", "ticketResponse", "ticket", "short_title", "problem_description", "ticketsResponse", "ticketsData", "length", "latestTicket", "ticket_number", "title", "handleFileDownload", "file", "link", "document", "createElement", "url", "download", "source_file", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "prev", "filter", "msg", "files", "addBot", "handleTicketClose", "response", "method", "JSON", "stringify", "setTimeout", "handleLogout", "handleYesNoResponse", "context", "userMsg", "now", "onInputChange", "e", "target", "value", "handleProductTypeChange", "selectedType", "verifyOrganization", "orgName", "organization", "fetchPendingTickets", "baseUrl", "origin", "signupLink", "submitTicket", "finalTicketData", "validProductTypes", "allFilled", "Object", "values", "every", "v", "trim", "includes", "product_type", "purchased_from", "year_of_purchase", "product_name", "serial_no", "operating_system", "errors", "handleTicketSelect", "summaryRes", "summaryData", "problem_summary", "solution_summary", "handleSubmit", "preventDefault", "<PERSON><PERSON><PERSON><PERSON>", "toLowerCase", "n", "map", "t", "i", "index", "picked", "find", "idx", "String", "keys", "current<PERSON><PERSON>", "updatedTicketData", "_saveData$files", "saveRes", "saveData", "botContent", "answer", "rawFiles", "validFiles", "f", "startsWith", "filename", "encodeURIComponent", "baseMessages", "downloadLinks", "join", "push", "historyText", "m", "finalPrompt", "replace", "ticket_mode", "ticket_id", "stage", "_data$files", "formatTime", "toLocaleTimeString", "hour", "minute", "renderMessages", "className", "style", "textAlign", "children", "display", "max<PERSON><PERSON><PERSON>", "padding", "borderRadius", "backgroundColor", "whiteSpace", "overflowY", "maxHeight", "fontSize", "lineHeight", "split", "line", "parts", "remaining", "keyIndex", "linkMatch", "match", "boldMatch", "slice", "rel", "color", "textDecoration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "cursor", "borderBottom", "onClick", "role", "position", "top", "right", "border", "fontWeight", "onYes", "onNo", "yesText", "noText", "ref", "tabIndex", "onSubmit", "alignItems", "width", "onChange", "placeholder", "disabled", "autoFocus", "flex", "margin", "onKeyDown", "key", "shift<PERSON>ey", "marginLeft", "borderLeft", "option", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/AI-Agent-Chatbot-main/frontend/src/Home.jsx"], "sourcesContent": ["\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { YesNoButtons, FileDownloadButtons, TicketCloseButtons } from \"./YesNoButtons\";\r\nimport \"./App.css\";\r\n\r\n// Define the backend URL\r\nconst BACKEND_URL = \"http://localhost:8000\";\r\n\r\nexport default function Home({ token }) {\r\n  const accessToken = token || localStorage.getItem(\"access\");\r\n\r\n  // --- States ---\r\n  const [query, setQuery] = useState(\"\");\r\n  const [messages, setMessages] = useState([]);\r\n  const [promptTemplate, setPromptTemplate] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(\"\");\r\n  const [mode, setMode] = useState(\"strict\");\r\n  const [pendingFiles, setPendingFiles] = useState(null);\r\n  const [orgVerified, setOrgVerified] = useState(false);\r\n  const [verifying, setVerifying] = useState(false);\r\n  const [username, setUserName] = useState(\"\");\r\n  const [askRaiseTicket, setAskRaiseTicket] = useState(false);\r\n  const [awaitingCloseConfirmation, setAwaitingCloseConfirmation] = useState(false);\r\n  const [awaitingOtherQueries, setAwaitingOtherQueries] = useState(false);\r\n  const [pendingTickets, setPendingTickets] = useState([]);\r\n  const [awaitingPendingChoice, setAwaitingPendingChoice] = useState(false);\r\n  const [awaitingTicketSelect, setAwaitingTicketSelect] = useState(false);\r\n  const [activeTicket, setActiveTicket] = useState(null);\r\n  const [ticketRefused, setTicketRefused] = useState(false);\r\n  const [queriesAfterNoTicket, setQueriesAfterNoTicket] = useState(0);\r\n  const [awaitingUnrelatedQueryResponse, setAwaitingUnrelatedQueryResponse] = useState(false);\r\n  const MAX_QUERIES_AFTER_NO_TICKET = 10;\r\n  const [tickets, setTickets] = useState([]);\r\n  const [showTickets, setShowTickets] = useState(false);\r\n  const [ticketQueryCount, setTicketQueryCount] = useState(0);\r\n\r\n\r\n  // --- New ticket states ---\r\n  const [ticketStep, setTicketStep] = useState(0);\r\n  const [ticketData, setTicketData] = useState({\r\n    productType: \"\",\r\n    purchasedFrom: \"\",\r\n    yearOfPurchase: \"\",\r\n    productName: \"\",\r\n    model: \"\",\r\n    serialNo: \"\",\r\n    operatingSystem: \"\",\r\n  });\r\n  const [awaitingProblemDescription, setAwaitingProblemDescription] = useState(false);\r\n  const [currentTicketNumber, setCurrentTicketNumber] = useState(null);\r\n\r\n  const ticketQuestions = [\r\n    \"Please select the product type using the dropdown below:\",\r\n    \"Please enter the 'Purchased From' information:\",\r\n    \"Please enter the 'Year of Purchase':\",\r\n    \"Please enter the 'Product Name':\",\r\n    \"Please enter the 'Model':\",\r\n    \"Please enter the 'Serial Number':\",\r\n    \"Please enter the 'Operating System':\",\r\n  ];\r\n\r\n  const productTypeOptions = [\"Camera\", \"Frame Grabber\", \"Accessories\", \"Software\"];\r\n\r\n  // Auto-scroll messages\r\n  useEffect(() => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  }, [messages, loading, error]);\r\n\r\n  const messagesEndRef = useRef(null);\r\n\r\n  // Fetch prompt template\r\n  useEffect(() => {\r\n    fetch(`${BACKEND_URL}/api/prompt/?type=chat`)\r\n      .then((res) => res.json())\r\n      .then((data) => setPromptTemplate(data.template))\r\n      .catch((err) => {\r\n        console.error(\"Failed to fetch prompt template:\", err);\r\n        setPromptTemplate(null);\r\n      });\r\n  }, []);\r\n\r\n  // Check for pending mode or new ticket mode from URL\r\n  useEffect(() => {\r\n    const urlParams = new URLSearchParams(window.location.search);\r\n    const isPendingMode = urlParams.get('mode') === 'pending';\r\n    const isNewTicketMode = urlParams.get('mode') === 'new';\r\n    const ticketNumber = urlParams.get('ticket');\r\n\r\n    if (isPendingMode) {\r\n      // Skip organization verification and directly load pending ticket\r\n      loadPendingTicketDirectly();\r\n    } else if (isNewTicketMode && ticketNumber) {\r\n      // Load the newly created ticket\r\n      loadNewTicket(ticketNumber);\r\n    }\r\n  }, []);\r\n\r\n  // Fetch username and welcome message\r\n  useEffect(() => {\r\n    if (!accessToken) return;\r\n\r\n    const urlParams = new URLSearchParams(window.location.search);\r\n    const isPendingMode = urlParams.get('mode') === 'pending';\r\n\r\n    if (isPendingMode) {\r\n      return; // Skip normal flow for pending mode\r\n    }\r\n\r\n    fetch(`${BACKEND_URL}/api/user_info/`, {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        Authorization: `Bearer ${accessToken}`,\r\n      },\r\n    })\r\n      .then(async (res) => {\r\n        if (!res.ok) {\r\n          const errorText = await res.text();\r\n          console.error(\"User info fetch failed:\", res.status, errorText);\r\n          throw new Error(\"User info fetch failed\");\r\n        }\r\n        return res.json();\r\n      })\r\n      .then((data) => {\r\n        const name = data.name || data.username || data.email;\r\n        if (!name) throw new Error(\"Name missing in response\");\r\n\r\n        setUserName(name);\r\n        setMessages([\r\n          {\r\n            id: 1,\r\n            type: \"bot\",\r\n            content: `👋 Welcome, ${name}! Please enter your organization name to verify your account.`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n      })\r\n      .catch((err) => {\r\n        console.error(\"Failed to fetch user info:\", err.message);\r\n        localStorage.removeItem(\"access\");\r\n        window.location.href = \"/auth\";\r\n      });\r\n  }, [accessToken]);\r\n\r\n  // Load new ticket (for new ticket mode)\r\n  const loadNewTicket = async (ticketNumber) => {\r\n    if (!accessToken) return;\r\n\r\n    try {\r\n      // Get user info\r\n      const userResponse = await fetch(`${BACKEND_URL}/api/user_info/`, {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n      });\r\n\r\n      if (!userResponse.ok) {\r\n        throw new Error(\"Failed to fetch user info\");\r\n      }\r\n\r\n      const userData = await userResponse.json();\r\n      const name = userData.name || userData.username || userData.email;\r\n      setUserName(name);\r\n      setOrgVerified(true);\r\n      setActiveTicket(ticketNumber);\r\n\r\n      // Fetch the ticket details to show generated content\r\n      try {\r\n        const ticketResponse = await fetch(`${BACKEND_URL}/api/ticket/${ticketNumber}/`, {\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: `Bearer ${accessToken}`,\r\n          },\r\n        });\r\n\r\n        if (ticketResponse.ok) {\r\n          const ticketData = await ticketResponse.json();\r\n\r\n          setMessages([\r\n            {\r\n              id: 1,\r\n              type: \"bot\",\r\n              content: `👋 Welcome, ${name}! Your new support ticket ${ticketNumber} has been created successfully.`,\r\n              timestamp: new Date(),\r\n            },\r\n            {\r\n              id: 2,\r\n              type: \"bot\",\r\n              content: `🎫 **Ticket Created:** ${ticketNumber}\\n\\n**Generated Title:** ${ticketData.ticket.short_title || 'No title'}\\n\\n**Generated Problem Description:** ${ticketData.ticket.problem_description || 'No description'}\\n\\nHow can I help you with this issue?`,\r\n              timestamp: new Date(),\r\n            },\r\n          ]);\r\n        } else {\r\n          // Fallback if ticket details can't be fetched\r\n          setMessages([\r\n            {\r\n              id: 1,\r\n              type: \"bot\",\r\n              content: `👋 Welcome, ${name}! Your new support ticket ${ticketNumber} has been created successfully.`,\r\n              timestamp: new Date(),\r\n            },\r\n            {\r\n              id: 2,\r\n              type: \"bot\",\r\n              content: `🎫 **Ticket Created:** ${ticketNumber}\\n\\nHow can I help you with your issue?`,\r\n              timestamp: new Date(),\r\n            },\r\n          ]);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Failed to fetch ticket details:\", err);\r\n        // Fallback messages\r\n        setMessages([\r\n          {\r\n            id: 1,\r\n            type: \"bot\",\r\n            content: `👋 Welcome, ${name}! Your new support ticket ${ticketNumber} has been created successfully.`,\r\n            timestamp: new Date(),\r\n          },\r\n          {\r\n            id: 2,\r\n            type: \"bot\",\r\n            content: `🎫 **Ticket Created:** ${ticketNumber}\\n\\nHow can I help you with your issue?`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Failed to load new ticket:\", err);\r\n      setError(\"Failed to load ticket. Please try again.\");\r\n    }\r\n  };\r\n\r\n  // Load pending ticket directly (for pending mode)\r\n  const loadPendingTicketDirectly = async () => {\r\n    if (!accessToken) return;\r\n\r\n    try {\r\n      // First get user info\r\n      const userResponse = await fetch(`${BACKEND_URL}/api/user_info/`, {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n      });\r\n\r\n      if (!userResponse.ok) {\r\n        throw new Error(\"Failed to fetch user info\");\r\n      }\r\n\r\n      const userData = await userResponse.json();\r\n      const name = userData.name || userData.username || userData.email;\r\n      setUserName(name);\r\n      setOrgVerified(true);\r\n\r\n      // Fetch pending tickets\r\n      const ticketsResponse = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n      });\r\n\r\n      if (!ticketsResponse.ok) {\r\n        throw new Error(\"Failed to fetch pending tickets\");\r\n      }\r\n\r\n      const ticketsData = await ticketsResponse.json();\r\n\r\n      if (ticketsData.tickets && ticketsData.tickets.length > 0) {\r\n        const latestTicket = ticketsData.tickets[0]; // Get the latest ticket\r\n        setActiveTicket(latestTicket.ticket_number);\r\n\r\n        setMessages([\r\n          {\r\n            id: 1,\r\n            type: \"bot\",\r\n            content: `👋 Welcome back, ${name}! Continuing with your ticket: ${latestTicket.ticket_number}`,\r\n            timestamp: new Date(),\r\n          },\r\n          {\r\n            id: 2,\r\n            type: \"bot\",\r\n            content: `📋 **Ticket Details:**\\n**Title:** ${latestTicket.title || latestTicket.short_title || 'No title'}\\n**Problem:** ${latestTicket.problem_description || 'No description available'}\\n\\nHow can I help you with this ticket?`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n      } else {\r\n        setMessages([\r\n          {\r\n            id: 1,\r\n            type: \"bot\",\r\n            content: `👋 Welcome, ${name}! No pending tickets found. Please go back to create a new ticket.`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Failed to load pending ticket:\", err);\r\n      setError(\"Failed to load pending ticket. Please try again.\");\r\n    }\r\n  };\r\n\r\n  // File download handler\r\n  const handleFileDownload = (file) => {\r\n    // Create a temporary link and trigger download\r\n    const link = document.createElement('a');\r\n    link.href = file.url;\r\n    link.download = file.source_file;\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n\r\n    // Remove the file download message from chat\r\n    setMessages(prev => prev.filter(msg => !msg.files || msg.files.length === 0));\r\n    setPendingFiles(null);\r\n\r\n    // Add confirmation message\r\n    addBot(\"✅ File downloaded successfully! Do you have any more questions about this ticket?\");\r\n    setAwaitingOtherQueries(true);\r\n  };\r\n\r\n  // Ticket closure handler\r\n  const handleTicketClose = async () => {\r\n    if (!activeTicket) return;\r\n\r\n    try {\r\n      const response = await fetch(`${BACKEND_URL}/api/update_ticket_status/`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n        body: JSON.stringify({\r\n          ticket_number: activeTicket,\r\n          status: \"closed\"\r\n        }),\r\n      });\r\n\r\n      if (response.ok) {\r\n        addBot(`✅ Ticket ${activeTicket} has been closed successfully. Thank you for using our support system!`);\r\n\r\n        // Auto logout after 3 seconds\r\n        setTimeout(() => {\r\n          handleLogout();\r\n        }, 3000);\r\n      } else {\r\n        addBot(\"❌ Failed to close the ticket. Please try again.\");\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error closing ticket:\", err);\r\n      addBot(\"❌ Error occurred while closing the ticket. Please try again.\");\r\n    }\r\n  };\r\n\r\n  // Handle yes/no responses\r\n  const handleYesNoResponse = (response, context) => {\r\n    const userMsg = {\r\n      id: Date.now(),\r\n      type: \"user\",\r\n      content: response ? \"Yes\" : \"No\",\r\n      timestamp: new Date(),\r\n    };\r\n    setMessages((prev) => [...prev, userMsg]);\r\n\r\n    if (context === \"file_download\") {\r\n      if (response && pendingFiles && pendingFiles.length > 0) {\r\n        handleFileDownload(pendingFiles[0]);\r\n      } else {\r\n        setPendingFiles(null);\r\n        addBot(\"Do you have any more questions about this ticket?\");\r\n        setAwaitingOtherQueries(true);\r\n      }\r\n    } else if (context === \"more_queries\") {\r\n      setAwaitingOtherQueries(false);\r\n      if (response) {\r\n        addBot(\"Please ask your question:\");\r\n      } else {\r\n        addBot(\"Do you want to close this ticket?\");\r\n        setAwaitingCloseConfirmation(true);\r\n      }\r\n    } else if (context === \"close_ticket\") {\r\n      setAwaitingCloseConfirmation(false);\r\n      if (response) {\r\n        handleTicketClose();\r\n      } else {\r\n        addBot(\"Ticket remains open. How else can I help you?\");\r\n      }\r\n    }\r\n  };\r\n\r\n  // Input change handler\r\n  const onInputChange = (e) => {\r\n    if (error) setError(\"\");\r\n    setQuery(e.target.value);\r\n  };\r\n\r\n  // Dropdown change handler for productType\r\n  const handleProductTypeChange = (e) => {\r\n    const selectedType = e.target.value;\r\n    setTicketData({ ...ticketData, productType: selectedType });\r\n    setMessages((prev) => [\r\n      ...prev,\r\n      {\r\n        id: Date.now(),\r\n        type: \"user\",\r\n        content: `Selected product type: ${selectedType}`,\r\n        timestamp: new Date(),\r\n      },\r\n    ]);\r\n    setTicketStep(ticketStep + 1);\r\n    addBot(ticketQuestions[ticketStep]);\r\n  };\r\n\r\n  // Verify organization name\r\n  const verifyOrganization = async (orgName) => {\r\n    setVerifying(true);\r\n    setError(\"\");\r\n\r\n    try {\r\n      const response = await fetch(`${BACKEND_URL}/api/verify_organization/`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n        body: JSON.stringify({ organization: orgName }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok && data.status === \"verified\") {\r\n        setOrgVerified(true);\r\n        setUserName(data.name || username);\r\n\r\n        setMessages((prev) => [\r\n          ...prev,\r\n          {\r\n            id: Date.now(),\r\n            type: \"bot\",\r\n            content: data.message || \"✅ Organization verified.\",\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n\r\n        await fetchPendingTickets();\r\n      } else {\r\n        const baseUrl = window.location.origin;\r\n        const signupLink = `[sign up here](${baseUrl}/signup/)`;\r\n\r\n        setError(data.message || \"❌ Organization mismatch.\");\r\n        setMessages((prev) => [\r\n          ...prev,\r\n          {\r\n            id: Date.now(),\r\n            type: \"bot\",\r\n            content: `❌ Organization verification failed.\\n\\nIf you belong to a new organization, please ${signupLink} to register first.`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n\r\n        localStorage.removeItem(\"access\");\r\n      }\r\n    } catch (err) {\r\n      setError(\"Network error during verification.\");\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: \"❌ Network error during organization verification. Please try again.\",\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n      console.error(\"Verification error:\", err);\r\n    } finally {\r\n      setVerifying(false);\r\n    }\r\n  };\r\n\r\n  // Helper function to fetch pending tickets after verification\r\n  async function fetchPendingTickets() {\r\n    try {\r\n      const response = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {\r\n        headers: {\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n      });\r\n      const data = await response.json();\r\n\r\n      if (response.ok) {\r\n        setPendingTickets(data.tickets || []);\r\n        if (data.tickets && data.tickets.length > 0) {\r\n          setAwaitingPendingChoice(true);\r\n          addBot(\r\n            `You have ${data.tickets.length} pending ticket(s). Do you want to continue with any of them? (yes/no)`\r\n          );\r\n        } else {\r\n          setAskRaiseTicket(true);\r\n          addBot(\"No pending tickets found. Would you like to raise a support ticket? (yes/no)\");\r\n        }\r\n      } else {\r\n        setPendingTickets([]);\r\n        setAskRaiseTicket(true);\r\n        addBot(\"Could not fetch pending tickets. Would you like to raise a support ticket? ( Juno/no)\");\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error fetching pending tickets:\", err);\r\n      setPendingTickets([]);\r\n      setAskRaiseTicket(true);\r\n      addBot(\"Error fetching pending tickets. Would you like to raise a support ticket? (yes/no)\");\r\n    }\r\n  }\r\n\r\n  // Submit ticket with latest data\r\n  const submitTicket = async (finalTicketData) => {\r\n    setLoading(true);\r\n    setError(\"\");\r\n\r\n    const validProductTypes = [\"Camera\", \"Frame Grabber\", \"Accessories\", \"Software\"];\r\n    const allFilled = Object.values(finalTicketData).every(\r\n      (v) => v && v.trim() !== \"\"\r\n    );\r\n    if (!allFilled) {\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: \"❌ Please fill in all required fields before submitting the ticket.\",\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n      setLoading(false);\r\n      return;\r\n    }\r\n    if (!validProductTypes.includes(finalTicketData.productType)) {\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: \"❌ Invalid product type. Please select: Camera, Frame Grabber, Accessories, or Software.\",\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n        body: JSON.stringify({\r\n          product_type: finalTicketData.productType,\r\n          purchased_from: finalTicketData.purchasedFrom,\r\n          year_of_purchase: finalTicketData.yearOfPurchase,\r\n          product_name: finalTicketData.productName,\r\n          model: finalTicketData.model,\r\n          serial_no: finalTicketData.serialNo,\r\n          operating_system: finalTicketData.operatingSystem,\r\n        }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok) {\r\n        setCurrentTicketNumber(data.ticket_number);\r\n        setActiveTicket(data.ticket_number);\r\n        setAwaitingProblemDescription(true);\r\n        setMessages((prev) => [\r\n          ...prev,\r\n          {\r\n            id: Date.now(),\r\n            type: \"bot\",\r\n            content: `🎉 Thank you! Your support ticket has been created successfully. Your ticket number is **${data.ticket_number}**.\\n\\nPlease describe your problem so we can assist you.`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n      } else {\r\n        setMessages((prev) => [\r\n          ...prev,\r\n          {\r\n            id: Date.now(),\r\n            type: \"bot\",\r\n            content: `❌ Failed to create ticket: ${JSON.stringify(data.errors || data.message)}`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n      }\r\n    } catch (err) {\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: `❌ Network error while creating ticket: ${err.message}`,\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n    } finally {\r\n      setLoading(false);\r\n      setTicketStep(0);\r\n      setAskRaiseTicket(false);\r\n      setTicketData({\r\n        productType: \"\",\r\n        purchasedFrom: \"\",\r\n        yearOfPurchase: \"\",\r\n        productName: \"\",\r\n        model: \"\",\r\n        serialNo: \"\",\r\n        operatingSystem: \"\",\r\n      });\r\n    }\r\n  };\r\n\r\n  // Helper to add a bot message\r\n  function addBot(text) {\r\n    setMessages((prev) => [\r\n      ...prev,\r\n      { id: Date.now(), type: \"bot\", content: text, timestamp: new Date() },\r\n    ]);\r\n  }\r\n\r\n  // Handle ticket selection from UI\r\n  const handleTicketSelect = async (ticketNumber) => {\r\n    setAwaitingTicketSelect(false);\r\n    setActiveTicket(ticketNumber);\r\n    setCurrentTicketNumber(ticketNumber);\r\n    setShowTickets(false);\r\n\r\n    try {\r\n      const summaryRes = await fetch(`${BACKEND_URL}/api/ticket_summary/`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n        body: JSON.stringify({ ticket_number: ticketNumber }),\r\n      });\r\n\r\n      const summaryData = await summaryRes.json();\r\n\r\n      if (summaryRes.ok) {\r\n        addBot(\r\n          `🔄 Resuming ticket **${ticketNumber}** …\\n\\n` +\r\n            `📝 **Raised problem:** ${summaryData.problem_summary || summaryData.problem_description}\\n\\n` +\r\n            `💡 **Given solution:** ${summaryData.solution_summary || \"No solution yet.\"}\\n\\n` +\r\n            \"✅ You can ask your follow-up query now.\"\r\n        );\r\n      } else {\r\n        addBot(\"⚠️ Error fetching ticket summary.\");\r\n      }\r\n    } catch (err) {\r\n      addBot(\"❌ Network error while fetching ticket summary.\");\r\n    }\r\n  };\r\n\r\n  // FULLY UPDATED handleSubmit\r\nasync function handleSubmit(e) {\r\n  e.preventDefault();\r\n  if (!query.trim() || loading || verifying) return;\r\n\r\n  const currentQuery = query.trim().toLowerCase();\r\n\r\n  // --- New: Check 5 queries per ticket limit ---\r\n  if (\r\n    activeTicket &&            // There is an active ticket\r\n    ticketStep === 0 &&        // Not in ticket creation steps\r\n    !awaitingProblemDescription && // Not waiting for problem description input\r\n    ticketQueryCount >= 5      // Limit reached\r\n  ) {\r\n    addBot(\r\n      \"🛑 You have reached the maximum of five queries for this ticket. It has been automatically escalated to ensure prompt resolution. Kindly create a new ticket for any further inquiries or await our team’s response.\"\r\n    );\r\n    setQuery(\"\");\r\n    return;\r\n  }\r\n  // ---------------------------------------------\r\n\r\n  if (ticketRefused) {\r\n    if (queriesAfterNoTicket >= MAX_QUERIES_AFTER_NO_TICKET) {\r\n      addBot(\r\n        \"⚠️ You have reached the maximum number of free queries. Please raise a support ticket for further assistance.\"\r\n      );\r\n      setQuery(\"\");\r\n      return;\r\n    } else {\r\n      setQueriesAfterNoTicket((n) => n + 1);\r\n    }\r\n  }\r\n\r\n  const userMsg = {\r\n    id: Date.now(),\r\n    type: \"user\",\r\n    content: query.trim(),\r\n    timestamp: new Date(),\r\n  };\r\n  setMessages((prev) => [...prev, userMsg]);\r\n  setQuery(\"\");\r\n  setError(\"\");\r\n\r\n  if (awaitingPendingChoice) {\r\n    setAwaitingPendingChoice(false);\r\n\r\n    if (currentQuery === \"yes\") {\r\n      setAwaitingTicketSelect(true);\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: `Select a ticket by typing its number:`,\r\n          timestamp: new Date(),\r\n          tickets: pendingTickets.map((t, i) => ({\r\n            index: i + 1,\r\n            ticketNumber: t.ticket_number,\r\n            title: t.title || t.short_title || \"No title\",\r\n          })),\r\n        },\r\n      ]);\r\n    } else if (currentQuery === \"no\") {\r\n      setAskRaiseTicket(true);\r\n      addBot(\"Do you want to raise a support ticket? (yes/no)\");\r\n    } else {\r\n      addBot(\"Please answer 'yes' or 'no'. Do you want to continue an open ticket?\");\r\n      setAwaitingPendingChoice(true);\r\n    }\r\n    return;\r\n  }\r\n\r\n  if (awaitingTicketSelect) {\r\n    const picked = pendingTickets.find(\r\n      (t, idx) =>\r\n        currentQuery === String(idx + 1) ||\r\n        currentQuery.includes(t.ticket_number.toLowerCase())\r\n    );\r\n\r\n    if (!picked) {\r\n      addBot(\"Ticket not recognised, please type its number.\");\r\n      return;\r\n    }\r\n\r\n    await handleTicketSelect(picked.ticket_number);\r\n    return;\r\n  }\r\n\r\n  if (!orgVerified) {\r\n    await verifyOrganization(currentQuery);\r\n    return;\r\n  }\r\n\r\n  if (askRaiseTicket) {\r\n    if (currentQuery === \"yes\") {\r\n      setAskRaiseTicket(false);\r\n      setTicketStep(1);\r\n      addBot(ticketQuestions[0]);\r\n      setTicketRefused(false);\r\n      setQueriesAfterNoTicket(0);\r\n\r\n      // --- New: Reset query count when new ticket starts ---\r\n      setTicketQueryCount(0);\r\n      // ------------------------------------------------------\r\n\r\n    } else if (currentQuery === \"no\") {\r\n      setAskRaiseTicket(false);\r\n      addBot(\"👍 Okay, no ticket will be raised. How else can I help you?\");\r\n      setTicketRefused(true);\r\n      setQueriesAfterNoTicket(0);\r\n    } else {\r\n      addBot(\"Please answer 'yes' or 'no'. Do you want to raise a support ticket?\");\r\n    }\r\n    return;\r\n  }\r\n\r\n  if (awaitingUnrelatedQueryResponse) {\r\n    setAwaitingUnrelatedQueryResponse(false);\r\n    if (currentQuery === \"yes\") {\r\n      setTicketStep(1);\r\n      setActiveTicket(null);\r\n      setCurrentTicketNumber(null);\r\n      addBot(ticketQuestions[0]);\r\n\r\n      // --- New: Reset query count when new ticket starts here too ---\r\n      setTicketQueryCount(0);\r\n      // ---------------------------------------------------------------\r\n\r\n    } else if (currentQuery === \"no\") {\r\n      setAwaitingCloseConfirmation(true);\r\n      addBot(\"Can I close this ticket now? (yes/no)\");\r\n    } else {\r\n      setAwaitingUnrelatedQueryResponse(true);\r\n      addBot(\"Please answer 'yes' or 'no'. Do you want to create a new ticket?\");\r\n    }\r\n    return;\r\n  }\r\n\r\n  if (ticketStep > 0 && ticketStep <= ticketQuestions.length) {\r\n    if (ticketStep === 1) {\r\n      const selectedType = query.trim();\r\n      if (productTypeOptions.includes(selectedType)) {\r\n        setTicketData({ ...ticketData, productType: selectedType });\r\n        setMessages((prev) => [\r\n          ...prev,\r\n          {\r\n            id: Date.now(),\r\n            type: \"user\",\r\n            content: `Selected product type: ${selectedType}`,\r\n            timestamp: new Date(),\r\n          },\r\n        ]);\r\n        setTicketStep(ticketStep + 1);\r\n        addBot(ticketQuestions[ticketStep]);\r\n      } else {\r\n        addBot(\r\n          \"Please select a valid product type from: Camera, Frame Grabber, Accessories, or Software.\"\r\n        );\r\n      }\r\n      return;\r\n    }\r\n    const keys = [\r\n      \"purchasedFrom\",\r\n      \"yearOfPurchase\",\r\n      \"productName\",\r\n      \"model\",\r\n      \"serialNo\",\r\n      \"operatingSystem\",\r\n    ];\r\n    const currentField = keys[ticketStep - 2];\r\n    const updatedTicketData = { ...ticketData, [currentField]: query.trim() };\r\n    setTicketData(updatedTicketData);\r\n\r\n    if (ticketStep < ticketQuestions.length) {\r\n      setTicketStep(ticketStep + 1);\r\n      addBot(ticketQuestions[ticketStep]);\r\n    } else {\r\n      await submitTicket(updatedTicketData);\r\n    }\r\n    return;\r\n  }\r\n\r\n  if (awaitingProblemDescription && currentTicketNumber) {\r\n    setAwaitingProblemDescription(false);\r\n    setLoading(true);\r\n\r\n    try {\r\n      const saveRes = await fetch(`${BACKEND_URL}/api/add_problem_description/`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n        body: JSON.stringify({\r\n          ticket_number: currentTicketNumber,\r\n          problem_description: userMsg.content,\r\n        }),\r\n      });\r\n\r\n      const saveData = await saveRes.json();\r\n      if (!saveRes.ok) {\r\n        throw new Error(saveData.error || \"Failed to save problem description.\");\r\n      }\r\n\r\n      let botContent = saveData.answer || \"No solution available at the moment.\";\r\n\r\n      const rawFiles = saveData.files ?? []; // Updated from saveData.related_files\r\n      const validFiles = rawFiles.filter((f) => {\r\n        if (typeof f === \"string\") {\r\n          return f.trim() !== \"\" && !f.toLowerCase().startsWith(\"none\");\r\n        }\r\n        if (typeof f === \"object\" && f !== null && f.filename) {\r\n          return (\r\n            f.filename.trim() !== \"\" &&\r\n            !f.filename.toLowerCase().startsWith(\"none\")\r\n          );\r\n        }\r\n        return false;\r\n      });\r\n\r\n      if (validFiles.length > 0) {\r\n        setPendingFiles(\r\n          validFiles.map((f) => {\r\n            const filename = typeof f === \"string\" ? f : f.filename;\r\n            const url =\r\n              typeof f === \"string\" || !f.url || !f.url.startsWith(\"http\")\r\n                ? `${BACKEND_URL}/api/files/${encodeURIComponent(\r\n                    filename\r\n                  )}?token=${accessToken}`\r\n                : `${f.url}${f.url.includes(\"?\") ? \"&\" : \"?\"}token=${accessToken}`;\r\n            return { source_file: filename, url };\r\n          })\r\n        );\r\n        botContent += \"\\n\\n💡 For full explanation, do you want the related file? (yes/no)\";\r\n      } else {\r\n        setPendingFiles(null);\r\n      }\r\n\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: botContent,\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n\r\n      setAwaitingOtherQueries(true);\r\n    } catch (err) {\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: `❌ Error processing problem description: ${err.message}`,\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n    return;\r\n  }\r\n\r\n  if (pendingFiles && (currentQuery === \"yes\" || currentQuery === \"no\")) {\r\n    const baseMessages = [];\r\n\r\n    if (currentQuery === \"yes\") {\r\n      const downloadLinks = pendingFiles\r\n        .map((f, idx) => `${idx + 1}. [${f.source_file}](${f.url})`)\r\n        .join(\"\\n\");\r\n\r\n      baseMessages.push({\r\n        id: Date.now() + 1,\r\n        type: \"bot\",\r\n        content: `📎 Here are the related files:\\n\\n${downloadLinks}`,\r\n        timestamp: new Date(),\r\n      });\r\n    } else {\r\n      baseMessages.push({\r\n        id: Date.now() + 1,\r\n        type: \"bot\",\r\n        content: \"👍 Okay, no files will be sent.\",\r\n        timestamp: new Date(),\r\n      });\r\n    }\r\n\r\n    if (activeTicket !== null) {\r\n      baseMessages.push({\r\n        id: Date.now() + 2,\r\n        type: \"bot\",\r\n        content: \"Do you have any other queries? (yes/no)\",\r\n        timestamp: new Date(),\r\n      });\r\n      setAwaitingOtherQueries(true);\r\n    }\r\n\r\n    setMessages((prev) => [...prev, ...baseMessages]);\r\n    setPendingFiles(null);\r\n    return;\r\n  }\r\n\r\n  if (awaitingOtherQueries) {\r\n    if (currentQuery === \"no\") {\r\n      setAwaitingOtherQueries(false);\r\n      setAwaitingCloseConfirmation(true);\r\n      addBot(\"Can I close this ticket now? (yes/no)\");\r\n    } else if (currentQuery === \"yes\") {\r\n      setAwaitingOtherQueries(false);\r\n      addBot(\"Please go ahead and ask your question.\");\r\n    } else {\r\n      addBot(\"Please answer 'yes' or 'no'. Do you have any other queries?\");\r\n    }\r\n    return;\r\n  }\r\n\r\n  if (awaitingCloseConfirmation) {\r\n    if (currentQuery === \"yes\") {\r\n      setAwaitingCloseConfirmation(false);\r\n      setLoading(true);\r\n      try {\r\n        const response = await fetch(`${BACKEND_URL}/api/update_ticket_status/`, {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: `Bearer ${accessToken}`,\r\n          },\r\n          body: JSON.stringify({\r\n            ticket_number: currentTicketNumber,\r\n            status: \"closed\",\r\n          }),\r\n        });\r\n        const data = await response.json();\r\n\r\n        if (response.ok) {\r\n          setActiveTicket(null);\r\n          setCurrentTicketNumber(null);\r\n          setTicketData({\r\n            productType: \"\",\r\n            purchasedFrom: \"\",\r\n            yearOfPurchase: \"\",\r\n            productName: \"\",\r\n            model: \"\",\r\n            serialNo: \"\",\r\n            operatingSystem: \"\",\r\n          });\r\n\r\n          addBot(`✅ Ticket ${currentTicketNumber} has been closed. Thank you!`);\r\n\r\n          // --- New: Reset query count when ticket is closed ---\r\n          setTicketQueryCount(0);\r\n          // -----------------------------------------------------\r\n\r\n        } else {\r\n          throw new Error(data.error || \"Failed to close ticket.\");\r\n        }\r\n      } catch (err) {\r\n        addBot(`❌ Error closing ticket: ${err.message}`);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    } else if (currentQuery === \"no\") {\r\n      setAwaitingCloseConfirmation(false);\r\n      addBot(\"Okay, ticket will remain open.\");\r\n    } else {\r\n      addBot(\"Please answer 'yes' or 'no'. Can I close this ticket now?\");\r\n    }\r\n    return;\r\n  }\r\n\r\n  setLoading(true);\r\n  try {\r\n    const historyText = messages\r\n      .filter((m) => m.type === \"user\" || m.type === \"bot\")\r\n      .map((m) => `${m.type === \"user\" ? \"User\" : \"Bot\"}: ${m.content}`)\r\n      .join(\"\\n\");\r\n\r\n    const finalPrompt = promptTemplate\r\n      ? promptTemplate\r\n          .replace(\"{context_text}\", \"some context text here\")\r\n          .replace(\"{history_text}\", historyText)\r\n          .replace(\"{query}\", query.trim())\r\n      : query.trim();\r\n\r\n    const response = await fetch(`${BACKEND_URL}/api/chat/`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        Authorization: `Bearer ${accessToken}`,\r\n      },\r\n      body: JSON.stringify({\r\n        query: finalPrompt,\r\n        ticket_mode: !!activeTicket,\r\n        ticket_id: activeTicket,\r\n        stage: awaitingUnrelatedQueryResponse ? \"unrelated_query\" : \"\",\r\n      }),\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (response.ok) {\r\n      let botContent = data.answer || \"…\";\r\n\r\n      if (data.stage === \"unrelated_query\") {\r\n        setAwaitingUnrelatedQueryResponse(true);\r\n      } else if (data.stage === \"create_new_ticket\") {\r\n        setTicketStep(1);\r\n        setActiveTicket(null);\r\n        setCurrentTicketNumber(null);\r\n        addBot(ticketQuestions[0]);\r\n\r\n        // --- New: Reset query count on new ticket here too ---\r\n        setTicketQueryCount(0);\r\n        // ------------------------------------------------------\r\n        return;\r\n      }\r\n\r\n      const rawFiles = data.files ?? [];\r\n      const validFiles = rawFiles.filter((f) => {\r\n        if (typeof f === \"string\") {\r\n          return f.trim() !== \"\" && !f.toLowerCase().startsWith(\"none\");\r\n        }\r\n        if (typeof f === \"object\" && f !== null && f.filename) {\r\n          return (\r\n            f.filename.trim() !== \"\" &&\r\n            !f.filename.toLowerCase().startsWith(\"none\")\r\n          );\r\n        }\r\n        return false;\r\n      });\r\n\r\n      if (validFiles.length > 0) {\r\n        setPendingFiles(\r\n          validFiles.map((f) => {\r\n            const filename = typeof f === \"string\" ? f : f.filename;\r\n            const url =\r\n              typeof f === \"string\" || !f.url || !f.url.startsWith(\"http\")\r\n                ? `${BACKEND_URL}/api/files/${encodeURIComponent(filename)}?token=${accessToken}`\r\n                : `${f.url}${f.url.includes(\"?\") ? \"&\" : \"?\"}token=${accessToken}`;\r\n            return {\r\n              source_file: filename,\r\n              url: url,\r\n            };\r\n          })\r\n        );\r\n        if (!botContent.toLowerCase().includes(\"do you want the related file\")) {\r\n          botContent += \"\\n\\n💡 For full explanation, do you want the related file? (yes/no)\";\r\n        }\r\n      } else {\r\n        setPendingFiles(null);\r\n      }\r\n\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        {\r\n          id: Date.now(),\r\n          type: \"bot\",\r\n          content: botContent,\r\n          timestamp: new Date(),\r\n        },\r\n      ]);\r\n\r\n      // --- New: Increment query count for ticket queries ---\r\n      if (activeTicket && ticketStep === 0) {\r\n        setTicketQueryCount((prev) => prev + 1);\r\n      }\r\n      // ------------------------------------------------------\r\n\r\n      if (data.stage === \"await_close\") {\r\n        setAwaitingCloseConfirmation(true);\r\n      }\r\n    } else {\r\n      setError(data.error || \"Error processing request\");\r\n    }\r\n  } catch (err) {\r\n    setError(\"Network error: \" + err.message);\r\n  } finally {\r\n    setLoading(false);\r\n  }\r\n}\r\n\r\n\r\n  // Logout handler\r\n  function handleLogout() {\r\n    localStorage.removeItem(\"access\");\r\n    localStorage.removeItem(\"refresh\");\r\n    localStorage.removeItem(\"userData\");\r\n    window.location.href = \"/auth\";\r\n  }\r\n\r\n  // Format message time HH:MM\r\n  const formatTime = (timestamp) => {\r\n    return timestamp.toLocaleTimeString([], { hour: \"2-digit\", minute: \"2-digit\" });\r\n  };\r\n\r\n  // Render chat messages with links parsed\r\n  const renderMessages = () =>\r\n    messages.map((message) => (\r\n      <div\r\n        key={message.id}\r\n        className={`message ${message.type}`}\r\n        style={{ textAlign: message.type === \"user\" ? \"right\" : \"left\" }}\r\n        aria-live=\"polite\"\r\n      >\r\n        <div\r\n          className=\"message-content\"\r\n          style={{\r\n            display: \"inline-block\",\r\n            maxWidth: \"75%\",\r\n            padding: \"8px 12px\",\r\n            borderRadius: \"12px\",\r\n            backgroundColor: message.type === \"user\" ? \"#DCF8C6\" : \"#F1F0F0\",\r\n          }}\r\n        >\r\n          <div\r\n            className=\"message-text\"\r\n            style={{\r\n              whiteSpace: \"pre-wrap\",\r\n              overflowY: \"auto\",\r\n              maxHeight: \"400px\",\r\n              fontSize: \"1em\",\r\n              lineHeight: \"1.5\",\r\n            }}\r\n          >\r\n            {message.content.split(\"\\n\").map((line, idx) => {\r\n              const parts = [];\r\n              let remaining = line;\r\n              let keyIndex = 0;\r\n\r\n              while (remaining.length > 0) {\r\n                const linkMatch = remaining.match(/\\[(.*?)\\]\\((http.*?)\\)/);\r\n                const boldMatch = remaining.match(/\\*\\*(.*?)\\*\\*/);\r\n\r\n                if (linkMatch && (!boldMatch || linkMatch.index < boldMatch.index)) {\r\n                  let href = linkMatch[2];\r\n                  if (!href.startsWith(\"http\")) {\r\n                    href = `${BACKEND_URL}${href.startsWith(\"/\") ? href : \"/\" + href}`;\r\n                  }\r\n                  if (\r\n                    (href.startsWith(`${BACKEND_URL}/api/files/`) || href.startsWith(\"/api/files/\")) &&\r\n                    accessToken &&\r\n                    !href.includes(\"token=\")\r\n                  ) {\r\n                    href += href.includes(\"?\") ? `&token=${accessToken}` : `?token=${accessToken}`;\r\n                  }\r\n                  parts.push(\r\n                    <span key={keyIndex++}>\r\n                      {remaining.slice(0, linkMatch.index)}\r\n                      <a\r\n                        href={href}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        style={{ color: \"#0645AD\", textDecoration: \"underline\" }}\r\n                      >\r\n                        {linkMatch[1]}\r\n                      </a>\r\n                    </span>\r\n                  );\r\n                  remaining = remaining.slice(linkMatch.index + linkMatch[0].length);\r\n                } else if (boldMatch) {\r\n                  parts.push(\r\n                    <span key={keyIndex++}>\r\n                      {remaining.slice(0, boldMatch.index)}\r\n                      <strong>{boldMatch[1]}</strong>\r\n                    </span>\r\n                  );\r\n                  remaining = remaining.slice(boldMatch.index + boldMatch[0].length);\r\n                } else {\r\n                  parts.push(<span key={keyIndex++}>{remaining}</span>);\r\n                  break;\r\n                }\r\n              }\r\n\r\n              return <div key={idx}>{parts}</div>;\r\n            })}\r\n            {message.tickets && (\r\n              <div style={{ marginTop: \"8px\" }}>\r\n                {message.tickets.map((ticket, idx) => (\r\n                  <div\r\n                    key={ticket.ticketNumber}\r\n                    style={{\r\n                      cursor: \"pointer\",\r\n                      padding: \"6px 4px\",\r\n                      borderBottom:\r\n                        idx !== message.tickets.length - 1 ? \"1px solid #eee\" : \"none\",\r\n                    }}\r\n                    onClick={() => handleTicketSelect(ticket.ticketNumber)}\r\n                  >\r\n                    {ticket.index}. <strong>{ticket.ticketNumber}</strong> — {ticket.title}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n          <div\r\n            className=\"message-time\"\r\n            style={{ fontSize: \"0.7em\", color: \"#666\", marginTop: \"6px\" }}\r\n          >\r\n            {formatTime(message.timestamp)}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    ));\r\n\r\n  return (\r\n    <div className=\"chat-container\" role=\"main\" aria-label=\"AI Agent Chatbot\">\r\n      <div className=\"chat-header\" style={{ position: \"relative\" }}>\r\n        <h1>ONLINE SOLUTIONS TECHNICAL SUPPORT</h1>\r\n        <p className=\"subtitle\">Technical Documentation Assistant</p>\r\n        {username && (\r\n          <p style={{ fontSize: \"0.9em\", color: \"#444\" }}>\r\n            Logged in as: <strong>{username}</strong>\r\n          </p>\r\n        )}\r\n        <div style={{ position: \"absolute\", top: 15, right: 15 }}>\r\n          <button\r\n            onClick={handleLogout}\r\n            style={{\r\n              padding: \"6px 12px\",\r\n              cursor: \"pointer\",\r\n              backgroundColor: \"#d9534f\",\r\n              border: \"none\",\r\n              borderRadius: 4,\r\n              color: \"white\",\r\n              fontWeight: \"bold\",\r\n            }}\r\n            aria-label=\"Logout\"\r\n            title=\"Logout\"\r\n          >\r\n            Logout\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* CHAT MESSAGES */}\r\n      <div className=\"chat-messages\" aria-live=\"polite\" aria-relevant=\"additions\">\r\n        {renderMessages()}\r\n        {(loading || verifying) && (\r\n          <div className=\"message bot typing\" aria-label=\"Analyzing\">\r\n            <div className=\"message-content\">\r\n              <div className=\"typing-indicator\" aria-hidden=\"true\">\r\n                <span></span>\r\n                <span></span>\r\n                <span></span>\r\n              </div>\r\n              <div className=\"message-text\">\r\n                {verifying ? \"Verifying organization...\" : \"Analyzing...\"}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Yes/No Buttons for File Download */}\r\n        {pendingFiles && pendingFiles.length > 0 && (\r\n          <div style={{ padding: \"10px\", textAlign: \"center\" }}>\r\n            <YesNoButtons\r\n              onYes={() => handleYesNoResponse(true, \"file_download\")}\r\n              onNo={() => handleYesNoResponse(false, \"file_download\")}\r\n              yesText=\"Download File\"\r\n              noText=\"Skip\"\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        {/* Yes/No Buttons for More Queries */}\r\n        {awaitingOtherQueries && (\r\n          <div style={{ padding: \"10px\", textAlign: \"center\" }}>\r\n            <YesNoButtons\r\n              onYes={() => handleYesNoResponse(true, \"more_queries\")}\r\n              onNo={() => handleYesNoResponse(false, \"more_queries\")}\r\n              yesText=\"Yes\"\r\n              noText=\"No\"\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        {/* Yes/No Buttons for Ticket Closure */}\r\n        {awaitingCloseConfirmation && (\r\n          <div style={{ padding: \"10px\", textAlign: \"center\" }}>\r\n            <YesNoButtons\r\n              onYes={() => handleYesNoResponse(true, \"close_ticket\")}\r\n              onNo={() => handleYesNoResponse(false, \"close_ticket\")}\r\n              yesText=\"Close Ticket\"\r\n              noText=\"Keep Open\"\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        <div ref={messagesEndRef} />\r\n      </div>\r\n\r\n      {error && (\r\n        <div\r\n          className=\"error-message\"\r\n          onClick={() => setError(\"\")}\r\n          style={{ cursor: \"pointer\" }}\r\n          role=\"alert\"\r\n          aria-live=\"assertive\"\r\n          tabIndex={0}\r\n        >\r\n          {error} (click to dismiss)\r\n        </div>\r\n      )}\r\n\r\n      <form className=\"chat-input-form\" onSubmit={handleSubmit} aria-label=\"Send message form\">\r\n        <div className=\"input-container\" style={{ display: \"flex\", alignItems: \"center\", width: \"100%\" }}>\r\n          <input\r\n            type=\"text\"\r\n            value={query}\r\n            onChange={onInputChange}\r\n            placeholder={\r\n              orgVerified\r\n                ? ticketStep > 0\r\n                  ? ticketQuestions[ticketStep - 1]\r\n                  : awaitingUnrelatedQueryResponse\r\n                  ? \"Answer 'yes' or 'no' to create a new ticket...\"\r\n                  : awaitingCloseConfirmation\r\n                  ? \"Answer 'yes' or 'no' to close the ticket...\"\r\n                  : \"Type your question here...\"\r\n                : \"Enter your organization name...\"\r\n            }\r\n            disabled={loading || verifying || (ticketStep === 1 && !query)}\r\n            autoFocus\r\n            aria-label=\"Chat input\"\r\n            style={{ flex: \"1\", width: \"100%\", padding: \"8px\", borderRadius: \"4px 0 0 4px\", border: \"1px solid #ccc\", margin: 0 }}\r\n            onKeyDown={(e) => {\r\n              if (e.key === \"Enter\" && !e.shiftKey) {\r\n                e.preventDefault();\r\n                handleSubmit(e);\r\n              }\r\n            }}\r\n          />\r\n          {ticketStep === 1 && (\r\n            <select\r\n              value={ticketData.productType}\r\n              onChange={handleProductTypeChange}\r\n              style={{\r\n                marginLeft: \"0\",\r\n                padding: \"6px\",\r\n                borderRadius: \"0\",\r\n                border: \"1px solid #ccc\",\r\n                borderLeft: \"none\",\r\n                fontSize: \"1em\",\r\n              }}\r\n              aria-label=\"Select product type\"\r\n            >\r\n              <option value=\"\" disabled>\r\n                Select a product type\r\n              </option>\r\n              {productTypeOptions.map((option) => (\r\n                <option key={option} value={option}>\r\n                  {option}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          )}\r\n          <button\r\n            type=\"submit\"\r\n            disabled={loading || !query.trim() || verifying || (ticketStep === 1 && !ticketData.productType)}\r\n            title={loading || verifying ? \"Please wait...\" : \"Send\"}\r\n            aria-label=\"Send message\"\r\n            style={{ padding: \"8px 12px\", borderRadius: \"0 4px 4px 0\", border: \"1px solid #ccc\", borderLeft: \"none\", backgroundColor: \"#4CAF50\", color: \"white\" }}\r\n          >\r\n            {loading || verifying ? <span className=\"spinner\" /> : \"📤\"}\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,YAAY,EAAEC,mBAAmB,EAAEC,kBAAkB,QAAQ,gBAAgB;AACtF,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG,uBAAuB;AAE3C,eAAe,SAASC,IAAIA,CAAC;EAAEC;AAAM,CAAC,EAAE;EAAAC,EAAA;EACtC,MAAMC,WAAW,GAAGF,KAAK,IAAIG,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;;EAE3D;EACA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmB,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,IAAI,EAAEC,OAAO,CAAC,GAAG1B,QAAQ,CAAC,QAAQ,CAAC;EAC1C,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACqC,yBAAyB,EAAEC,4BAA4B,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACjF,MAAM,CAACuC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2C,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC6C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC+C,YAAY,EAAEC,eAAe,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACmD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAACqD,8BAA8B,EAAEC,iCAAiC,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC3F,MAAMuD,2BAA2B,GAAG,EAAE;EACtC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0D,WAAW,EAAEC,cAAc,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC4D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7D,QAAQ,CAAC,CAAC,CAAC;;EAG3D;EACA,MAAM,CAAC8D,UAAU,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACgE,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC;IAC3CkE,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,0BAA0B,EAAEC,6BAA6B,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAAC2E,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EAEpE,MAAM6E,eAAe,GAAG,CACtB,0DAA0D,EAC1D,gDAAgD,EAChD,sCAAsC,EACtC,kCAAkC,EAClC,2BAA2B,EAC3B,mCAAmC,EACnC,sCAAsC,CACvC;EAED,MAAMC,kBAAkB,GAAG,CAAC,QAAQ,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,CAAC;;EAEjF;EACA7E,SAAS,CAAC,MAAM;IAAA,IAAA8E,qBAAA;IACd,CAAAA,qBAAA,GAAAC,cAAc,CAACC,OAAO,cAAAF,qBAAA,uBAAtBA,qBAAA,CAAwBG,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAAClE,QAAQ,EAAEI,OAAO,EAAEE,KAAK,CAAC,CAAC;EAE9B,MAAMyD,cAAc,GAAG9E,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACAD,SAAS,CAAC,MAAM;IACdmF,KAAK,CAAC,GAAG5E,WAAW,wBAAwB,CAAC,CAC1C6E,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEG,IAAI,IAAKpE,iBAAiB,CAACoE,IAAI,CAACC,QAAQ,CAAC,CAAC,CAChDC,KAAK,CAAEC,GAAG,IAAK;MACdC,OAAO,CAACrE,KAAK,CAAC,kCAAkC,EAAEoE,GAAG,CAAC;MACtDvE,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnB,SAAS,CAAC,MAAM;IACd,MAAM4F,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,MAAMC,aAAa,GAAGL,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC,KAAK,SAAS;IACzD,MAAMC,eAAe,GAAGP,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC,KAAK,KAAK;IACvD,MAAME,YAAY,GAAGR,SAAS,CAACM,GAAG,CAAC,QAAQ,CAAC;IAE5C,IAAID,aAAa,EAAE;MACjB;MACAI,yBAAyB,CAAC,CAAC;IAC7B,CAAC,MAAM,IAAIF,eAAe,IAAIC,YAAY,EAAE;MAC1C;MACAE,aAAa,CAACF,YAAY,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApG,SAAS,CAAC,MAAM;IACd,IAAI,CAACW,WAAW,EAAE;IAElB,MAAMiF,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,MAAMC,aAAa,GAAGL,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC,KAAK,SAAS;IAEzD,IAAID,aAAa,EAAE;MACjB,OAAO,CAAC;IACV;IAEAd,KAAK,CAAC,GAAG5E,WAAW,iBAAiB,EAAE;MACrCgG,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAE,UAAU7F,WAAW;MACtC;IACF,CAAC,CAAC,CACCyE,IAAI,CAAC,MAAOC,GAAG,IAAK;MACnB,IAAI,CAACA,GAAG,CAACoB,EAAE,EAAE;QACX,MAAMC,SAAS,GAAG,MAAMrB,GAAG,CAACsB,IAAI,CAAC,CAAC;QAClChB,OAAO,CAACrE,KAAK,CAAC,yBAAyB,EAAE+D,GAAG,CAACuB,MAAM,EAAEF,SAAS,CAAC;QAC/D,MAAM,IAAIG,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MACA,OAAOxB,GAAG,CAACC,IAAI,CAAC,CAAC;IACnB,CAAC,CAAC,CACDF,IAAI,CAAEG,IAAI,IAAK;MACd,MAAMuB,IAAI,GAAGvB,IAAI,CAACuB,IAAI,IAAIvB,IAAI,CAACvD,QAAQ,IAAIuD,IAAI,CAACwB,KAAK;MACrD,IAAI,CAACD,IAAI,EAAE,MAAM,IAAID,KAAK,CAAC,0BAA0B,CAAC;MAEtD5E,WAAW,CAAC6E,IAAI,CAAC;MACjB7F,WAAW,CAAC,CACV;QACE+F,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE,eAAeJ,IAAI,+DAA+D;QAC3FK,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;IACJ,CAAC,CAAC,CACD3B,KAAK,CAAEC,GAAG,IAAK;MACdC,OAAO,CAACrE,KAAK,CAAC,4BAA4B,EAAEoE,GAAG,CAAC2B,OAAO,CAAC;MACxDzG,YAAY,CAAC0G,UAAU,CAAC,QAAQ,CAAC;MACjCxB,MAAM,CAACC,QAAQ,CAACwB,IAAI,GAAG,OAAO;IAChC,CAAC,CAAC;EACN,CAAC,EAAE,CAAC5G,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAM2F,aAAa,GAAG,MAAOF,YAAY,IAAK;IAC5C,IAAI,CAACzF,WAAW,EAAE;IAElB,IAAI;MACF;MACA,MAAM6G,YAAY,GAAG,MAAMrC,KAAK,CAAC,GAAG5E,WAAW,iBAAiB,EAAE;QAChEgG,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAU7F,WAAW;QACtC;MACF,CAAC,CAAC;MAEF,IAAI,CAAC6G,YAAY,CAACf,EAAE,EAAE;QACpB,MAAM,IAAII,KAAK,CAAC,2BAA2B,CAAC;MAC9C;MAEA,MAAMY,QAAQ,GAAG,MAAMD,YAAY,CAAClC,IAAI,CAAC,CAAC;MAC1C,MAAMwB,IAAI,GAAGW,QAAQ,CAACX,IAAI,IAAIW,QAAQ,CAACzF,QAAQ,IAAIyF,QAAQ,CAACV,KAAK;MACjE9E,WAAW,CAAC6E,IAAI,CAAC;MACjBjF,cAAc,CAAC,IAAI,CAAC;MACpBkB,eAAe,CAACqD,YAAY,CAAC;;MAE7B;MACA,IAAI;QACF,MAAMsB,cAAc,GAAG,MAAMvC,KAAK,CAAC,GAAG5E,WAAW,eAAe6F,YAAY,GAAG,EAAE;UAC/EG,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClCC,aAAa,EAAE,UAAU7F,WAAW;UACtC;QACF,CAAC,CAAC;QAEF,IAAI+G,cAAc,CAACjB,EAAE,EAAE;UACrB,MAAM1C,UAAU,GAAG,MAAM2D,cAAc,CAACpC,IAAI,CAAC,CAAC;UAE9CrE,WAAW,CAAC,CACV;YACE+F,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,KAAK;YACXC,OAAO,EAAE,eAAeJ,IAAI,6BAA6BV,YAAY,iCAAiC;YACtGe,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,EACD;YACEJ,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,KAAK;YACXC,OAAO,EAAE,0BAA0Bd,YAAY,4BAA4BrC,UAAU,CAAC4D,MAAM,CAACC,WAAW,IAAI,UAAU,0CAA0C7D,UAAU,CAAC4D,MAAM,CAACE,mBAAmB,IAAI,gBAAgB,yCAAyC;YAClQV,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,CACF,CAAC;QACJ,CAAC,MAAM;UACL;UACAnG,WAAW,CAAC,CACV;YACE+F,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,KAAK;YACXC,OAAO,EAAE,eAAeJ,IAAI,6BAA6BV,YAAY,iCAAiC;YACtGe,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,EACD;YACEJ,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,KAAK;YACXC,OAAO,EAAE,0BAA0Bd,YAAY,yCAAyC;YACxFe,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,CACF,CAAC;QACJ;MACF,CAAC,CAAC,OAAO1B,GAAG,EAAE;QACZC,OAAO,CAACrE,KAAK,CAAC,iCAAiC,EAAEoE,GAAG,CAAC;QACrD;QACAzE,WAAW,CAAC,CACV;UACE+F,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,eAAeJ,IAAI,6BAA6BV,YAAY,iCAAiC;UACtGe,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,EACD;UACEJ,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,0BAA0Bd,YAAY,yCAAyC;UACxFe,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;MACJ;IACF,CAAC,CAAC,OAAO1B,GAAG,EAAE;MACZC,OAAO,CAACrE,KAAK,CAAC,4BAA4B,EAAEoE,GAAG,CAAC;MAChDnE,QAAQ,CAAC,0CAA0C,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAM8E,yBAAyB,GAAG,MAAAA,CAAA,KAAY;IAC5C,IAAI,CAAC1F,WAAW,EAAE;IAElB,IAAI;MACF;MACA,MAAM6G,YAAY,GAAG,MAAMrC,KAAK,CAAC,GAAG5E,WAAW,iBAAiB,EAAE;QAChEgG,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAU7F,WAAW;QACtC;MACF,CAAC,CAAC;MAEF,IAAI,CAAC6G,YAAY,CAACf,EAAE,EAAE;QACpB,MAAM,IAAII,KAAK,CAAC,2BAA2B,CAAC;MAC9C;MAEA,MAAMY,QAAQ,GAAG,MAAMD,YAAY,CAAClC,IAAI,CAAC,CAAC;MAC1C,MAAMwB,IAAI,GAAGW,QAAQ,CAACX,IAAI,IAAIW,QAAQ,CAACzF,QAAQ,IAAIyF,QAAQ,CAACV,KAAK;MACjE9E,WAAW,CAAC6E,IAAI,CAAC;MACjBjF,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,MAAMiG,eAAe,GAAG,MAAM3C,KAAK,CAAC,GAAG5E,WAAW,uBAAuB,EAAE;QACzEgG,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAU7F,WAAW;QACtC;MACF,CAAC,CAAC;MAEF,IAAI,CAACmH,eAAe,CAACrB,EAAE,EAAE;QACvB,MAAM,IAAII,KAAK,CAAC,iCAAiC,CAAC;MACpD;MAEA,MAAMkB,WAAW,GAAG,MAAMD,eAAe,CAACxC,IAAI,CAAC,CAAC;MAEhD,IAAIyC,WAAW,CAACxE,OAAO,IAAIwE,WAAW,CAACxE,OAAO,CAACyE,MAAM,GAAG,CAAC,EAAE;QACzD,MAAMC,YAAY,GAAGF,WAAW,CAACxE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7CR,eAAe,CAACkF,YAAY,CAACC,aAAa,CAAC;QAE3CjH,WAAW,CAAC,CACV;UACE+F,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,oBAAoBJ,IAAI,kCAAkCmB,YAAY,CAACC,aAAa,EAAE;UAC/Ff,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,EACD;UACEJ,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,sCAAsCe,YAAY,CAACE,KAAK,IAAIF,YAAY,CAACL,WAAW,IAAI,UAAU,kBAAkBK,YAAY,CAACJ,mBAAmB,IAAI,0BAA0B,0CAA0C;UACrOV,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;MACJ,CAAC,MAAM;QACLnG,WAAW,CAAC,CACV;UACE+F,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,eAAeJ,IAAI,oEAAoE;UAChGK,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;MACJ;IACF,CAAC,CAAC,OAAO1B,GAAG,EAAE;MACZC,OAAO,CAACrE,KAAK,CAAC,gCAAgC,EAAEoE,GAAG,CAAC;MACpDnE,QAAQ,CAAC,kDAAkD,CAAC;IAC9D;EACF,CAAC;;EAED;EACA,MAAM6G,kBAAkB,GAAIC,IAAI,IAAK;IACnC;IACA,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACf,IAAI,GAAGc,IAAI,CAACI,GAAG;IACpBH,IAAI,CAACI,QAAQ,GAAGL,IAAI,CAACM,WAAW;IAChCJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;IAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;IACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;;IAE/B;IACArH,WAAW,CAAC+H,IAAI,IAAIA,IAAI,CAACC,MAAM,CAACC,GAAG,IAAI,CAACA,GAAG,CAACC,KAAK,IAAID,GAAG,CAACC,KAAK,CAACnB,MAAM,KAAK,CAAC,CAAC,CAAC;IAC7ErG,eAAe,CAAC,IAAI,CAAC;;IAErB;IACAyH,MAAM,CAAC,mFAAmF,CAAC;IAC3F7G,uBAAuB,CAAC,IAAI,CAAC;EAC/B,CAAC;;EAED;EACA,MAAM8G,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACvG,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMwG,QAAQ,GAAG,MAAMnE,KAAK,CAAC,GAAG5E,WAAW,4BAA4B,EAAE;QACvEgJ,MAAM,EAAE,MAAM;QACdhD,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAU7F,WAAW;QACtC,CAAC;QACDiI,IAAI,EAAEY,IAAI,CAACC,SAAS,CAAC;UACnBvB,aAAa,EAAEpF,YAAY;UAC3B8D,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAI0C,QAAQ,CAAC7C,EAAE,EAAE;QACf2C,MAAM,CAAC,YAAYtG,YAAY,wEAAwE,CAAC;;QAExG;QACA4G,UAAU,CAAC,MAAM;UACfC,YAAY,CAAC,CAAC;QAChB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLP,MAAM,CAAC,iDAAiD,CAAC;MAC3D;IACF,CAAC,CAAC,OAAO1D,GAAG,EAAE;MACZC,OAAO,CAACrE,KAAK,CAAC,uBAAuB,EAAEoE,GAAG,CAAC;MAC3C0D,MAAM,CAAC,8DAA8D,CAAC;IACxE;EACF,CAAC;;EAED;EACA,MAAMQ,mBAAmB,GAAGA,CAACN,QAAQ,EAAEO,OAAO,KAAK;IACjD,MAAMC,OAAO,GAAG;MACd9C,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC;MACd9C,IAAI,EAAE,MAAM;MACZC,OAAO,EAAEoC,QAAQ,GAAG,KAAK,GAAG,IAAI;MAChCnC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IACDnG,WAAW,CAAE+H,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEc,OAAO,CAAC,CAAC;IAEzC,IAAID,OAAO,KAAK,eAAe,EAAE;MAC/B,IAAIP,QAAQ,IAAI5H,YAAY,IAAIA,YAAY,CAACsG,MAAM,GAAG,CAAC,EAAE;QACvDI,kBAAkB,CAAC1G,YAAY,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,MAAM;QACLC,eAAe,CAAC,IAAI,CAAC;QACrByH,MAAM,CAAC,mDAAmD,CAAC;QAC3D7G,uBAAuB,CAAC,IAAI,CAAC;MAC/B;IACF,CAAC,MAAM,IAAIsH,OAAO,KAAK,cAAc,EAAE;MACrCtH,uBAAuB,CAAC,KAAK,CAAC;MAC9B,IAAI+G,QAAQ,EAAE;QACZF,MAAM,CAAC,2BAA2B,CAAC;MACrC,CAAC,MAAM;QACLA,MAAM,CAAC,mCAAmC,CAAC;QAC3C/G,4BAA4B,CAAC,IAAI,CAAC;MACpC;IACF,CAAC,MAAM,IAAIwH,OAAO,KAAK,cAAc,EAAE;MACrCxH,4BAA4B,CAAC,KAAK,CAAC;MACnC,IAAIiH,QAAQ,EAAE;QACZD,iBAAiB,CAAC,CAAC;MACrB,CAAC,MAAM;QACLD,MAAM,CAAC,+CAA+C,CAAC;MACzD;IACF;EACF,CAAC;;EAED;EACA,MAAMY,aAAa,GAAIC,CAAC,IAAK;IAC3B,IAAI3I,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;IACvBR,QAAQ,CAACkJ,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAIH,CAAC,IAAK;IACrC,MAAMI,YAAY,GAAGJ,CAAC,CAACC,MAAM,CAACC,KAAK;IACnCnG,aAAa,CAAC;MAAE,GAAGD,UAAU;MAAEE,WAAW,EAAEoG;IAAa,CAAC,CAAC;IAC3DpJ,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;MACEhC,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC;MACd9C,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,0BAA0BmD,YAAY,EAAE;MACjDlD,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC,CACF,CAAC;IACFtD,aAAa,CAACD,UAAU,GAAG,CAAC,CAAC;IAC7BuF,MAAM,CAACxE,eAAe,CAACf,UAAU,CAAC,CAAC;EACrC,CAAC;;EAED;EACA,MAAMyG,kBAAkB,GAAG,MAAOC,OAAO,IAAK;IAC5CxI,YAAY,CAAC,IAAI,CAAC;IAClBR,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAM+H,QAAQ,GAAG,MAAMnE,KAAK,CAAC,GAAG5E,WAAW,2BAA2B,EAAE;QACtEgJ,MAAM,EAAE,MAAM;QACdhD,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAU7F,WAAW;QACtC,CAAC;QACDiI,IAAI,EAAEY,IAAI,CAACC,SAAS,CAAC;UAAEe,YAAY,EAAED;QAAQ,CAAC;MAChD,CAAC,CAAC;MAEF,MAAMhF,IAAI,GAAG,MAAM+D,QAAQ,CAAChE,IAAI,CAAC,CAAC;MAElC,IAAIgE,QAAQ,CAAC7C,EAAE,IAAIlB,IAAI,CAACqB,MAAM,KAAK,UAAU,EAAE;QAC7C/E,cAAc,CAAC,IAAI,CAAC;QACpBI,WAAW,CAACsD,IAAI,CAACuB,IAAI,IAAI9E,QAAQ,CAAC;QAElCf,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACEhC,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC;UACd9C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE3B,IAAI,CAAC8B,OAAO,IAAI,0BAA0B;UACnDF,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;QAEF,MAAMqD,mBAAmB,CAAC,CAAC;MAC7B,CAAC,MAAM;QACL,MAAMC,OAAO,GAAG5E,MAAM,CAACC,QAAQ,CAAC4E,MAAM;QACtC,MAAMC,UAAU,GAAG,kBAAkBF,OAAO,WAAW;QAEvDnJ,QAAQ,CAACgE,IAAI,CAAC8B,OAAO,IAAI,0BAA0B,CAAC;QACpDpG,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACEhC,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC;UACd9C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,sFAAsF0D,UAAU,qBAAqB;UAC9HzD,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;QAEFxG,YAAY,CAAC0G,UAAU,CAAC,QAAQ,CAAC;MACnC;IACF,CAAC,CAAC,OAAO5B,GAAG,EAAE;MACZnE,QAAQ,CAAC,oCAAoC,CAAC;MAC9CN,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QACEhC,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC;QACd9C,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE,qEAAqE;QAC9EC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;MACFzB,OAAO,CAACrE,KAAK,CAAC,qBAAqB,EAAEoE,GAAG,CAAC;IAC3C,CAAC,SAAS;MACR3D,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,eAAe0I,mBAAmBA,CAAA,EAAG;IACnC,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMnE,KAAK,CAAC,GAAG5E,WAAW,uBAAuB,EAAE;QAClEgG,OAAO,EAAE;UACPC,aAAa,EAAE,UAAU7F,WAAW;QACtC;MACF,CAAC,CAAC;MACF,MAAM4E,IAAI,GAAG,MAAM+D,QAAQ,CAAChE,IAAI,CAAC,CAAC;MAElC,IAAIgE,QAAQ,CAAC7C,EAAE,EAAE;QACfhE,iBAAiB,CAAC8C,IAAI,CAAChC,OAAO,IAAI,EAAE,CAAC;QACrC,IAAIgC,IAAI,CAAChC,OAAO,IAAIgC,IAAI,CAAChC,OAAO,CAACyE,MAAM,GAAG,CAAC,EAAE;UAC3CrF,wBAAwB,CAAC,IAAI,CAAC;UAC9ByG,MAAM,CACJ,YAAY7D,IAAI,CAAChC,OAAO,CAACyE,MAAM,wEACjC,CAAC;QACH,CAAC,MAAM;UACL7F,iBAAiB,CAAC,IAAI,CAAC;UACvBiH,MAAM,CAAC,8EAA8E,CAAC;QACxF;MACF,CAAC,MAAM;QACL3G,iBAAiB,CAAC,EAAE,CAAC;QACrBN,iBAAiB,CAAC,IAAI,CAAC;QACvBiH,MAAM,CAAC,uFAAuF,CAAC;MACjG;IACF,CAAC,CAAC,OAAO1D,GAAG,EAAE;MACZC,OAAO,CAACrE,KAAK,CAAC,iCAAiC,EAAEoE,GAAG,CAAC;MACrDjD,iBAAiB,CAAC,EAAE,CAAC;MACrBN,iBAAiB,CAAC,IAAI,CAAC;MACvBiH,MAAM,CAAC,oFAAoF,CAAC;IAC9F;EACF;;EAEA;EACA,MAAMyB,YAAY,GAAG,MAAOC,eAAe,IAAK;IAC9CzJ,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,MAAMwJ,iBAAiB,GAAG,CAAC,QAAQ,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,CAAC;IAChF,MAAMC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACJ,eAAe,CAAC,CAACK,KAAK,CACnDC,CAAC,IAAKA,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,KAAK,EAC3B,CAAC;IACD,IAAI,CAACL,SAAS,EAAE;MACd/J,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QACEhC,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC;QACd9C,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE,oEAAoE;QAC7EC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;MACF/F,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IACA,IAAI,CAAC0J,iBAAiB,CAACO,QAAQ,CAACR,eAAe,CAAC7G,WAAW,CAAC,EAAE;MAC5DhD,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QACEhC,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC;QACd9C,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE,yFAAyF;QAClGC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;MACF/F,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAI;MACF,MAAMiI,QAAQ,GAAG,MAAMnE,KAAK,CAAC,GAAG5E,WAAW,qBAAqB,EAAE;QAChEgJ,MAAM,EAAE,MAAM;QACdhD,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAU7F,WAAW;QACtC,CAAC;QACDiI,IAAI,EAAEY,IAAI,CAACC,SAAS,CAAC;UACnB8B,YAAY,EAAET,eAAe,CAAC7G,WAAW;UACzCuH,cAAc,EAAEV,eAAe,CAAC5G,aAAa;UAC7CuH,gBAAgB,EAAEX,eAAe,CAAC3G,cAAc;UAChDuH,YAAY,EAAEZ,eAAe,CAAC1G,WAAW;UACzCC,KAAK,EAAEyG,eAAe,CAACzG,KAAK;UAC5BsH,SAAS,EAAEb,eAAe,CAACxG,QAAQ;UACnCsH,gBAAgB,EAAEd,eAAe,CAACvG;QACpC,CAAC;MACH,CAAC,CAAC;MAEF,MAAMgB,IAAI,GAAG,MAAM+D,QAAQ,CAAChE,IAAI,CAAC,CAAC;MAElC,IAAIgE,QAAQ,CAAC7C,EAAE,EAAE;QACf9B,sBAAsB,CAACY,IAAI,CAAC2C,aAAa,CAAC;QAC1CnF,eAAe,CAACwC,IAAI,CAAC2C,aAAa,CAAC;QACnCzD,6BAA6B,CAAC,IAAI,CAAC;QACnCxD,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACEhC,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC;UACd9C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,4FAA4F3B,IAAI,CAAC2C,aAAa,2DAA2D;UAClLf,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;MACJ,CAAC,MAAM;QACLnG,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACEhC,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC;UACd9C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,8BAA8BsC,IAAI,CAACC,SAAS,CAAClE,IAAI,CAACsG,MAAM,IAAItG,IAAI,CAAC8B,OAAO,CAAC,EAAE;UACpFF,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;MACJ;IACF,CAAC,CAAC,OAAO1B,GAAG,EAAE;MACZzE,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QACEhC,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC;QACd9C,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE,0CAA0CxB,GAAG,CAAC2B,OAAO,EAAE;QAChEF,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;IACJ,CAAC,SAAS;MACR/F,UAAU,CAAC,KAAK,CAAC;MACjByC,aAAa,CAAC,CAAC,CAAC;MAChB3B,iBAAiB,CAAC,KAAK,CAAC;MACxB6B,aAAa,CAAC;QACZC,WAAW,EAAE,EAAE;QACfC,aAAa,EAAE,EAAE;QACjBC,cAAc,EAAE,EAAE;QAClBC,WAAW,EAAE,EAAE;QACfC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,eAAe,EAAE;MACnB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,SAAS6E,MAAMA,CAACzC,IAAI,EAAE;IACpB1F,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;MAAEhC,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC;MAAE9C,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAEP,IAAI;MAAEQ,SAAS,EAAE,IAAIC,IAAI,CAAC;IAAE,CAAC,CACtE,CAAC;EACJ;;EAEA;EACA,MAAM0E,kBAAkB,GAAG,MAAO1F,YAAY,IAAK;IACjDvD,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,eAAe,CAACqD,YAAY,CAAC;IAC7BzB,sBAAsB,CAACyB,YAAY,CAAC;IACpC1C,cAAc,CAAC,KAAK,CAAC;IAErB,IAAI;MACF,MAAMqI,UAAU,GAAG,MAAM5G,KAAK,CAAC,GAAG5E,WAAW,sBAAsB,EAAE;QACnEgJ,MAAM,EAAE,MAAM;QACdhD,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAU7F,WAAW;QACtC,CAAC;QACDiI,IAAI,EAAEY,IAAI,CAACC,SAAS,CAAC;UAAEvB,aAAa,EAAE9B;QAAa,CAAC;MACtD,CAAC,CAAC;MAEF,MAAM4F,WAAW,GAAG,MAAMD,UAAU,CAACzG,IAAI,CAAC,CAAC;MAE3C,IAAIyG,UAAU,CAACtF,EAAE,EAAE;QACjB2C,MAAM,CACJ,wBAAwBhD,YAAY,UAAU,GAC5C,0BAA0B4F,WAAW,CAACC,eAAe,IAAID,WAAW,CAACnE,mBAAmB,MAAM,GAC9F,0BAA0BmE,WAAW,CAACE,gBAAgB,IAAI,kBAAkB,MAAM,GAClF,yCACJ,CAAC;MACH,CAAC,MAAM;QACL9C,MAAM,CAAC,mCAAmC,CAAC;MAC7C;IACF,CAAC,CAAC,OAAO1D,GAAG,EAAE;MACZ0D,MAAM,CAAC,gDAAgD,CAAC;IAC1D;EACF,CAAC;;EAED;EACF,eAAe+C,YAAYA,CAAClC,CAAC,EAAE;IAC7BA,CAAC,CAACmC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACtL,KAAK,CAACuK,IAAI,CAAC,CAAC,IAAIjK,OAAO,IAAIU,SAAS,EAAE;IAE3C,MAAMuK,YAAY,GAAGvL,KAAK,CAACuK,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;;IAE/C;IACA,IACExJ,YAAY;IAAe;IAC3Be,UAAU,KAAK,CAAC;IAAW;IAC3B,CAACW,0BAA0B;IAAI;IAC/Bb,gBAAgB,IAAI,CAAC,CAAM;IAAA,EAC3B;MACAyF,MAAM,CACJ,sNACF,CAAC;MACDrI,QAAQ,CAAC,EAAE,CAAC;MACZ;IACF;IACA;;IAEA,IAAIiC,aAAa,EAAE;MACjB,IAAIE,oBAAoB,IAAII,2BAA2B,EAAE;QACvD8F,MAAM,CACJ,+GACF,CAAC;QACDrI,QAAQ,CAAC,EAAE,CAAC;QACZ;MACF,CAAC,MAAM;QACLoC,uBAAuB,CAAEoJ,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;MACvC;IACF;IAEA,MAAMzC,OAAO,GAAG;MACd9C,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC;MACd9C,IAAI,EAAE,MAAM;MACZC,OAAO,EAAEpG,KAAK,CAACuK,IAAI,CAAC,CAAC;MACrBlE,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IACDnG,WAAW,CAAE+H,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEc,OAAO,CAAC,CAAC;IACzC/I,QAAQ,CAAC,EAAE,CAAC;IACZQ,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAImB,qBAAqB,EAAE;MACzBC,wBAAwB,CAAC,KAAK,CAAC;MAE/B,IAAI0J,YAAY,KAAK,KAAK,EAAE;QAC1BxJ,uBAAuB,CAAC,IAAI,CAAC;QAC7B5B,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACEhC,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC;UACd9C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,uCAAuC;UAChDC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;UACrB7D,OAAO,EAAEf,cAAc,CAACgK,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,MAAM;YACrCC,KAAK,EAAED,CAAC,GAAG,CAAC;YACZtG,YAAY,EAAEqG,CAAC,CAACvE,aAAa;YAC7BC,KAAK,EAAEsE,CAAC,CAACtE,KAAK,IAAIsE,CAAC,CAAC7E,WAAW,IAAI;UACrC,CAAC,CAAC;QACJ,CAAC,CACF,CAAC;MACJ,CAAC,MAAM,IAAIyE,YAAY,KAAK,IAAI,EAAE;QAChClK,iBAAiB,CAAC,IAAI,CAAC;QACvBiH,MAAM,CAAC,iDAAiD,CAAC;MAC3D,CAAC,MAAM;QACLA,MAAM,CAAC,sEAAsE,CAAC;QAC9EzG,wBAAwB,CAAC,IAAI,CAAC;MAChC;MACA;IACF;IAEA,IAAIC,oBAAoB,EAAE;MACxB,MAAMgK,MAAM,GAAGpK,cAAc,CAACqK,IAAI,CAChC,CAACJ,CAAC,EAAEK,GAAG,KACLT,YAAY,KAAKU,MAAM,CAACD,GAAG,GAAG,CAAC,CAAC,IAChCT,YAAY,CAACf,QAAQ,CAACmB,CAAC,CAACvE,aAAa,CAACoE,WAAW,CAAC,CAAC,CACvD,CAAC;MAED,IAAI,CAACM,MAAM,EAAE;QACXxD,MAAM,CAAC,gDAAgD,CAAC;QACxD;MACF;MAEA,MAAM0C,kBAAkB,CAACc,MAAM,CAAC1E,aAAa,CAAC;MAC9C;IACF;IAEA,IAAI,CAACtG,WAAW,EAAE;MAChB,MAAM0I,kBAAkB,CAAC+B,YAAY,CAAC;MACtC;IACF;IAEA,IAAInK,cAAc,EAAE;MAClB,IAAImK,YAAY,KAAK,KAAK,EAAE;QAC1BlK,iBAAiB,CAAC,KAAK,CAAC;QACxB2B,aAAa,CAAC,CAAC,CAAC;QAChBsF,MAAM,CAACxE,eAAe,CAAC,CAAC,CAAC,CAAC;QAC1B3B,gBAAgB,CAAC,KAAK,CAAC;QACvBE,uBAAuB,CAAC,CAAC,CAAC;;QAE1B;QACAS,mBAAmB,CAAC,CAAC,CAAC;QACtB;MAEF,CAAC,MAAM,IAAIyI,YAAY,KAAK,IAAI,EAAE;QAChClK,iBAAiB,CAAC,KAAK,CAAC;QACxBiH,MAAM,CAAC,6DAA6D,CAAC;QACrEnG,gBAAgB,CAAC,IAAI,CAAC;QACtBE,uBAAuB,CAAC,CAAC,CAAC;MAC5B,CAAC,MAAM;QACLiG,MAAM,CAAC,qEAAqE,CAAC;MAC/E;MACA;IACF;IAEA,IAAIhG,8BAA8B,EAAE;MAClCC,iCAAiC,CAAC,KAAK,CAAC;MACxC,IAAIgJ,YAAY,KAAK,KAAK,EAAE;QAC1BvI,aAAa,CAAC,CAAC,CAAC;QAChBf,eAAe,CAAC,IAAI,CAAC;QACrB4B,sBAAsB,CAAC,IAAI,CAAC;QAC5ByE,MAAM,CAACxE,eAAe,CAAC,CAAC,CAAC,CAAC;;QAE1B;QACAhB,mBAAmB,CAAC,CAAC,CAAC;QACtB;MAEF,CAAC,MAAM,IAAIyI,YAAY,KAAK,IAAI,EAAE;QAChChK,4BAA4B,CAAC,IAAI,CAAC;QAClC+G,MAAM,CAAC,uCAAuC,CAAC;MACjD,CAAC,MAAM;QACL/F,iCAAiC,CAAC,IAAI,CAAC;QACvC+F,MAAM,CAAC,kEAAkE,CAAC;MAC5E;MACA;IACF;IAEA,IAAIvF,UAAU,GAAG,CAAC,IAAIA,UAAU,IAAIe,eAAe,CAACoD,MAAM,EAAE;MAC1D,IAAInE,UAAU,KAAK,CAAC,EAAE;QACpB,MAAMwG,YAAY,GAAGvJ,KAAK,CAACuK,IAAI,CAAC,CAAC;QACjC,IAAIxG,kBAAkB,CAACyG,QAAQ,CAACjB,YAAY,CAAC,EAAE;UAC7CrG,aAAa,CAAC;YAAE,GAAGD,UAAU;YAAEE,WAAW,EAAEoG;UAAa,CAAC,CAAC;UAC3DpJ,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;YACEhC,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC;YACd9C,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE,0BAA0BmD,YAAY,EAAE;YACjDlD,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,CACF,CAAC;UACFtD,aAAa,CAACD,UAAU,GAAG,CAAC,CAAC;UAC7BuF,MAAM,CAACxE,eAAe,CAACf,UAAU,CAAC,CAAC;QACrC,CAAC,MAAM;UACLuF,MAAM,CACJ,2FACF,CAAC;QACH;QACA;MACF;MACA,MAAM4D,IAAI,GAAG,CACX,eAAe,EACf,gBAAgB,EAChB,aAAa,EACb,OAAO,EACP,UAAU,EACV,iBAAiB,CAClB;MACD,MAAMC,YAAY,GAAGD,IAAI,CAACnJ,UAAU,GAAG,CAAC,CAAC;MACzC,MAAMqJ,iBAAiB,GAAG;QAAE,GAAGnJ,UAAU;QAAE,CAACkJ,YAAY,GAAGnM,KAAK,CAACuK,IAAI,CAAC;MAAE,CAAC;MACzErH,aAAa,CAACkJ,iBAAiB,CAAC;MAEhC,IAAIrJ,UAAU,GAAGe,eAAe,CAACoD,MAAM,EAAE;QACvClE,aAAa,CAACD,UAAU,GAAG,CAAC,CAAC;QAC7BuF,MAAM,CAACxE,eAAe,CAACf,UAAU,CAAC,CAAC;MACrC,CAAC,MAAM;QACL,MAAMgH,YAAY,CAACqC,iBAAiB,CAAC;MACvC;MACA;IACF;IAEA,IAAI1I,0BAA0B,IAAIE,mBAAmB,EAAE;MACrDD,6BAA6B,CAAC,KAAK,CAAC;MACpCpD,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAI;QAAA,IAAA8L,eAAA;QACF,MAAMC,OAAO,GAAG,MAAMjI,KAAK,CAAC,GAAG5E,WAAW,+BAA+B,EAAE;UACzEgJ,MAAM,EAAE,MAAM;UACdhD,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClCC,aAAa,EAAE,UAAU7F,WAAW;UACtC,CAAC;UACDiI,IAAI,EAAEY,IAAI,CAACC,SAAS,CAAC;YACnBvB,aAAa,EAAExD,mBAAmB;YAClCmD,mBAAmB,EAAEiC,OAAO,CAAC5C;UAC/B,CAAC;QACH,CAAC,CAAC;QAEF,MAAMmG,QAAQ,GAAG,MAAMD,OAAO,CAAC9H,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC8H,OAAO,CAAC3G,EAAE,EAAE;UACf,MAAM,IAAII,KAAK,CAACwG,QAAQ,CAAC/L,KAAK,IAAI,qCAAqC,CAAC;QAC1E;QAEA,IAAIgM,UAAU,GAAGD,QAAQ,CAACE,MAAM,IAAI,sCAAsC;QAE1E,MAAMC,QAAQ,IAAAL,eAAA,GAAGE,QAAQ,CAAClE,KAAK,cAAAgE,eAAA,cAAAA,eAAA,GAAI,EAAE,CAAC,CAAC;QACvC,MAAMM,UAAU,GAAGD,QAAQ,CAACvE,MAAM,CAAEyE,CAAC,IAAK;UACxC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;YACzB,OAAOA,CAAC,CAACrC,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAACqC,CAAC,CAACpB,WAAW,CAAC,CAAC,CAACqB,UAAU,CAAC,MAAM,CAAC;UAC/D;UACA,IAAI,OAAOD,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,CAACE,QAAQ,EAAE;YACrD,OACEF,CAAC,CAACE,QAAQ,CAACvC,IAAI,CAAC,CAAC,KAAK,EAAE,IACxB,CAACqC,CAAC,CAACE,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAACqB,UAAU,CAAC,MAAM,CAAC;UAEhD;UACA,OAAO,KAAK;QACd,CAAC,CAAC;QAEF,IAAIF,UAAU,CAACzF,MAAM,GAAG,CAAC,EAAE;UACzBrG,eAAe,CACb8L,UAAU,CAACjB,GAAG,CAAEkB,CAAC,IAAK;YACpB,MAAME,QAAQ,GAAG,OAAOF,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGA,CAAC,CAACE,QAAQ;YACvD,MAAMnF,GAAG,GACP,OAAOiF,CAAC,KAAK,QAAQ,IAAI,CAACA,CAAC,CAACjF,GAAG,IAAI,CAACiF,CAAC,CAACjF,GAAG,CAACkF,UAAU,CAAC,MAAM,CAAC,GACxD,GAAGpN,WAAW,cAAcsN,kBAAkB,CAC5CD,QACF,CAAC,UAAUjN,WAAW,EAAE,GACxB,GAAG+M,CAAC,CAACjF,GAAG,GAAGiF,CAAC,CAACjF,GAAG,CAAC6C,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,SAAS3K,WAAW,EAAE;YACtE,OAAO;cAAEgI,WAAW,EAAEiF,QAAQ;cAAEnF;YAAI,CAAC;UACvC,CAAC,CACH,CAAC;UACD6E,UAAU,IAAI,qEAAqE;QACrF,CAAC,MAAM;UACL3L,eAAe,CAAC,IAAI,CAAC;QACvB;QAEAV,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACEhC,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC;UACd9C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAEoG,UAAU;UACnBnG,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;QAEF7E,uBAAuB,CAAC,IAAI,CAAC;MAC/B,CAAC,CAAC,OAAOmD,GAAG,EAAE;QACZzE,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACEhC,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC;UACd9C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,2CAA2CxB,GAAG,CAAC2B,OAAO,EAAE;UACjEF,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;MACJ,CAAC,SAAS;QACR/F,UAAU,CAAC,KAAK,CAAC;MACnB;MACA;IACF;IAEA,IAAIK,YAAY,KAAK2K,YAAY,KAAK,KAAK,IAAIA,YAAY,KAAK,IAAI,CAAC,EAAE;MACrE,MAAMyB,YAAY,GAAG,EAAE;MAEvB,IAAIzB,YAAY,KAAK,KAAK,EAAE;QAC1B,MAAM0B,aAAa,GAAGrM,YAAY,CAC/B8K,GAAG,CAAC,CAACkB,CAAC,EAAEZ,GAAG,KAAK,GAAGA,GAAG,GAAG,CAAC,MAAMY,CAAC,CAAC/E,WAAW,KAAK+E,CAAC,CAACjF,GAAG,GAAG,CAAC,CAC3DuF,IAAI,CAAC,IAAI,CAAC;QAEbF,YAAY,CAACG,IAAI,CAAC;UAChBjH,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC,GAAG,CAAC;UAClB9C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,qCAAqC6G,aAAa,EAAE;UAC7D5G,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL0G,YAAY,CAACG,IAAI,CAAC;UAChBjH,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC,GAAG,CAAC;UAClB9C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,iCAAiC;UAC1CC,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CAAC;MACJ;MAEA,IAAItE,YAAY,KAAK,IAAI,EAAE;QACzBgL,YAAY,CAACG,IAAI,CAAC;UAChBjH,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC,GAAG,CAAC;UAClB9C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,yCAAyC;UAClDC,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CAAC;QACF7E,uBAAuB,CAAC,IAAI,CAAC;MAC/B;MAEAtB,WAAW,CAAE+H,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAE,GAAG8E,YAAY,CAAC,CAAC;MACjDnM,eAAe,CAAC,IAAI,CAAC;MACrB;IACF;IAEA,IAAIW,oBAAoB,EAAE;MACxB,IAAI+J,YAAY,KAAK,IAAI,EAAE;QACzB9J,uBAAuB,CAAC,KAAK,CAAC;QAC9BF,4BAA4B,CAAC,IAAI,CAAC;QAClC+G,MAAM,CAAC,uCAAuC,CAAC;MACjD,CAAC,MAAM,IAAIiD,YAAY,KAAK,KAAK,EAAE;QACjC9J,uBAAuB,CAAC,KAAK,CAAC;QAC9B6G,MAAM,CAAC,wCAAwC,CAAC;MAClD,CAAC,MAAM;QACLA,MAAM,CAAC,6DAA6D,CAAC;MACvE;MACA;IACF;IAEA,IAAIhH,yBAAyB,EAAE;MAC7B,IAAIiK,YAAY,KAAK,KAAK,EAAE;QAC1BhK,4BAA4B,CAAC,KAAK,CAAC;QACnChB,UAAU,CAAC,IAAI,CAAC;QAChB,IAAI;UACF,MAAMiI,QAAQ,GAAG,MAAMnE,KAAK,CAAC,GAAG5E,WAAW,4BAA4B,EAAE;YACvEgJ,MAAM,EAAE,MAAM;YACdhD,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClCC,aAAa,EAAE,UAAU7F,WAAW;YACtC,CAAC;YACDiI,IAAI,EAAEY,IAAI,CAACC,SAAS,CAAC;cACnBvB,aAAa,EAAExD,mBAAmB;cAClCkC,MAAM,EAAE;YACV,CAAC;UACH,CAAC,CAAC;UACF,MAAMrB,IAAI,GAAG,MAAM+D,QAAQ,CAAChE,IAAI,CAAC,CAAC;UAElC,IAAIgE,QAAQ,CAAC7C,EAAE,EAAE;YACf1D,eAAe,CAAC,IAAI,CAAC;YACrB4B,sBAAsB,CAAC,IAAI,CAAC;YAC5BX,aAAa,CAAC;cACZC,WAAW,EAAE,EAAE;cACfC,aAAa,EAAE,EAAE;cACjBC,cAAc,EAAE,EAAE;cAClBC,WAAW,EAAE,EAAE;cACfC,KAAK,EAAE,EAAE;cACTC,QAAQ,EAAE,EAAE;cACZC,eAAe,EAAE;YACnB,CAAC,CAAC;YAEF6E,MAAM,CAAC,YAAY1E,mBAAmB,8BAA8B,CAAC;;YAErE;YACAd,mBAAmB,CAAC,CAAC,CAAC;YACtB;UAEF,CAAC,MAAM;YACL,MAAM,IAAIiD,KAAK,CAACtB,IAAI,CAACjE,KAAK,IAAI,yBAAyB,CAAC;UAC1D;QACF,CAAC,CAAC,OAAOoE,GAAG,EAAE;UACZ0D,MAAM,CAAC,2BAA2B1D,GAAG,CAAC2B,OAAO,EAAE,CAAC;QAClD,CAAC,SAAS;UACRhG,UAAU,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,MAAM,IAAIgL,YAAY,KAAK,IAAI,EAAE;QAChChK,4BAA4B,CAAC,KAAK,CAAC;QACnC+G,MAAM,CAAC,gCAAgC,CAAC;MAC1C,CAAC,MAAM;QACLA,MAAM,CAAC,2DAA2D,CAAC;MACrE;MACA;IACF;IAEA/H,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM6M,WAAW,GAAGlN,QAAQ,CACzBiI,MAAM,CAAEkF,CAAC,IAAKA,CAAC,CAAClH,IAAI,KAAK,MAAM,IAAIkH,CAAC,CAAClH,IAAI,KAAK,KAAK,CAAC,CACpDuF,GAAG,CAAE2B,CAAC,IAAK,GAAGA,CAAC,CAAClH,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,KAAK,KAAKkH,CAAC,CAACjH,OAAO,EAAE,CAAC,CACjE8G,IAAI,CAAC,IAAI,CAAC;MAEb,MAAMI,WAAW,GAAGlN,cAAc,GAC9BA,cAAc,CACXmN,OAAO,CAAC,gBAAgB,EAAE,wBAAwB,CAAC,CACnDA,OAAO,CAAC,gBAAgB,EAAEH,WAAW,CAAC,CACtCG,OAAO,CAAC,SAAS,EAAEvN,KAAK,CAACuK,IAAI,CAAC,CAAC,CAAC,GACnCvK,KAAK,CAACuK,IAAI,CAAC,CAAC;MAEhB,MAAM/B,QAAQ,GAAG,MAAMnE,KAAK,CAAC,GAAG5E,WAAW,YAAY,EAAE;QACvDgJ,MAAM,EAAE,MAAM;QACdhD,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAU7F,WAAW;QACtC,CAAC;QACDiI,IAAI,EAAEY,IAAI,CAACC,SAAS,CAAC;UACnB3I,KAAK,EAAEsN,WAAW;UAClBE,WAAW,EAAE,CAAC,CAACxL,YAAY;UAC3ByL,SAAS,EAAEzL,YAAY;UACvB0L,KAAK,EAAEpL,8BAA8B,GAAG,iBAAiB,GAAG;QAC9D,CAAC;MACH,CAAC,CAAC;MAEF,MAAMmC,IAAI,GAAG,MAAM+D,QAAQ,CAAChE,IAAI,CAAC,CAAC;MAElC,IAAIgE,QAAQ,CAAC7C,EAAE,EAAE;QAAA,IAAAgI,WAAA;QACf,IAAInB,UAAU,GAAG/H,IAAI,CAACgI,MAAM,IAAI,GAAG;QAEnC,IAAIhI,IAAI,CAACiJ,KAAK,KAAK,iBAAiB,EAAE;UACpCnL,iCAAiC,CAAC,IAAI,CAAC;QACzC,CAAC,MAAM,IAAIkC,IAAI,CAACiJ,KAAK,KAAK,mBAAmB,EAAE;UAC7C1K,aAAa,CAAC,CAAC,CAAC;UAChBf,eAAe,CAAC,IAAI,CAAC;UACrB4B,sBAAsB,CAAC,IAAI,CAAC;UAC5ByE,MAAM,CAACxE,eAAe,CAAC,CAAC,CAAC,CAAC;;UAE1B;UACAhB,mBAAmB,CAAC,CAAC,CAAC;UACtB;UACA;QACF;QAEA,MAAM4J,QAAQ,IAAAiB,WAAA,GAAGlJ,IAAI,CAAC4D,KAAK,cAAAsF,WAAA,cAAAA,WAAA,GAAI,EAAE;QACjC,MAAMhB,UAAU,GAAGD,QAAQ,CAACvE,MAAM,CAAEyE,CAAC,IAAK;UACxC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;YACzB,OAAOA,CAAC,CAACrC,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAACqC,CAAC,CAACpB,WAAW,CAAC,CAAC,CAACqB,UAAU,CAAC,MAAM,CAAC;UAC/D;UACA,IAAI,OAAOD,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,CAACE,QAAQ,EAAE;YACrD,OACEF,CAAC,CAACE,QAAQ,CAACvC,IAAI,CAAC,CAAC,KAAK,EAAE,IACxB,CAACqC,CAAC,CAACE,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAACqB,UAAU,CAAC,MAAM,CAAC;UAEhD;UACA,OAAO,KAAK;QACd,CAAC,CAAC;QAEF,IAAIF,UAAU,CAACzF,MAAM,GAAG,CAAC,EAAE;UACzBrG,eAAe,CACb8L,UAAU,CAACjB,GAAG,CAAEkB,CAAC,IAAK;YACpB,MAAME,QAAQ,GAAG,OAAOF,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGA,CAAC,CAACE,QAAQ;YACvD,MAAMnF,GAAG,GACP,OAAOiF,CAAC,KAAK,QAAQ,IAAI,CAACA,CAAC,CAACjF,GAAG,IAAI,CAACiF,CAAC,CAACjF,GAAG,CAACkF,UAAU,CAAC,MAAM,CAAC,GACxD,GAAGpN,WAAW,cAAcsN,kBAAkB,CAACD,QAAQ,CAAC,UAAUjN,WAAW,EAAE,GAC/E,GAAG+M,CAAC,CAACjF,GAAG,GAAGiF,CAAC,CAACjF,GAAG,CAAC6C,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,SAAS3K,WAAW,EAAE;YACtE,OAAO;cACLgI,WAAW,EAAEiF,QAAQ;cACrBnF,GAAG,EAAEA;YACP,CAAC;UACH,CAAC,CACH,CAAC;UACD,IAAI,CAAC6E,UAAU,CAAChB,WAAW,CAAC,CAAC,CAAChB,QAAQ,CAAC,8BAA8B,CAAC,EAAE;YACtEgC,UAAU,IAAI,qEAAqE;UACrF;QACF,CAAC,MAAM;UACL3L,eAAe,CAAC,IAAI,CAAC;QACvB;QAEAV,WAAW,CAAE+H,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UACEhC,EAAE,EAAEI,IAAI,CAAC2C,GAAG,CAAC,CAAC;UACd9C,IAAI,EAAE,KAAK;UACXC,OAAO,EAAEoG,UAAU;UACnBnG,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;;QAEF;QACA,IAAItE,YAAY,IAAIe,UAAU,KAAK,CAAC,EAAE;UACpCD,mBAAmB,CAAEoF,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;QACzC;QACA;;QAEA,IAAIzD,IAAI,CAACiJ,KAAK,KAAK,aAAa,EAAE;UAChCnM,4BAA4B,CAAC,IAAI,CAAC;QACpC;MACF,CAAC,MAAM;QACLd,QAAQ,CAACgE,IAAI,CAACjE,KAAK,IAAI,0BAA0B,CAAC;MACpD;IACF,CAAC,CAAC,OAAOoE,GAAG,EAAE;MACZnE,QAAQ,CAAC,iBAAiB,GAAGmE,GAAG,CAAC2B,OAAO,CAAC;IAC3C,CAAC,SAAS;MACRhG,UAAU,CAAC,KAAK,CAAC;IACnB;EACF;;EAGE;EACA,SAASsI,YAAYA,CAAA,EAAG;IACtB/I,YAAY,CAAC0G,UAAU,CAAC,QAAQ,CAAC;IACjC1G,YAAY,CAAC0G,UAAU,CAAC,SAAS,CAAC;IAClC1G,YAAY,CAAC0G,UAAU,CAAC,UAAU,CAAC;IACnCxB,MAAM,CAACC,QAAQ,CAACwB,IAAI,GAAG,OAAO;EAChC;;EAEA;EACA,MAAMmH,UAAU,GAAIvH,SAAS,IAAK;IAChC,OAAOA,SAAS,CAACwH,kBAAkB,CAAC,EAAE,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC,CAAC;EACjF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KACrB9N,QAAQ,CAACwL,GAAG,CAAEnF,OAAO,iBACnB/G,OAAA;IAEEyO,SAAS,EAAE,WAAW1H,OAAO,CAACJ,IAAI,EAAG;IACrC+H,KAAK,EAAE;MAAEC,SAAS,EAAE5H,OAAO,CAACJ,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG;IAAO,CAAE;IACjE,aAAU,QAAQ;IAAAiI,QAAA,eAElB5O,OAAA;MACEyO,SAAS,EAAC,iBAAiB;MAC3BC,KAAK,EAAE;QACLG,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,KAAK;QACfC,OAAO,EAAE,UAAU;QACnBC,YAAY,EAAE,MAAM;QACpBC,eAAe,EAAElI,OAAO,CAACJ,IAAI,KAAK,MAAM,GAAG,SAAS,GAAG;MACzD,CAAE;MAAAiI,QAAA,gBAEF5O,OAAA;QACEyO,SAAS,EAAC,cAAc;QACxBC,KAAK,EAAE;UACLQ,UAAU,EAAE,UAAU;UACtBC,SAAS,EAAE,MAAM;UACjBC,SAAS,EAAE,OAAO;UAClBC,QAAQ,EAAE,KAAK;UACfC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,GAED7H,OAAO,CAACH,OAAO,CAAC2I,KAAK,CAAC,IAAI,CAAC,CAACrD,GAAG,CAAC,CAACsD,IAAI,EAAEhD,GAAG,KAAK;UAC9C,MAAMiD,KAAK,GAAG,EAAE;UAChB,IAAIC,SAAS,GAAGF,IAAI;UACpB,IAAIG,QAAQ,GAAG,CAAC;UAEhB,OAAOD,SAAS,CAAChI,MAAM,GAAG,CAAC,EAAE;YAC3B,MAAMkI,SAAS,GAAGF,SAAS,CAACG,KAAK,CAAC,wBAAwB,CAAC;YAC3D,MAAMC,SAAS,GAAGJ,SAAS,CAACG,KAAK,CAAC,eAAe,CAAC;YAElD,IAAID,SAAS,KAAK,CAACE,SAAS,IAAIF,SAAS,CAACvD,KAAK,GAAGyD,SAAS,CAACzD,KAAK,CAAC,EAAE;cAClE,IAAIpF,IAAI,GAAG2I,SAAS,CAAC,CAAC,CAAC;cACvB,IAAI,CAAC3I,IAAI,CAACoG,UAAU,CAAC,MAAM,CAAC,EAAE;gBAC5BpG,IAAI,GAAG,GAAGhH,WAAW,GAAGgH,IAAI,CAACoG,UAAU,CAAC,GAAG,CAAC,GAAGpG,IAAI,GAAG,GAAG,GAAGA,IAAI,EAAE;cACpE;cACA,IACE,CAACA,IAAI,CAACoG,UAAU,CAAC,GAAGpN,WAAW,aAAa,CAAC,IAAIgH,IAAI,CAACoG,UAAU,CAAC,aAAa,CAAC,KAC/EhN,WAAW,IACX,CAAC4G,IAAI,CAAC+D,QAAQ,CAAC,QAAQ,CAAC,EACxB;gBACA/D,IAAI,IAAIA,IAAI,CAAC+D,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU3K,WAAW,EAAE,GAAG,UAAUA,WAAW,EAAE;cAChF;cACAoP,KAAK,CAAC9B,IAAI,cACR3N,OAAA;gBAAA4O,QAAA,GACGc,SAAS,CAACK,KAAK,CAAC,CAAC,EAAEH,SAAS,CAACvD,KAAK,CAAC,eACpCrM,OAAA;kBACEiH,IAAI,EAAEA,IAAK;kBACX2C,MAAM,EAAC,QAAQ;kBACfoG,GAAG,EAAC,qBAAqB;kBACzBtB,KAAK,EAAE;oBAAEuB,KAAK,EAAE,SAAS;oBAAEC,cAAc,EAAE;kBAAY,CAAE;kBAAAtB,QAAA,EAExDgB,SAAS,CAAC,CAAC;gBAAC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC;cAAA,GATKX,QAAQ,EAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUf,CACR,CAAC;cACDZ,SAAS,GAAGA,SAAS,CAACK,KAAK,CAACH,SAAS,CAACvD,KAAK,GAAGuD,SAAS,CAAC,CAAC,CAAC,CAAClI,MAAM,CAAC;YACpE,CAAC,MAAM,IAAIoI,SAAS,EAAE;cACpBL,KAAK,CAAC9B,IAAI,cACR3N,OAAA;gBAAA4O,QAAA,GACGc,SAAS,CAACK,KAAK,CAAC,CAAC,EAAED,SAAS,CAACzD,KAAK,CAAC,eACpCrM,OAAA;kBAAA4O,QAAA,EAASkB,SAAS,CAAC,CAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA,GAFtBX,QAAQ,EAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGf,CACR,CAAC;cACDZ,SAAS,GAAGA,SAAS,CAACK,KAAK,CAACD,SAAS,CAACzD,KAAK,GAAGyD,SAAS,CAAC,CAAC,CAAC,CAACpI,MAAM,CAAC;YACpE,CAAC,MAAM;cACL+H,KAAK,CAAC9B,IAAI,cAAC3N,OAAA;gBAAA4O,QAAA,EAAwBc;cAAS,GAAtBC,QAAQ,EAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAmB,CAAC,CAAC;cACrD;YACF;UACF;UAEA,oBAAOtQ,OAAA;YAAA4O,QAAA,EAAgBa;UAAK,GAAXjD,GAAG;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QACrC,CAAC,CAAC,EACDvJ,OAAO,CAAC9D,OAAO,iBACdjD,OAAA;UAAK0O,KAAK,EAAE;YAAE6B,SAAS,EAAE;UAAM,CAAE;UAAA3B,QAAA,EAC9B7H,OAAO,CAAC9D,OAAO,CAACiJ,GAAG,CAAC,CAAC7E,MAAM,EAAEmF,GAAG,kBAC/BxM,OAAA;YAEE0O,KAAK,EAAE;cACL8B,MAAM,EAAE,SAAS;cACjBzB,OAAO,EAAE,SAAS;cAClB0B,YAAY,EACVjE,GAAG,KAAKzF,OAAO,CAAC9D,OAAO,CAACyE,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG;YAC5D,CAAE;YACFgJ,OAAO,EAAEA,CAAA,KAAMlF,kBAAkB,CAACnE,MAAM,CAACvB,YAAY,CAAE;YAAA8I,QAAA,GAEtDvH,MAAM,CAACgF,KAAK,EAAC,IAAE,eAAArM,OAAA;cAAA4O,QAAA,EAASvH,MAAM,CAACvB;YAAY;cAAAqK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,YAAG,EAACjJ,MAAM,CAACQ,KAAK;UAAA,GATjER,MAAM,CAACvB,YAAY;YAAAqK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUrB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNtQ,OAAA;QACEyO,SAAS,EAAC,cAAc;QACxBC,KAAK,EAAE;UAAEW,QAAQ,EAAE,OAAO;UAAEY,KAAK,EAAE,MAAM;UAAEM,SAAS,EAAE;QAAM,CAAE;QAAA3B,QAAA,EAE7DR,UAAU,CAACrH,OAAO,CAACF,SAAS;MAAC;QAAAsJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC,GArGDvJ,OAAO,CAACL,EAAE;IAAAyJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAsGZ,CACN,CAAC;EAEJ,oBACEtQ,OAAA;IAAKyO,SAAS,EAAC,gBAAgB;IAACkC,IAAI,EAAC,MAAM;IAAC,cAAW,kBAAkB;IAAA/B,QAAA,gBACvE5O,OAAA;MAAKyO,SAAS,EAAC,aAAa;MAACC,KAAK,EAAE;QAAEkC,QAAQ,EAAE;MAAW,CAAE;MAAAhC,QAAA,gBAC3D5O,OAAA;QAAA4O,QAAA,EAAI;MAAkC;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3CtQ,OAAA;QAAGyO,SAAS,EAAC,UAAU;QAAAG,QAAA,EAAC;MAAiC;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,EAC5D5O,QAAQ,iBACP1B,OAAA;QAAG0O,KAAK,EAAE;UAAEW,QAAQ,EAAE,OAAO;UAAEY,KAAK,EAAE;QAAO,CAAE;QAAArB,QAAA,GAAC,gBAChC,eAAA5O,OAAA;UAAA4O,QAAA,EAASlN;QAAQ;UAAAyO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CACJ,eACDtQ,OAAA;QAAK0O,KAAK,EAAE;UAAEkC,QAAQ,EAAE,UAAU;UAAEC,GAAG,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAG,CAAE;QAAAlC,QAAA,eACvD5O,OAAA;UACE0Q,OAAO,EAAErH,YAAa;UACtBqF,KAAK,EAAE;YACLK,OAAO,EAAE,UAAU;YACnByB,MAAM,EAAE,SAAS;YACjBvB,eAAe,EAAE,SAAS;YAC1B8B,MAAM,EAAE,MAAM;YACd/B,YAAY,EAAE,CAAC;YACfiB,KAAK,EAAE,OAAO;YACde,UAAU,EAAE;UACd,CAAE;UACF,cAAW,QAAQ;UACnBnJ,KAAK,EAAC,QAAQ;UAAA+G,QAAA,EACf;QAED;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtQ,OAAA;MAAKyO,SAAS,EAAC,eAAe;MAAC,aAAU,QAAQ;MAAC,iBAAc,WAAW;MAAAG,QAAA,GACxEJ,cAAc,CAAC,CAAC,EAChB,CAAC1N,OAAO,IAAIU,SAAS,kBACpBxB,OAAA;QAAKyO,SAAS,EAAC,oBAAoB;QAAC,cAAW,WAAW;QAAAG,QAAA,eACxD5O,OAAA;UAAKyO,SAAS,EAAC,iBAAiB;UAAAG,QAAA,gBAC9B5O,OAAA;YAAKyO,SAAS,EAAC,kBAAkB;YAAC,eAAY,MAAM;YAAAG,QAAA,gBAClD5O,OAAA;cAAAmQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbtQ,OAAA;cAAAmQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbtQ,OAAA;cAAAmQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNtQ,OAAA;YAAKyO,SAAS,EAAC,cAAc;YAAAG,QAAA,EAC1BpN,SAAS,GAAG,2BAA2B,GAAG;UAAc;YAAA2O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAlP,YAAY,IAAIA,YAAY,CAACsG,MAAM,GAAG,CAAC,iBACtC1H,OAAA;QAAK0O,KAAK,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEJ,SAAS,EAAE;QAAS,CAAE;QAAAC,QAAA,eACnD5O,OAAA,CAACJ,YAAY;UACXqR,KAAK,EAAEA,CAAA,KAAM3H,mBAAmB,CAAC,IAAI,EAAE,eAAe,CAAE;UACxD4H,IAAI,EAAEA,CAAA,KAAM5H,mBAAmB,CAAC,KAAK,EAAE,eAAe,CAAE;UACxD6H,OAAO,EAAC,eAAe;UACvBC,MAAM,EAAC;QAAM;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGAtO,oBAAoB,iBACnBhC,OAAA;QAAK0O,KAAK,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEJ,SAAS,EAAE;QAAS,CAAE;QAAAC,QAAA,eACnD5O,OAAA,CAACJ,YAAY;UACXqR,KAAK,EAAEA,CAAA,KAAM3H,mBAAmB,CAAC,IAAI,EAAE,cAAc,CAAE;UACvD4H,IAAI,EAAEA,CAAA,KAAM5H,mBAAmB,CAAC,KAAK,EAAE,cAAc,CAAE;UACvD6H,OAAO,EAAC,KAAK;UACbC,MAAM,EAAC;QAAI;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGAxO,yBAAyB,iBACxB9B,OAAA;QAAK0O,KAAK,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEJ,SAAS,EAAE;QAAS,CAAE;QAAAC,QAAA,eACnD5O,OAAA,CAACJ,YAAY;UACXqR,KAAK,EAAEA,CAAA,KAAM3H,mBAAmB,CAAC,IAAI,EAAE,cAAc,CAAE;UACvD4H,IAAI,EAAEA,CAAA,KAAM5H,mBAAmB,CAAC,KAAK,EAAE,cAAc,CAAE;UACvD6H,OAAO,EAAC,cAAc;UACtBC,MAAM,EAAC;QAAW;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,eAEDtQ,OAAA;QAAKqR,GAAG,EAAE5M;MAAe;QAAA0L,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,EAELtP,KAAK,iBACJhB,OAAA;MACEyO,SAAS,EAAC,eAAe;MACzBiC,OAAO,EAAEA,CAAA,KAAMzP,QAAQ,CAAC,EAAE,CAAE;MAC5ByN,KAAK,EAAE;QAAE8B,MAAM,EAAE;MAAU,CAAE;MAC7BG,IAAI,EAAC,OAAO;MACZ,aAAU,WAAW;MACrBW,QAAQ,EAAE,CAAE;MAAA1C,QAAA,GAEX5N,KAAK,EAAC,qBACT;IAAA;MAAAmP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,eAEDtQ,OAAA;MAAMyO,SAAS,EAAC,iBAAiB;MAAC8C,QAAQ,EAAE1F,YAAa;MAAC,cAAW,mBAAmB;MAAA+C,QAAA,eACtF5O,OAAA;QAAKyO,SAAS,EAAC,iBAAiB;QAACC,KAAK,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAE2C,UAAU,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAA7C,QAAA,gBAC/F5O,OAAA;UACE2G,IAAI,EAAC,MAAM;UACXkD,KAAK,EAAErJ,KAAM;UACbkR,QAAQ,EAAEhI,aAAc;UACxBiI,WAAW,EACTrQ,WAAW,GACPiC,UAAU,GAAG,CAAC,GACZe,eAAe,CAACf,UAAU,GAAG,CAAC,CAAC,GAC/BT,8BAA8B,GAC9B,gDAAgD,GAChDhB,yBAAyB,GACzB,6CAA6C,GAC7C,4BAA4B,GAC9B,iCACL;UACD8P,QAAQ,EAAE9Q,OAAO,IAAIU,SAAS,IAAK+B,UAAU,KAAK,CAAC,IAAI,CAAC/C,KAAO;UAC/DqR,SAAS;UACT,cAAW,YAAY;UACvBnD,KAAK,EAAE;YAAEoD,IAAI,EAAE,GAAG;YAAEL,KAAK,EAAE,MAAM;YAAE1C,OAAO,EAAE,KAAK;YAAEC,YAAY,EAAE,aAAa;YAAE+B,MAAM,EAAE,gBAAgB;YAAEgB,MAAM,EAAE;UAAE,CAAE;UACtHC,SAAS,EAAGrI,CAAC,IAAK;YAChB,IAAIA,CAAC,CAACsI,GAAG,KAAK,OAAO,IAAI,CAACtI,CAAC,CAACuI,QAAQ,EAAE;cACpCvI,CAAC,CAACmC,cAAc,CAAC,CAAC;cAClBD,YAAY,CAAClC,CAAC,CAAC;YACjB;UACF;QAAE;UAAAwG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACD/M,UAAU,KAAK,CAAC,iBACfvD,OAAA;UACE6J,KAAK,EAAEpG,UAAU,CAACE,WAAY;UAC9B+N,QAAQ,EAAE5H,uBAAwB;UAClC4E,KAAK,EAAE;YACLyD,UAAU,EAAE,GAAG;YACfpD,OAAO,EAAE,KAAK;YACdC,YAAY,EAAE,GAAG;YACjB+B,MAAM,EAAE,gBAAgB;YACxBqB,UAAU,EAAE,MAAM;YAClB/C,QAAQ,EAAE;UACZ,CAAE;UACF,cAAW,qBAAqB;UAAAT,QAAA,gBAEhC5O,OAAA;YAAQ6J,KAAK,EAAC,EAAE;YAAC+H,QAAQ;YAAAhD,QAAA,EAAC;UAE1B;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACR/L,kBAAkB,CAAC2H,GAAG,CAAEmG,MAAM,iBAC7BrS,OAAA;YAAqB6J,KAAK,EAAEwI,MAAO;YAAAzD,QAAA,EAChCyD;UAAM,GADIA,MAAM;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEX,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACT,eACDtQ,OAAA;UACE2G,IAAI,EAAC,QAAQ;UACbiL,QAAQ,EAAE9Q,OAAO,IAAI,CAACN,KAAK,CAACuK,IAAI,CAAC,CAAC,IAAIvJ,SAAS,IAAK+B,UAAU,KAAK,CAAC,IAAI,CAACE,UAAU,CAACE,WAAa;UACjGkE,KAAK,EAAE/G,OAAO,IAAIU,SAAS,GAAG,gBAAgB,GAAG,MAAO;UACxD,cAAW,cAAc;UACzBkN,KAAK,EAAE;YAAEK,OAAO,EAAE,UAAU;YAAEC,YAAY,EAAE,aAAa;YAAE+B,MAAM,EAAE,gBAAgB;YAAEqB,UAAU,EAAE,MAAM;YAAEnD,eAAe,EAAE,SAAS;YAAEgB,KAAK,EAAE;UAAQ,CAAE;UAAArB,QAAA,EAErJ9N,OAAO,IAAIU,SAAS,gBAAGxB,OAAA;YAAMyO,SAAS,EAAC;UAAS;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAI;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAAClQ,EAAA,CAl5CuBF,IAAI;AAAAoS,EAAA,GAAJpS,IAAI;AAAA,IAAAoS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}