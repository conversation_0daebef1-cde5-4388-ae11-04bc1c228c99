{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AI-Agent-Chatbot-main\\\\frontend\\\\src\\\\NewTicketForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport \"./App.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BACKEND_URL = \"http://localhost:8000\";\nexport default function NewTicketForm({\n  token\n}) {\n  _s();\n  const navigate = useNavigate();\n  const accessToken = token || localStorage.getItem(\"access\");\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n  const [formData, setFormData] = useState({\n    shortTitle: \"\",\n    productType: \"Camera\",\n    productName: \"\",\n    model: \"\",\n    serialNo: \"\",\n    purchasedFrom: \"\",\n    yearOfPurchase: \"\",\n    operatingSystem: \"\",\n    problemDescription: \"\"\n  });\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    if (error) setError(\"\");\n  };\n  const validateForm = () => {\n    const requiredFields = ['shortTitle', 'productName', 'model', 'serialNo', 'purchasedFrom', 'yearOfPurchase', 'operatingSystem', 'problemDescription'];\n    for (let field of requiredFields) {\n      if (!formData[field] || formData[field].trim() === \"\") {\n        return `Please fill in the ${field.replace(/([A-Z])/g, ' $1').toLowerCase()} field.`;\n      }\n    }\n    return null;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const validationError = validateForm();\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n    setLoading(true);\n    setError(\"\");\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        },\n        body: JSON.stringify({\n          short_title: formData.shortTitle,\n          product_type: formData.productType,\n          product_name: formData.productName,\n          model: formData.model,\n          serial_no: formData.serialNo,\n          purchased_from: formData.purchasedFrom,\n          year_of_purchase: formData.yearOfPurchase,\n          operating_system: formData.operatingSystem,\n          problem_description: formData.problemDescription\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        // Redirect to Home.jsx chatbot with the new ticket\n        navigate(`/legacy-chat?ticket=${data.ticket_number}&mode=new`);\n      } else {\n        setError(data.message || \"Failed to create ticket. Please try again.\");\n      }\n    } catch (err) {\n      console.error(\"Error creating ticket:\", err);\n      setError(\"Network error. Please check your connection and try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCancel = () => {\n    navigate(\"/actions\");\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: \"40px\",\n      maxWidth: \"600px\",\n      margin: \"0 auto\",\n      fontFamily: \"Arial, sans-serif\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      style: {\n        color: \"#333\",\n        marginBottom: \"20px\",\n        textAlign: \"center\"\n      },\n      children: \"Create New Support Ticket\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        fontSize: \"1.1rem\",\n        color: \"#666\",\n        marginBottom: \"30px\",\n        textAlign: \"center\"\n      },\n      children: \"Please provide your product details below. After submitting, you'll be able to describe your issue in the chat.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: \"#ffebee\",\n        color: \"#c62828\",\n        padding: \"12px\",\n        borderRadius: \"4px\",\n        marginBottom: \"20px\",\n        border: \"1px solid #ef5350\"\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      style: {\n        display: \"flex\",\n        flexDirection: \"column\",\n        gap: \"20px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Short Title *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"shortTitle\",\n          value: formData.shortTitle,\n          onChange: handleInputChange,\n          placeholder: \"Brief description of the issue\",\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Product Type *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          name: \"productType\",\n          value: formData.productType,\n          onChange: handleInputChange,\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Camera\",\n            children: \"Camera\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Frame Grabber\",\n            children: \"Frame Grabber\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Accessories\",\n            children: \"Accessories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Software\",\n            children: \"Software\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Product Name *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"productName\",\n          value: formData.productName,\n          onChange: handleInputChange,\n          placeholder: \"e.g., Genie Nano, Falcon4-CLHS\",\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Model *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"model\",\n          value: formData.model,\n          onChange: handleInputChange,\n          placeholder: \"e.g., C2590, M1920\",\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Serial Number *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"serialNo\",\n          value: formData.serialNo,\n          onChange: handleInputChange,\n          placeholder: \"e.g., SN123456789\",\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"PO Number *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"poNumber\",\n          value: formData.poNumber,\n          onChange: handleInputChange,\n          placeholder: \"Purchase Order Number\",\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Problem Description *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          name: \"problemDescription\",\n          value: formData.problemDescription,\n          onChange: handleInputChange,\n          placeholder: \"Describe the issue you're experiencing in detail...\",\n          required: true,\n          rows: 4,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\",\n            resize: \"vertical\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          gap: \"15px\",\n          marginTop: \"20px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handleCancel,\n          disabled: loading,\n          style: {\n            flex: 1,\n            padding: \"15px\",\n            backgroundColor: \"#6c757d\",\n            color: \"white\",\n            border: \"none\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\",\n            cursor: loading ? \"not-allowed\" : \"pointer\"\n          },\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: loading,\n          style: {\n            flex: 2,\n            padding: \"15px\",\n            backgroundColor: loading ? \"#ccc\" : \"#4CAF50\",\n            color: \"white\",\n            border: \"none\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\",\n            cursor: loading ? \"not-allowed\" : \"pointer\"\n          },\n          children: loading ? \"Creating Ticket...\" : \"Create Ticket & Start Chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 5\n  }, this);\n}\n_s(NewTicketForm, \"CMAT47PhuSJv/WXEHUFe88nEQPo=\", false, function () {\n  return [useNavigate];\n});\n_c = NewTicketForm;\nvar _c;\n$RefreshReg$(_c, \"NewTicketForm\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "jsxDEV", "_jsxDEV", "BACKEND_URL", "NewTicketForm", "token", "_s", "navigate", "accessToken", "localStorage", "getItem", "loading", "setLoading", "error", "setError", "formData", "setFormData", "shortTitle", "productType", "productName", "model", "serialNo", "purchasedFrom", "yearOfPurchase", "operatingSystem", "problemDescription", "handleInputChange", "e", "name", "value", "target", "prev", "validateForm", "requiredFields", "field", "trim", "replace", "toLowerCase", "handleSubmit", "preventDefault", "validationError", "response", "fetch", "method", "headers", "Authorization", "body", "JSON", "stringify", "short_title", "product_type", "product_name", "serial_no", "purchased_from", "year_of_purchase", "operating_system", "problem_description", "data", "json", "ok", "ticket_number", "message", "err", "console", "handleCancel", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "fontFamily", "children", "color", "marginBottom", "textAlign", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "backgroundColor", "borderRadius", "border", "onSubmit", "display", "flexDirection", "gap", "fontWeight", "type", "onChange", "placeholder", "required", "width", "poNumber", "rows", "resize", "marginTop", "onClick", "disabled", "flex", "cursor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/AI-Agent-Chatbot-main/frontend/src/NewTicketForm.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport \"./App.css\";\n\nconst BACKEND_URL = \"http://localhost:8000\";\n\nexport default function NewTicketForm({ token }) {\n  const navigate = useNavigate();\n  const accessToken = token || localStorage.getItem(\"access\");\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  const [formData, setFormData] = useState({\n    shortTitle: \"\",\n    productType: \"Camera\",\n    productName: \"\",\n    model: \"\",\n    serialNo: \"\",\n    purchasedFrom: \"\",\n    yearOfPurchase: \"\",\n    operatingSystem: \"\",\n    problemDescription: \"\",\n  });\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    if (error) setError(\"\");\n  };\n\n  const validateForm = () => {\n    const requiredFields = [\n      'shortTitle', 'productName', 'model', 'serialNo', 'purchasedFrom', 'yearOfPurchase', 'operatingSystem', 'problemDescription'\n    ];\n\n    for (let field of requiredFields) {\n      if (!formData[field] || formData[field].trim() === \"\") {\n        return `Please fill in the ${field.replace(/([A-Z])/g, ' $1').toLowerCase()} field.`;\n      }\n    }\n\n    return null;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const validationError = validateForm();\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    setLoading(true);\n    setError(\"\");\n\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`,\n        },\n        body: JSON.stringify({\n          short_title: formData.shortTitle,\n          product_type: formData.productType,\n          product_name: formData.productName,\n          model: formData.model,\n          serial_no: formData.serialNo,\n          purchased_from: formData.purchasedFrom,\n          year_of_purchase: formData.yearOfPurchase,\n          operating_system: formData.operatingSystem,\n          problem_description: formData.problemDescription,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        // Redirect to Home.jsx chatbot with the new ticket\n        navigate(`/legacy-chat?ticket=${data.ticket_number}&mode=new`);\n      } else {\n        setError(data.message || \"Failed to create ticket. Please try again.\");\n      }\n    } catch (err) {\n      console.error(\"Error creating ticket:\", err);\n      setError(\"Network error. Please check your connection and try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCancel = () => {\n    navigate(\"/actions\");\n  };\n\n  return (\n    <div style={{ \n      padding: \"40px\", \n      maxWidth: \"600px\", \n      margin: \"0 auto\", \n      fontFamily: \"Arial, sans-serif\"\n    }}>\n      <h1 style={{ \n        color: \"#333\", \n        marginBottom: \"20px\",\n        textAlign: \"center\"\n      }}>\n        Create New Support Ticket\n      </h1>\n      \n      <p style={{ \n        fontSize: \"1.1rem\", \n        color: \"#666\", \n        marginBottom: \"30px\",\n        textAlign: \"center\"\n      }}>\n        Please provide your product details below. After submitting, you'll be able to describe your issue in the chat.\n      </p>\n\n      {error && (\n        <div style={{\n          backgroundColor: \"#ffebee\",\n          color: \"#c62828\",\n          padding: \"12px\",\n          borderRadius: \"4px\",\n          marginBottom: \"20px\",\n          border: \"1px solid #ef5350\"\n        }}>\n          {error}\n        </div>\n      )}\n\n      <form onSubmit={handleSubmit} style={{ display: \"flex\", flexDirection: \"column\", gap: \"20px\" }}>\n\n        {/* Short Title */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Short Title *\n          </label>\n          <input\n            type=\"text\"\n            name=\"shortTitle\"\n            value={formData.shortTitle}\n            onChange={handleInputChange}\n            placeholder=\"Brief description of the issue\"\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n\n        {/* Product Type */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Product Type *\n          </label>\n          <select\n            name=\"productType\"\n            value={formData.productType}\n            onChange={handleInputChange}\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          >\n            <option value=\"Camera\">Camera</option>\n            <option value=\"Frame Grabber\">Frame Grabber</option>\n            <option value=\"Accessories\">Accessories</option>\n            <option value=\"Software\">Software</option>\n          </select>\n        </div>\n\n        {/* Product Name */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Product Name *\n          </label>\n          <input\n            type=\"text\"\n            name=\"productName\"\n            value={formData.productName}\n            onChange={handleInputChange}\n            placeholder=\"e.g., Genie Nano, Falcon4-CLHS\"\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n\n        {/* Model */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Model *\n          </label>\n          <input\n            type=\"text\"\n            name=\"model\"\n            value={formData.model}\n            onChange={handleInputChange}\n            placeholder=\"e.g., C2590, M1920\"\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n\n        {/* Serial Number */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Serial Number *\n          </label>\n          <input\n            type=\"text\"\n            name=\"serialNo\"\n            value={formData.serialNo}\n            onChange={handleInputChange}\n            placeholder=\"e.g., SN123456789\"\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n\n        {/* PO Number */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            PO Number *\n          </label>\n          <input\n            type=\"text\"\n            name=\"poNumber\"\n            value={formData.poNumber}\n            onChange={handleInputChange}\n            placeholder=\"Purchase Order Number\"\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n\n        {/* Problem Description */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Problem Description *\n          </label>\n          <textarea\n            name=\"problemDescription\"\n            value={formData.problemDescription}\n            onChange={handleInputChange}\n            placeholder=\"Describe the issue you're experiencing in detail...\"\n            required\n            rows={4}\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\",\n              resize: \"vertical\"\n            }}\n          />\n        </div>\n\n        {/* Buttons */}\n        <div style={{ display: \"flex\", gap: \"15px\", marginTop: \"20px\" }}>\n          <button\n            type=\"button\"\n            onClick={handleCancel}\n            disabled={loading}\n            style={{\n              flex: 1,\n              padding: \"15px\",\n              backgroundColor: \"#6c757d\",\n              color: \"white\",\n              border: \"none\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\",\n              cursor: loading ? \"not-allowed\" : \"pointer\"\n            }}\n          >\n            Cancel\n          </button>\n          \n          <button\n            type=\"submit\"\n            disabled={loading}\n            style={{\n              flex: 2,\n              padding: \"15px\",\n              backgroundColor: loading ? \"#ccc\" : \"#4CAF50\",\n              color: \"white\",\n              border: \"none\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\",\n              cursor: loading ? \"not-allowed\" : \"pointer\"\n            }}\n          >\n            {loading ? \"Creating Ticket...\" : \"Create Ticket & Start Chat\"}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,MAAMC,WAAW,GAAG,uBAAuB;AAE3C,eAAe,SAASC,aAAaA,CAAC;EAAEC;AAAM,CAAC,EAAE;EAAAC,EAAA;EAC/C,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAMQ,WAAW,GAAGH,KAAK,IAAII,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;EAC3D,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,QAAQ;IACrBC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE,EAAE;IAClBC,eAAe,EAAE,EAAE;IACnBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCd,WAAW,CAACe,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;IACH,IAAIhB,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC;EAED,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,cAAc,GAAG,CACrB,YAAY,EAAE,aAAa,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,oBAAoB,CAC7H;IAED,KAAK,IAAIC,KAAK,IAAID,cAAc,EAAE;MAChC,IAAI,CAAClB,QAAQ,CAACmB,KAAK,CAAC,IAAInB,QAAQ,CAACmB,KAAK,CAAC,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACrD,OAAO,sBAAsBD,KAAK,CAACE,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACC,WAAW,CAAC,CAAC,SAAS;MACtF;IACF;IAEA,OAAO,IAAI;EACb,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,MAAMC,eAAe,GAAGR,YAAY,CAAC,CAAC;IACtC,IAAIQ,eAAe,EAAE;MACnB1B,QAAQ,CAAC0B,eAAe,CAAC;MACzB;IACF;IAEA5B,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAM2B,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGvC,WAAW,qBAAqB,EAAE;QAChEwC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUrC,WAAW;QACtC,CAAC;QACDsC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,WAAW,EAAElC,QAAQ,CAACE,UAAU;UAChCiC,YAAY,EAAEnC,QAAQ,CAACG,WAAW;UAClCiC,YAAY,EAAEpC,QAAQ,CAACI,WAAW;UAClCC,KAAK,EAAEL,QAAQ,CAACK,KAAK;UACrBgC,SAAS,EAAErC,QAAQ,CAACM,QAAQ;UAC5BgC,cAAc,EAAEtC,QAAQ,CAACO,aAAa;UACtCgC,gBAAgB,EAAEvC,QAAQ,CAACQ,cAAc;UACzCgC,gBAAgB,EAAExC,QAAQ,CAACS,eAAe;UAC1CgC,mBAAmB,EAAEzC,QAAQ,CAACU;QAChC,CAAC;MACH,CAAC,CAAC;MAEF,MAAMgC,IAAI,GAAG,MAAMhB,QAAQ,CAACiB,IAAI,CAAC,CAAC;MAElC,IAAIjB,QAAQ,CAACkB,EAAE,EAAE;QACf;QACApD,QAAQ,CAAC,uBAAuBkD,IAAI,CAACG,aAAa,WAAW,CAAC;MAChE,CAAC,MAAM;QACL9C,QAAQ,CAAC2C,IAAI,CAACI,OAAO,IAAI,4CAA4C,CAAC;MACxE;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAAClD,KAAK,CAAC,wBAAwB,EAAEiD,GAAG,CAAC;MAC5ChD,QAAQ,CAAC,4DAA4D,CAAC;IACxE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoD,YAAY,GAAGA,CAAA,KAAM;IACzBzD,QAAQ,CAAC,UAAU,CAAC;EACtB,CAAC;EAED,oBACEL,OAAA;IAAK+D,KAAK,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,gBACApE,OAAA;MAAI+D,KAAK,EAAE;QACTM,KAAK,EAAE,MAAM;QACbC,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE;MACb,CAAE;MAAAH,QAAA,EAAC;IAEH;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEL3E,OAAA;MAAG+D,KAAK,EAAE;QACRa,QAAQ,EAAE,QAAQ;QAClBP,KAAK,EAAE,MAAM;QACbC,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE;MACb,CAAE;MAAAH,QAAA,EAAC;IAEH;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,EAEHhE,KAAK,iBACJX,OAAA;MAAK+D,KAAK,EAAE;QACVc,eAAe,EAAE,SAAS;QAC1BR,KAAK,EAAE,SAAS;QAChBL,OAAO,EAAE,MAAM;QACfc,YAAY,EAAE,KAAK;QACnBR,YAAY,EAAE,MAAM;QACpBS,MAAM,EAAE;MACV,CAAE;MAAAX,QAAA,EACCzD;IAAK;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED3E,OAAA;MAAMgF,QAAQ,EAAE5C,YAAa;MAAC2B,KAAK,EAAE;QAAEkB,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAf,QAAA,gBAG7FpE,OAAA;QAAAoE,QAAA,gBACEpE,OAAA;UAAO+D,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR3E,OAAA;UACEqF,IAAI,EAAC,MAAM;UACX3D,IAAI,EAAC,YAAY;UACjBC,KAAK,EAAEd,QAAQ,CAACE,UAAW;UAC3BuE,QAAQ,EAAE9D,iBAAkB;UAC5B+D,WAAW,EAAC,gCAAgC;UAC5CC,QAAQ;UACRzB,KAAK,EAAE;YACL0B,KAAK,EAAE,MAAM;YACbzB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN3E,OAAA;QAAAoE,QAAA,gBACEpE,OAAA;UAAO+D,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR3E,OAAA;UACE0B,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAEd,QAAQ,CAACG,WAAY;UAC5BsE,QAAQ,EAAE9D,iBAAkB;UAC5BgE,QAAQ;UACRzB,KAAK,EAAE;YACL0B,KAAK,EAAE,MAAM;YACbzB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ,CAAE;UAAAR,QAAA,gBAEFpE,OAAA;YAAQ2B,KAAK,EAAC,QAAQ;YAAAyC,QAAA,EAAC;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtC3E,OAAA;YAAQ2B,KAAK,EAAC,eAAe;YAAAyC,QAAA,EAAC;UAAa;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpD3E,OAAA;YAAQ2B,KAAK,EAAC,aAAa;YAAAyC,QAAA,EAAC;UAAW;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChD3E,OAAA;YAAQ2B,KAAK,EAAC,UAAU;YAAAyC,QAAA,EAAC;UAAQ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN3E,OAAA;QAAAoE,QAAA,gBACEpE,OAAA;UAAO+D,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR3E,OAAA;UACEqF,IAAI,EAAC,MAAM;UACX3D,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAEd,QAAQ,CAACI,WAAY;UAC5BqE,QAAQ,EAAE9D,iBAAkB;UAC5B+D,WAAW,EAAC,gCAAgC;UAC5CC,QAAQ;UACRzB,KAAK,EAAE;YACL0B,KAAK,EAAE,MAAM;YACbzB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN3E,OAAA;QAAAoE,QAAA,gBACEpE,OAAA;UAAO+D,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR3E,OAAA;UACEqF,IAAI,EAAC,MAAM;UACX3D,IAAI,EAAC,OAAO;UACZC,KAAK,EAAEd,QAAQ,CAACK,KAAM;UACtBoE,QAAQ,EAAE9D,iBAAkB;UAC5B+D,WAAW,EAAC,oBAAoB;UAChCC,QAAQ;UACRzB,KAAK,EAAE;YACL0B,KAAK,EAAE,MAAM;YACbzB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN3E,OAAA;QAAAoE,QAAA,gBACEpE,OAAA;UAAO+D,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR3E,OAAA;UACEqF,IAAI,EAAC,MAAM;UACX3D,IAAI,EAAC,UAAU;UACfC,KAAK,EAAEd,QAAQ,CAACM,QAAS;UACzBmE,QAAQ,EAAE9D,iBAAkB;UAC5B+D,WAAW,EAAC,mBAAmB;UAC/BC,QAAQ;UACRzB,KAAK,EAAE;YACL0B,KAAK,EAAE,MAAM;YACbzB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN3E,OAAA;QAAAoE,QAAA,gBACEpE,OAAA;UAAO+D,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR3E,OAAA;UACEqF,IAAI,EAAC,MAAM;UACX3D,IAAI,EAAC,UAAU;UACfC,KAAK,EAAEd,QAAQ,CAAC6E,QAAS;UACzBJ,QAAQ,EAAE9D,iBAAkB;UAC5B+D,WAAW,EAAC,uBAAuB;UACnCC,QAAQ;UACRzB,KAAK,EAAE;YACL0B,KAAK,EAAE,MAAM;YACbzB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN3E,OAAA;QAAAoE,QAAA,gBACEpE,OAAA;UAAO+D,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR3E,OAAA;UACE0B,IAAI,EAAC,oBAAoB;UACzBC,KAAK,EAAEd,QAAQ,CAACU,kBAAmB;UACnC+D,QAAQ,EAAE9D,iBAAkB;UAC5B+D,WAAW,EAAC,qDAAqD;UACjEC,QAAQ;UACRG,IAAI,EAAE,CAAE;UACR5B,KAAK,EAAE;YACL0B,KAAK,EAAE,MAAM;YACbzB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE,MAAM;YAChBgB,MAAM,EAAE;UACV;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN3E,OAAA;QAAK+D,KAAK,EAAE;UAAEkB,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE,MAAM;UAAEU,SAAS,EAAE;QAAO,CAAE;QAAAzB,QAAA,gBAC9DpE,OAAA;UACEqF,IAAI,EAAC,QAAQ;UACbS,OAAO,EAAEhC,YAAa;UACtBiC,QAAQ,EAAEtF,OAAQ;UAClBsD,KAAK,EAAE;YACLiC,IAAI,EAAE,CAAC;YACPhC,OAAO,EAAE,MAAM;YACfa,eAAe,EAAE,SAAS;YAC1BR,KAAK,EAAE,OAAO;YACdU,MAAM,EAAE,MAAM;YACdD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE,MAAM;YAChBqB,MAAM,EAAExF,OAAO,GAAG,aAAa,GAAG;UACpC,CAAE;UAAA2D,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET3E,OAAA;UACEqF,IAAI,EAAC,QAAQ;UACbU,QAAQ,EAAEtF,OAAQ;UAClBsD,KAAK,EAAE;YACLiC,IAAI,EAAE,CAAC;YACPhC,OAAO,EAAE,MAAM;YACfa,eAAe,EAAEpE,OAAO,GAAG,MAAM,GAAG,SAAS;YAC7C4D,KAAK,EAAE,OAAO;YACdU,MAAM,EAAE,MAAM;YACdD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE,MAAM;YAChBqB,MAAM,EAAExF,OAAO,GAAG,aAAa,GAAG;UACpC,CAAE;UAAA2D,QAAA,EAED3D,OAAO,GAAG,oBAAoB,GAAG;QAA4B;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACvE,EAAA,CA1UuBF,aAAa;EAAA,QAClBJ,WAAW;AAAA;AAAAoG,EAAA,GADNhG,aAAa;AAAA,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}