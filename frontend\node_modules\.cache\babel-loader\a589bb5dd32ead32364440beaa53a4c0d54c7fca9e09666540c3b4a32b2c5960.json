{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AI-Agent-Chatbot-main\\\\frontend\\\\src\\\\ActionsPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport \"./App.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BACKEND_URL = \"http://localhost:8000\";\nexport default function ActionsPage({\n  token\n}) {\n  _s();\n  const navigate = useNavigate();\n  const accessToken = token || localStorage.getItem(\"access\");\n  const [loading, setLoading] = useState(false);\n  const [pendingTickets, setPendingTickets] = useState([]);\n  const [userName, setUserName] = useState(\"\");\n  useEffect(() => {\n    if (!accessToken) {\n      navigate(\"/auth\");\n      return;\n    }\n\n    // Fetch user info\n    fetch(`${BACKEND_URL}/api/user_info/`, {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${accessToken}`\n      }\n    }).then(async res => {\n      if (!res.ok) {\n        throw new Error(\"User info fetch failed\");\n      }\n      return res.json();\n    }).then(data => {\n      setUserName(data.name || data.username || data.email);\n    }).catch(err => {\n      console.error(\"Failed to fetch user info:\", err);\n      localStorage.removeItem(\"access\");\n      navigate(\"/auth\");\n    });\n\n    // Fetch pending tickets\n    fetchPendingTickets();\n  }, [accessToken, navigate]);\n  const fetchPendingTickets = async () => {\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {\n        headers: {\n          Authorization: `Bearer ${accessToken}`\n        }\n      });\n      const data = await response.json();\n      if (response.ok) {\n        setPendingTickets(data.tickets || []);\n      }\n    } catch (err) {\n      console.error(\"Error fetching pending tickets:\", err);\n      setPendingTickets([]);\n    }\n  };\n  const handleRaiseNewTicket = () => {\n    navigate(\"/new-ticket\");\n  };\n  const handleUsePendingTicket = () => {\n    // Always redirect directly to Home.jsx chatbot\n    // Backend will handle fetching the latest open ticket\n    navigate(\"/?mode=pending\");\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: \"40px\",\n      maxWidth: \"800px\",\n      margin: \"0 auto\",\n      textAlign: \"center\",\n      fontFamily: \"Arial, sans-serif\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      style: {\n        color: \"#333\",\n        marginBottom: \"20px\",\n        fontSize: \"2.5rem\"\n      },\n      children: [\"Welcome, \", userName, \"!\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        fontSize: \"1.2rem\",\n        color: \"#666\",\n        marginBottom: \"40px\",\n        lineHeight: \"1.6\"\n      },\n      children: \"How can we help you today? Please choose one of the options below:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        flexDirection: \"column\",\n        gap: \"20px\",\n        alignItems: \"center\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleRaiseNewTicket,\n        disabled: loading,\n        style: {\n          backgroundColor: \"#4CAF50\",\n          color: \"white\",\n          border: \"none\",\n          padding: \"20px 40px\",\n          fontSize: \"1.3rem\",\n          borderRadius: \"8px\",\n          cursor: loading ? \"not-allowed\" : \"pointer\",\n          width: \"300px\",\n          boxShadow: \"0 4px 8px rgba(0,0,0,0.1)\",\n          transition: \"all 0.3s ease\"\n        },\n        onMouseOver: e => {\n          if (!loading) {\n            e.target.style.backgroundColor = \"#45a049\";\n            e.target.style.transform = \"translateY(-2px)\";\n          }\n        },\n        onMouseOut: e => {\n          if (!loading) {\n            e.target.style.backgroundColor = \"#4CAF50\";\n            e.target.style.transform = \"translateY(0)\";\n          }\n        },\n        children: \"\\uD83C\\uDFAB Raise New Ticket\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleUsePendingTicket,\n        style: {\n          backgroundColor: \"#2196F3\",\n          color: \"white\",\n          border: \"none\",\n          padding: \"20px 40px\",\n          fontSize: \"1.3rem\",\n          borderRadius: \"8px\",\n          cursor: \"pointer\",\n          width: \"300px\",\n          boxShadow: \"0 4px 8px rgba(0,0,0,0.1)\",\n          transition: \"all 0.3s ease\"\n        },\n        onMouseOver: e => {\n          e.target.style.backgroundColor = \"#1976D2\";\n          e.target.style.transform = \"translateY(-2px)\";\n        },\n        onMouseOut: e => {\n          e.target.style.backgroundColor = \"#2196F3\";\n          e.target.style.transform = \"translateY(0)\";\n        },\n        children: \"\\uD83D\\uDCCB Use Pending Ticket\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n}\n_s(ActionsPage, \"3aGceuNBjkt87rDKvLzuNgg6wc4=\", false, function () {\n  return [useNavigate];\n});\n_c = ActionsPage;\nvar _c;\n$RefreshReg$(_c, \"ActionsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "jsxDEV", "_jsxDEV", "BACKEND_URL", "ActionsPage", "token", "_s", "navigate", "accessToken", "localStorage", "getItem", "loading", "setLoading", "pendingTickets", "setPendingTickets", "userName", "setUserName", "fetch", "headers", "Authorization", "then", "res", "ok", "Error", "json", "data", "name", "username", "email", "catch", "err", "console", "error", "removeItem", "fetchPendingTickets", "response", "tickets", "handleRaiseNewTicket", "handleUsePendingTicket", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "textAlign", "fontFamily", "children", "color", "marginBottom", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "lineHeight", "display", "flexDirection", "gap", "alignItems", "onClick", "disabled", "backgroundColor", "border", "borderRadius", "cursor", "width", "boxShadow", "transition", "onMouseOver", "e", "target", "transform", "onMouseOut", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/AI-Agent-Chatbot-main/frontend/src/ActionsPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport \"./App.css\";\n\nconst BACKEND_URL = \"http://localhost:8000\";\n\nexport default function ActionsPage({ token }) {\n  const navigate = useNavigate();\n  const accessToken = token || localStorage.getItem(\"access\");\n  const [loading, setLoading] = useState(false);\n  const [pendingTickets, setPendingTickets] = useState([]);\n  const [userName, setUserName] = useState(\"\");\n\n  useEffect(() => {\n    if (!accessToken) {\n      navigate(\"/auth\");\n      return;\n    }\n\n    // Fetch user info\n    fetch(`${BACKEND_URL}/api/user_info/`, {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${accessToken}`,\n      },\n    })\n      .then(async (res) => {\n        if (!res.ok) {\n          throw new Error(\"User info fetch failed\");\n        }\n        return res.json();\n      })\n      .then((data) => {\n        setUserName(data.name || data.username || data.email);\n      })\n      .catch((err) => {\n        console.error(\"Failed to fetch user info:\", err);\n        localStorage.removeItem(\"access\");\n        navigate(\"/auth\");\n      });\n\n    // Fetch pending tickets\n    fetchPendingTickets();\n  }, [accessToken, navigate]);\n\n  const fetchPendingTickets = async () => {\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {\n        headers: {\n          Authorization: `Bearer ${accessToken}`,\n        },\n      });\n      const data = await response.json();\n\n      if (response.ok) {\n        setPendingTickets(data.tickets || []);\n      }\n    } catch (err) {\n      console.error(\"Error fetching pending tickets:\", err);\n      setPendingTickets([]);\n    }\n  };\n\n  const handleRaiseNewTicket = () => {\n    navigate(\"/new-ticket\");\n  };\n\n  const handleUsePendingTicket = () => {\n    // Always redirect directly to Home.jsx chatbot\n    // Backend will handle fetching the latest open ticket\n    navigate(\"/?mode=pending\");\n  };\n\n  return (\n    <div style={{ \n      padding: \"40px\", \n      maxWidth: \"800px\", \n      margin: \"0 auto\", \n      textAlign: \"center\",\n      fontFamily: \"Arial, sans-serif\"\n    }}>\n      <h1 style={{ \n        color: \"#333\", \n        marginBottom: \"20px\",\n        fontSize: \"2.5rem\"\n      }}>\n        Welcome, {userName}!\n      </h1>\n      \n      <p style={{ \n        fontSize: \"1.2rem\", \n        color: \"#666\", \n        marginBottom: \"40px\",\n        lineHeight: \"1.6\"\n      }}>\n        How can we help you today? Please choose one of the options below:\n      </p>\n\n      <div style={{ \n        display: \"flex\", \n        flexDirection: \"column\", \n        gap: \"20px\", \n        alignItems: \"center\" \n      }}>\n        \n        {/* Raise New Ticket Button */}\n        <button\n          onClick={handleRaiseNewTicket}\n          disabled={loading}\n          style={{\n            backgroundColor: \"#4CAF50\",\n            color: \"white\",\n            border: \"none\",\n            padding: \"20px 40px\",\n            fontSize: \"1.3rem\",\n            borderRadius: \"8px\",\n            cursor: loading ? \"not-allowed\" : \"pointer\",\n            width: \"300px\",\n            boxShadow: \"0 4px 8px rgba(0,0,0,0.1)\",\n            transition: \"all 0.3s ease\",\n          }}\n          onMouseOver={(e) => {\n            if (!loading) {\n              e.target.style.backgroundColor = \"#45a049\";\n              e.target.style.transform = \"translateY(-2px)\";\n            }\n          }}\n          onMouseOut={(e) => {\n            if (!loading) {\n              e.target.style.backgroundColor = \"#4CAF50\";\n              e.target.style.transform = \"translateY(0)\";\n            }\n          }}\n        >\n          🎫 Raise New Ticket\n        </button>\n\n        {/* Use Pending Ticket Button */}\n        <button\n          onClick={handleUsePendingTicket}\n          style={{\n            backgroundColor: \"#2196F3\",\n            color: \"white\",\n            border: \"none\",\n            padding: \"20px 40px\",\n            fontSize: \"1.3rem\",\n            borderRadius: \"8px\",\n            cursor: \"pointer\",\n            width: \"300px\",\n            boxShadow: \"0 4px 8px rgba(0,0,0,0.1)\",\n            transition: \"all 0.3s ease\",\n          }}\n          onMouseOver={(e) => {\n            e.target.style.backgroundColor = \"#1976D2\";\n            e.target.style.transform = \"translateY(-2px)\";\n          }}\n          onMouseOut={(e) => {\n            e.target.style.backgroundColor = \"#2196F3\";\n            e.target.style.transform = \"translateY(0)\";\n          }}\n        >\n          📋 Use Pending Ticket\n        </button>\n      </div>\n\n\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,MAAMC,WAAW,GAAG,uBAAuB;AAE3C,eAAe,SAASC,WAAWA,CAAC;EAAEC;AAAM,CAAC,EAAE;EAAAC,EAAA;EAC7C,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAMQ,WAAW,GAAGH,KAAK,IAAII,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;EAC3D,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,IAAI,CAACS,WAAW,EAAE;MAChBD,QAAQ,CAAC,OAAO,CAAC;MACjB;IACF;;IAEA;IACAU,KAAK,CAAC,GAAGd,WAAW,iBAAiB,EAAE;MACrCe,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAE,UAAUX,WAAW;MACtC;IACF,CAAC,CAAC,CACCY,IAAI,CAAC,MAAOC,GAAG,IAAK;MACnB,IAAI,CAACA,GAAG,CAACC,EAAE,EAAE;QACX,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MACA,OAAOF,GAAG,CAACG,IAAI,CAAC,CAAC;IACnB,CAAC,CAAC,CACDJ,IAAI,CAAEK,IAAI,IAAK;MACdT,WAAW,CAACS,IAAI,CAACC,IAAI,IAAID,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAACG,KAAK,CAAC;IACvD,CAAC,CAAC,CACDC,KAAK,CAAEC,GAAG,IAAK;MACdC,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEF,GAAG,CAAC;MAChDrB,YAAY,CAACwB,UAAU,CAAC,QAAQ,CAAC;MACjC1B,QAAQ,CAAC,OAAO,CAAC;IACnB,CAAC,CAAC;;IAEJ;IACA2B,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAAC1B,WAAW,EAAED,QAAQ,CAAC,CAAC;EAE3B,MAAM2B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMlB,KAAK,CAAC,GAAGd,WAAW,uBAAuB,EAAE;QAClEe,OAAO,EAAE;UACPC,aAAa,EAAE,UAAUX,WAAW;QACtC;MACF,CAAC,CAAC;MACF,MAAMiB,IAAI,GAAG,MAAMU,QAAQ,CAACX,IAAI,CAAC,CAAC;MAElC,IAAIW,QAAQ,CAACb,EAAE,EAAE;QACfR,iBAAiB,CAACW,IAAI,CAACW,OAAO,IAAI,EAAE,CAAC;MACvC;IACF,CAAC,CAAC,OAAON,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEF,GAAG,CAAC;MACrDhB,iBAAiB,CAAC,EAAE,CAAC;IACvB;EACF,CAAC;EAED,MAAMuB,oBAAoB,GAAGA,CAAA,KAAM;IACjC9B,QAAQ,CAAC,aAAa,CAAC;EACzB,CAAC;EAED,MAAM+B,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACA;IACA/B,QAAQ,CAAC,gBAAgB,CAAC;EAC5B,CAAC;EAED,oBACEL,OAAA;IAAKqC,KAAK,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAE,QAAQ;MACnBC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,gBACA3C,OAAA;MAAIqC,KAAK,EAAE;QACTO,KAAK,EAAE,MAAM;QACbC,YAAY,EAAE,MAAM;QACpBC,QAAQ,EAAE;MACZ,CAAE;MAAAH,QAAA,GAAC,WACQ,EAAC9B,QAAQ,EAAC,GACrB;IAAA;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAELlD,OAAA;MAAGqC,KAAK,EAAE;QACRS,QAAQ,EAAE,QAAQ;QAClBF,KAAK,EAAE,MAAM;QACbC,YAAY,EAAE,MAAM;QACpBM,UAAU,EAAE;MACd,CAAE;MAAAR,QAAA,EAAC;IAEH;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAEJlD,OAAA;MAAKqC,KAAK,EAAE;QACVe,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,GAAG,EAAE,MAAM;QACXC,UAAU,EAAE;MACd,CAAE;MAAAZ,QAAA,gBAGA3C,OAAA;QACEwD,OAAO,EAAErB,oBAAqB;QAC9BsB,QAAQ,EAAEhD,OAAQ;QAClB4B,KAAK,EAAE;UACLqB,eAAe,EAAE,SAAS;UAC1Bd,KAAK,EAAE,OAAO;UACde,MAAM,EAAE,MAAM;UACdrB,OAAO,EAAE,WAAW;UACpBQ,QAAQ,EAAE,QAAQ;UAClBc,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAEpD,OAAO,GAAG,aAAa,GAAG,SAAS;UAC3CqD,KAAK,EAAE,OAAO;UACdC,SAAS,EAAE,2BAA2B;UACtCC,UAAU,EAAE;QACd,CAAE;QACFC,WAAW,EAAGC,CAAC,IAAK;UAClB,IAAI,CAACzD,OAAO,EAAE;YACZyD,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAACqB,eAAe,GAAG,SAAS;YAC1CQ,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAC+B,SAAS,GAAG,kBAAkB;UAC/C;QACF,CAAE;QACFC,UAAU,EAAGH,CAAC,IAAK;UACjB,IAAI,CAACzD,OAAO,EAAE;YACZyD,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAACqB,eAAe,GAAG,SAAS;YAC1CQ,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAC+B,SAAS,GAAG,eAAe;UAC5C;QACF,CAAE;QAAAzB,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAGTlD,OAAA;QACEwD,OAAO,EAAEpB,sBAAuB;QAChCC,KAAK,EAAE;UACLqB,eAAe,EAAE,SAAS;UAC1Bd,KAAK,EAAE,OAAO;UACde,MAAM,EAAE,MAAM;UACdrB,OAAO,EAAE,WAAW;UACpBQ,QAAQ,EAAE,QAAQ;UAClBc,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE,SAAS;UACjBC,KAAK,EAAE,OAAO;UACdC,SAAS,EAAE,2BAA2B;UACtCC,UAAU,EAAE;QACd,CAAE;QACFC,WAAW,EAAGC,CAAC,IAAK;UAClBA,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAACqB,eAAe,GAAG,SAAS;UAC1CQ,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAC+B,SAAS,GAAG,kBAAkB;QAC/C,CAAE;QACFC,UAAU,EAAGH,CAAC,IAAK;UACjBA,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAACqB,eAAe,GAAG,SAAS;UAC1CQ,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAC+B,SAAS,GAAG,eAAe;QAC5C,CAAE;QAAAzB,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGH,CAAC;AAEV;AAAC9C,EAAA,CAlKuBF,WAAW;EAAA,QAChBJ,WAAW;AAAA;AAAAwE,EAAA,GADNpE,WAAW;AAAA,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}