import React, { useState, useEffect } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Link,
  Navigate,
} from "react-router-dom";

import Home from "./Home.jsx";
import Uploads from "./upload.jsx";
import AuthForm from "./AuthForm.jsx";
import AdminPromptPage from "./AdminPromptPage.jsx";
import AdminDashboard from "./AdminDashboard.jsx";
import EscalatedTicketsPage from "./EscalatedTicketsPage.jsx";
import AdminChatbot from "./AdminChatbot.jsx";
import ActionsPage from "./ActionsPage.jsx";
import NewTicketForm from "./NewTicketForm.jsx";
import SelectTicketPage from "./SelectTicketPage.jsx";
import StructuredChatbot from "./StructuredChatbot.jsx";

function App() {
  const [user, setUser] = useState(null);

  useEffect(() => {
    // Clear all authentication data on app startup to force login
    console.log("Clearing authentication data on app startup...");
    localStorage.removeItem("userData");
    localStorage.removeItem("access");
    localStorage.removeItem("refresh");
    setUser(null);
    console.log("Authentication data cleared. User will be redirected to login.");
  }, []);

  const handleLoginSuccess = (userData, tokens) => {
    setUser(userData);
    localStorage.setItem("userData", JSON.stringify(userData));
    localStorage.setItem("access", tokens.access);
    localStorage.setItem("refresh", tokens.refresh);
  };

  const handleLogout = () => {
    setUser(null);
    localStorage.removeItem("userData");
    localStorage.removeItem("access");
    localStorage.removeItem("refresh");
  };

  const PrivateRoute = ({ children }) => (user ? children : <Navigate to="/auth" replace />);
  const AdminRoute = ({ children }) =>
    user && user.is_admin ? children : <Navigate to="/" replace />;

  return (
    <Router>
      {window.location.pathname !== "/admin" && (
        <nav style={{ padding: "10px", borderBottom: "1px solid #ccc", display: "flex", justifyContent: "space-between" }}>
          <div>
            {user && (
              <>
                <Link to={user.is_admin ? "/admin" : "/"} style={{ marginRight: 15 }}>
                  {user.is_admin ? "Admin Panel" : "Chatbot"}
                </Link>
                <Link to="/uploads" style={{ marginRight: 15 }}>
                  Uploads
                </Link>
                {user.is_admin && (
                  <Link to="/prompt-manager" style={{ marginRight: 15 }}>
                    Prompt Admin
                  </Link>
                )}
              </>
            )}
          </div>

          <div>
            {user ? (
              <button
                onClick={handleLogout}
                style={{
                  cursor: "pointer",
                  backgroundColor: "#f44336",
                  color: "white",
                  border: "none",
                  padding: "6px 12px",
                  borderRadius: "4px",
                }}
              >
                Logout
              </button>
            ) : (
              <>
                <Link to="/auth" style={{ marginRight: 12 }}>
                  Login
                </Link>
                <Link to="/signup">Sign Up
                </Link>
              </>
            )}
          </div>
        </nav>
      )}

      <Routes>
        <Route
          path="/"
          element={
            user ? (user.is_admin ? <Navigate to="/admin" replace /> : <Navigate to="/actions" replace />) : <Navigate to="/auth" replace />
          }
        />

        <Route
          path="/actions"
          element={
            <PrivateRoute>
              <ActionsPage token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/new-ticket"
          element={
            <PrivateRoute>
              <NewTicketForm token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/select-ticket"
          element={
            <PrivateRoute>
              <SelectTicketPage token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/chatbot/:ticketId"
          element={
            <PrivateRoute>
              <StructuredChatbot token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/legacy-chat"
          element={
            <PrivateRoute>
              <Home token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/uploads"
          element={
            <PrivateRoute>
              <Uploads />
            </PrivateRoute>
          }
        />

        <Route
          path="/prompt-manager"
          element={
            <AdminRoute>
              <AdminPromptPage />
            </AdminRoute>
          }
        />

        <Route
          path="/admin"
          element={
            <AdminRoute>
              <AdminDashboard />
            </AdminRoute>
          }
        />

        <Route
          path="/admin/tickets"
          element={
            <AdminRoute>
              <EscalatedTicketsPage />
            </AdminRoute>
          }
        />

        <Route
          path="/auth"
          element={<AuthForm onLoginSuccess={handleLoginSuccess} />}
        />
        <Route
          path="/admin/chatbot"
          element={
            <AdminRoute>
              <AdminChatbot />
            </AdminRoute>
          }
        />

        <Route
          path="/signup"
          element={<AuthForm defaultMode="signup" onLoginSuccess={handleLoginSuccess} />}
        />
      </Routes>
    </Router>
  );
}

export default App;