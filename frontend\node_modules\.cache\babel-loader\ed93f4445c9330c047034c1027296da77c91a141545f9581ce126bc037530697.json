{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AI-Agent-Chatbot-main\\\\frontend\\\\src\\\\PendingTicketsList.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport \"./App.css\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BACKEND_URL = \"http://localhost:8000\";\nexport default function PendingTicketsList({\n  token\n}) {\n  _s();\n  const navigate = useNavigate();\n  const accessToken = token || localStorage.getItem(\"access\");\n  const [tickets, setTickets] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [userName, setUserName] = useState(\"\");\n\n  // Redirect to auth if no token\n  useEffect(() => {\n    if (!accessToken) {\n      navigate(\"/auth\");\n      return;\n    }\n  }, [accessToken, navigate]);\n\n  // Fetch user info and pending tickets\n  useEffect(() => {\n    if (!accessToken) return;\n    const fetchData = async () => {\n      try {\n        // Get user info\n        const userResponse = await fetch(`${BACKEND_URL}/api/user_info/`, {\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${accessToken}`\n          }\n        });\n        if (userResponse.ok) {\n          const userData = await userResponse.json();\n          setUserName(userData.name || userData.username || userData.email);\n        }\n\n        // Get pending tickets\n        const ticketsResponse = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${accessToken}`\n          }\n        });\n        if (ticketsResponse.ok) {\n          const ticketsData = await ticketsResponse.json();\n          setTickets(ticketsData.tickets || []);\n        } else {\n          setError(\"Failed to fetch pending tickets\");\n        }\n      } catch (err) {\n        setError(\"Network error: \" + err.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [accessToken]);\n  const handleTicketSelect = ticketNumber => {\n    navigate(`/legacy-chat?ticket=${ticketNumber}&mode=pending`);\n  };\n  const handleBackToActions = () => {\n    navigate(\"/actions\");\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        height: \"100vh\",\n        backgroundColor: \"#f5f5f5\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: \"18px\",\n            color: \"#666\",\n            marginBottom: \"20px\"\n          },\n          children: \"Loading pending tickets...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: \"40px\",\n            height: \"40px\",\n            border: \"4px solid #f3f3f3\",\n            borderTop: \"4px solid #3498db\",\n            borderRadius: \"50%\",\n            animation: \"spin 1s linear infinite\",\n            margin: \"0 auto\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: \"100vh\",\n      backgroundColor: \"#f5f5f5\",\n      padding: \"20px\"\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        maxWidth: \"1000px\",\n        margin: \"0 auto\",\n        backgroundColor: \"white\",\n        borderRadius: \"12px\",\n        boxShadow: \"0 4px 6px rgba(0, 0, 0, 0.1)\",\n        overflow: \"hidden\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n          color: \"white\",\n          padding: \"30px\",\n          textAlign: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: \"0 0 10px 0\",\n            fontSize: \"2.5rem\",\n            fontWeight: \"bold\"\n          },\n          children: \"\\uD83D\\uDCCB Pending Tickets\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: \"0\",\n            fontSize: \"1.1rem\",\n            opacity: \"0.9\"\n          },\n          children: [\"Welcome back, \", userName, \"! Select a ticket to continue.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: \"30px\"\n        },\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: \"#fee\",\n            color: \"#c33\",\n            padding: \"15px\",\n            borderRadius: \"8px\",\n            marginBottom: \"20px\",\n            border: \"1px solid #fcc\"\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 13\n        }, this), tickets.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: \"center\",\n            padding: \"60px 20px\",\n            color: \"#666\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: \"4rem\",\n              marginBottom: \"20px\"\n            },\n            children: \"\\uD83D\\uDCDD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              marginBottom: \"10px\",\n              color: \"#333\"\n            },\n            children: \"No Pending Tickets\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              marginBottom: \"30px\"\n            },\n            children: \"You don't have any open tickets at the moment.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate(\"/new-ticket\"),\n            style: {\n              backgroundColor: \"#4CAF50\",\n              color: \"white\",\n              border: \"none\",\n              padding: \"12px 24px\",\n              borderRadius: \"6px\",\n              fontSize: \"16px\",\n              cursor: \"pointer\",\n              fontWeight: \"bold\"\n            },\n            children: \"Create New Ticket\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginBottom: \"20px\",\n              color: \"#333\",\n              fontSize: \"1.5rem\"\n            },\n            children: [\"Your Open Tickets (\", tickets.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              maxHeight: \"500px\",\n              overflowY: \"auto\",\n              border: \"1px solid #e0e0e0\",\n              borderRadius: \"8px\",\n              backgroundColor: \"#fafafa\"\n            },\n            children: tickets.map((ticket, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: () => handleTicketSelect(ticket.ticket_number),\n              style: {\n                padding: \"20px\",\n                borderBottom: index < tickets.length - 1 ? \"1px solid #e0e0e0\" : \"none\",\n                cursor: \"pointer\",\n                backgroundColor: \"white\",\n                transition: \"all 0.3s ease\",\n                margin: \"8px\",\n                borderRadius: \"6px\",\n                boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n              },\n              onMouseOver: e => {\n                e.target.style.backgroundColor = \"#f8f9fa\";\n                e.target.style.transform = \"translateY(-2px)\";\n                e.target.style.boxShadow = \"0 4px 8px rgba(0,0,0,0.15)\";\n              },\n              onMouseOut: e => {\n                e.target.style.backgroundColor = \"white\";\n                e.target.style.transform = \"translateY(0)\";\n                e.target.style.boxShadow = \"0 2px 4px rgba(0,0,0,0.1)\";\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"flex-start\",\n                  marginBottom: \"10px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: \"bold\",\n                    fontSize: \"1.1rem\",\n                    color: \"#2c3e50\"\n                  },\n                  children: [\"\\uD83C\\uDFAB \", ticket.ticket_number]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: \"0.9rem\",\n                    color: \"#666\",\n                    backgroundColor: \"#e8f5e8\",\n                    padding: \"4px 8px\",\n                    borderRadius: \"12px\",\n                    fontWeight: \"bold\"\n                  },\n                  children: ticket.status.toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: \"1rem\",\n                  fontWeight: \"600\",\n                  color: \"#34495e\",\n                  marginBottom: \"8px\"\n                },\n                children: ticket.title || ticket.short_title || \"No title\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: \"0.9rem\",\n                  color: \"#666\",\n                  marginBottom: \"10px\",\n                  lineHeight: \"1.4\"\n                },\n                children: ticket.problem_description ? ticket.problem_description.length > 150 ? ticket.problem_description.substring(0, 150) + \"...\" : ticket.problem_description : \"No description available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  fontSize: \"0.8rem\",\n                  color: \"#888\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\uD83D\\uDCE6 \", ticket.product_name, \" - \", ticket.model]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\uD83D\\uDD52 \", formatDate(ticket.last_activity)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 21\n              }, this)]\n            }, ticket.ticket_number, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: \"center\",\n            marginTop: \"30px\",\n            paddingTop: \"20px\",\n            borderTop: \"1px solid #e0e0e0\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleBackToActions,\n            style: {\n              backgroundColor: \"#6c757d\",\n              color: \"white\",\n              border: \"none\",\n              padding: \"12px 24px\",\n              borderRadius: \"6px\",\n              fontSize: \"16px\",\n              cursor: \"pointer\",\n              fontWeight: \"bold\",\n              marginRight: \"15px\"\n            },\n            onMouseOver: e => e.target.style.backgroundColor = \"#5a6268\",\n            onMouseOut: e => e.target.style.backgroundColor = \"#6c757d\",\n            children: \"\\u2190 Back to Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate(\"/new-ticket\"),\n            style: {\n              backgroundColor: \"#28a745\",\n              color: \"white\",\n              border: \"none\",\n              padding: \"12px 24px\",\n              borderRadius: \"6px\",\n              fontSize: \"16px\",\n              cursor: \"pointer\",\n              fontWeight: \"bold\"\n            },\n            onMouseOver: e => e.target.style.backgroundColor = \"#218838\",\n            onMouseOut: e => e.target.style.backgroundColor = \"#28a745\",\n            children: \"+ Create New Ticket\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 5\n  }, this);\n}\n_s(PendingTicketsList, \"bb9ynRUR6bHKhQYRGnpTkzq0Fto=\", false, function () {\n  return [useNavigate];\n});\n_c = PendingTicketsList;\nvar _c;\n$RefreshReg$(_c, \"PendingTicketsList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BACKEND_URL", "PendingTicketsList", "token", "_s", "navigate", "accessToken", "localStorage", "getItem", "tickets", "setTickets", "loading", "setLoading", "error", "setError", "userName", "setUserName", "fetchData", "userResponse", "fetch", "headers", "Authorization", "ok", "userData", "json", "name", "username", "email", "ticketsResponse", "ticketsData", "err", "message", "handleTicketSelect", "ticketNumber", "handleBackToActions", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "style", "display", "justifyContent", "alignItems", "height", "backgroundColor", "children", "textAlign", "fontSize", "color", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "border", "borderTop", "borderRadius", "animation", "margin", "minHeight", "padding", "max<PERSON><PERSON><PERSON>", "boxShadow", "overflow", "background", "fontWeight", "opacity", "length", "onClick", "cursor", "maxHeight", "overflowY", "map", "ticket", "index", "ticket_number", "borderBottom", "transition", "onMouseOver", "e", "target", "transform", "onMouseOut", "status", "toUpperCase", "title", "short_title", "lineHeight", "problem_description", "substring", "product_name", "model", "last_activity", "marginTop", "paddingTop", "marginRight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/AI-Agent-Chatbot-main/frontend/src/PendingTicketsList.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport \"./App.css\";\n\nconst BACKEND_URL = \"http://localhost:8000\";\n\nexport default function PendingTicketsList({ token }) {\n  const navigate = useNavigate();\n  const accessToken = token || localStorage.getItem(\"access\");\n  const [tickets, setTickets] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [userName, setUserName] = useState(\"\");\n\n  // Redirect to auth if no token\n  useEffect(() => {\n    if (!accessToken) {\n      navigate(\"/auth\");\n      return;\n    }\n  }, [accessToken, navigate]);\n\n  // Fetch user info and pending tickets\n  useEffect(() => {\n    if (!accessToken) return;\n\n    const fetchData = async () => {\n      try {\n        // Get user info\n        const userResponse = await fetch(`${BACKEND_URL}/api/user_info/`, {\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${accessToken}`,\n          },\n        });\n\n        if (userResponse.ok) {\n          const userData = await userResponse.json();\n          setUserName(userData.name || userData.username || userData.email);\n        }\n\n        // Get pending tickets\n        const ticketsResponse = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${accessToken}`,\n          },\n        });\n\n        if (ticketsResponse.ok) {\n          const ticketsData = await ticketsResponse.json();\n          setTickets(ticketsData.tickets || []);\n        } else {\n          setError(\"Failed to fetch pending tickets\");\n        }\n      } catch (err) {\n        setError(\"Network error: \" + err.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [accessToken]);\n\n  const handleTicketSelect = (ticketNumber) => {\n    navigate(`/legacy-chat?ticket=${ticketNumber}&mode=pending`);\n  };\n\n  const handleBackToActions = () => {\n    navigate(\"/actions\");\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  if (loading) {\n    return (\n      <div style={{ \n        display: \"flex\", \n        justifyContent: \"center\", \n        alignItems: \"center\", \n        height: \"100vh\",\n        backgroundColor: \"#f5f5f5\"\n      }}>\n        <div style={{ textAlign: \"center\" }}>\n          <div style={{ \n            fontSize: \"18px\", \n            color: \"#666\",\n            marginBottom: \"20px\"\n          }}>\n            Loading pending tickets...\n          </div>\n          <div style={{\n            width: \"40px\",\n            height: \"40px\",\n            border: \"4px solid #f3f3f3\",\n            borderTop: \"4px solid #3498db\",\n            borderRadius: \"50%\",\n            animation: \"spin 1s linear infinite\",\n            margin: \"0 auto\"\n          }}></div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{\n      minHeight: \"100vh\",\n      backgroundColor: \"#f5f5f5\",\n      padding: \"20px\"\n    }}>\n      <div style={{\n        maxWidth: \"1000px\",\n        margin: \"0 auto\",\n        backgroundColor: \"white\",\n        borderRadius: \"12px\",\n        boxShadow: \"0 4px 6px rgba(0, 0, 0, 0.1)\",\n        overflow: \"hidden\"\n      }}>\n        {/* Header */}\n        <div style={{\n          background: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n          color: \"white\",\n          padding: \"30px\",\n          textAlign: \"center\"\n        }}>\n          <h1 style={{ \n            margin: \"0 0 10px 0\", \n            fontSize: \"2.5rem\",\n            fontWeight: \"bold\"\n          }}>\n            📋 Pending Tickets\n          </h1>\n          <p style={{ \n            margin: \"0\", \n            fontSize: \"1.1rem\",\n            opacity: \"0.9\"\n          }}>\n            Welcome back, {userName}! Select a ticket to continue.\n          </p>\n        </div>\n\n        {/* Content */}\n        <div style={{ padding: \"30px\" }}>\n          {error && (\n            <div style={{\n              backgroundColor: \"#fee\",\n              color: \"#c33\",\n              padding: \"15px\",\n              borderRadius: \"8px\",\n              marginBottom: \"20px\",\n              border: \"1px solid #fcc\"\n            }}>\n              {error}\n            </div>\n          )}\n\n          {tickets.length === 0 ? (\n            <div style={{\n              textAlign: \"center\",\n              padding: \"60px 20px\",\n              color: \"#666\"\n            }}>\n              <div style={{ fontSize: \"4rem\", marginBottom: \"20px\" }}>📝</div>\n              <h3 style={{ marginBottom: \"10px\", color: \"#333\" }}>No Pending Tickets</h3>\n              <p style={{ marginBottom: \"30px\" }}>You don't have any open tickets at the moment.</p>\n              <button\n                onClick={() => navigate(\"/new-ticket\")}\n                style={{\n                  backgroundColor: \"#4CAF50\",\n                  color: \"white\",\n                  border: \"none\",\n                  padding: \"12px 24px\",\n                  borderRadius: \"6px\",\n                  fontSize: \"16px\",\n                  cursor: \"pointer\",\n                  fontWeight: \"bold\"\n                }}\n              >\n                Create New Ticket\n              </button>\n            </div>\n          ) : (\n            <>\n              <h2 style={{ \n                marginBottom: \"20px\", \n                color: \"#333\",\n                fontSize: \"1.5rem\"\n              }}>\n                Your Open Tickets ({tickets.length})\n              </h2>\n              \n              {/* Scrollable tickets list */}\n              <div style={{\n                maxHeight: \"500px\",\n                overflowY: \"auto\",\n                border: \"1px solid #e0e0e0\",\n                borderRadius: \"8px\",\n                backgroundColor: \"#fafafa\"\n              }}>\n                {tickets.map((ticket, index) => (\n                  <div\n                    key={ticket.ticket_number}\n                    onClick={() => handleTicketSelect(ticket.ticket_number)}\n                    style={{\n                      padding: \"20px\",\n                      borderBottom: index < tickets.length - 1 ? \"1px solid #e0e0e0\" : \"none\",\n                      cursor: \"pointer\",\n                      backgroundColor: \"white\",\n                      transition: \"all 0.3s ease\",\n                      margin: \"8px\",\n                      borderRadius: \"6px\",\n                      boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                    }}\n                    onMouseOver={(e) => {\n                      e.target.style.backgroundColor = \"#f8f9fa\";\n                      e.target.style.transform = \"translateY(-2px)\";\n                      e.target.style.boxShadow = \"0 4px 8px rgba(0,0,0,0.15)\";\n                    }}\n                    onMouseOut={(e) => {\n                      e.target.style.backgroundColor = \"white\";\n                      e.target.style.transform = \"translateY(0)\";\n                      e.target.style.boxShadow = \"0 2px 4px rgba(0,0,0,0.1)\";\n                    }}\n                  >\n                    <div style={{\n                      display: \"flex\",\n                      justifyContent: \"space-between\",\n                      alignItems: \"flex-start\",\n                      marginBottom: \"10px\"\n                    }}>\n                      <div style={{\n                        fontWeight: \"bold\",\n                        fontSize: \"1.1rem\",\n                        color: \"#2c3e50\"\n                      }}>\n                        🎫 {ticket.ticket_number}\n                      </div>\n                      <div style={{\n                        fontSize: \"0.9rem\",\n                        color: \"#666\",\n                        backgroundColor: \"#e8f5e8\",\n                        padding: \"4px 8px\",\n                        borderRadius: \"12px\",\n                        fontWeight: \"bold\"\n                      }}>\n                        {ticket.status.toUpperCase()}\n                      </div>\n                    </div>\n                    \n                    <div style={{\n                      fontSize: \"1rem\",\n                      fontWeight: \"600\",\n                      color: \"#34495e\",\n                      marginBottom: \"8px\"\n                    }}>\n                      {ticket.title || ticket.short_title || \"No title\"}\n                    </div>\n                    \n                    <div style={{\n                      fontSize: \"0.9rem\",\n                      color: \"#666\",\n                      marginBottom: \"10px\",\n                      lineHeight: \"1.4\"\n                    }}>\n                      {ticket.problem_description ? \n                        (ticket.problem_description.length > 150 ? \n                          ticket.problem_description.substring(0, 150) + \"...\" : \n                          ticket.problem_description\n                        ) : \n                        \"No description available\"\n                      }\n                    </div>\n                    \n                    <div style={{\n                      display: \"flex\",\n                      justifyContent: \"space-between\",\n                      alignItems: \"center\",\n                      fontSize: \"0.8rem\",\n                      color: \"#888\"\n                    }}>\n                      <span>\n                        📦 {ticket.product_name} - {ticket.model}\n                      </span>\n                      <span>\n                        🕒 {formatDate(ticket.last_activity)}\n                      </span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </>\n          )}\n\n          {/* Back button */}\n          <div style={{ \n            textAlign: \"center\", \n            marginTop: \"30px\",\n            paddingTop: \"20px\",\n            borderTop: \"1px solid #e0e0e0\"\n          }}>\n            <button\n              onClick={handleBackToActions}\n              style={{\n                backgroundColor: \"#6c757d\",\n                color: \"white\",\n                border: \"none\",\n                padding: \"12px 24px\",\n                borderRadius: \"6px\",\n                fontSize: \"16px\",\n                cursor: \"pointer\",\n                fontWeight: \"bold\",\n                marginRight: \"15px\"\n              }}\n              onMouseOver={(e) => e.target.style.backgroundColor = \"#5a6268\"}\n              onMouseOut={(e) => e.target.style.backgroundColor = \"#6c757d\"}\n            >\n              ← Back to Actions\n            </button>\n            \n            <button\n              onClick={() => navigate(\"/new-ticket\")}\n              style={{\n                backgroundColor: \"#28a745\",\n                color: \"white\",\n                border: \"none\",\n                padding: \"12px 24px\",\n                borderRadius: \"6px\",\n                fontSize: \"16px\",\n                cursor: \"pointer\",\n                fontWeight: \"bold\"\n              }}\n              onMouseOver={(e) => e.target.style.backgroundColor = \"#218838\"}\n              onMouseOut={(e) => e.target.style.backgroundColor = \"#28a745\"}\n            >\n              + Create New Ticket\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnB,MAAMC,WAAW,GAAG,uBAAuB;AAE3C,eAAe,SAASC,kBAAkBA,CAAC;EAAEC;AAAM,CAAC,EAAE;EAAAC,EAAA;EACpD,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAMU,WAAW,GAAGH,KAAK,IAAII,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;EAC3D,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,IAAI,CAACW,WAAW,EAAE;MAChBD,QAAQ,CAAC,OAAO,CAAC;MACjB;IACF;EACF,CAAC,EAAE,CAACC,WAAW,EAAED,QAAQ,CAAC,CAAC;;EAE3B;EACAV,SAAS,CAAC,MAAM;IACd,IAAI,CAACW,WAAW,EAAE;IAElB,MAAMW,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF;QACA,MAAMC,YAAY,GAAG,MAAMC,KAAK,CAAC,GAAGlB,WAAW,iBAAiB,EAAE;UAChEmB,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClCC,aAAa,EAAE,UAAUf,WAAW;UACtC;QACF,CAAC,CAAC;QAEF,IAAIY,YAAY,CAACI,EAAE,EAAE;UACnB,MAAMC,QAAQ,GAAG,MAAML,YAAY,CAACM,IAAI,CAAC,CAAC;UAC1CR,WAAW,CAACO,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACG,QAAQ,IAAIH,QAAQ,CAACI,KAAK,CAAC;QACnE;;QAEA;QACA,MAAMC,eAAe,GAAG,MAAMT,KAAK,CAAC,GAAGlB,WAAW,uBAAuB,EAAE;UACzEmB,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClCC,aAAa,EAAE,UAAUf,WAAW;UACtC;QACF,CAAC,CAAC;QAEF,IAAIsB,eAAe,CAACN,EAAE,EAAE;UACtB,MAAMO,WAAW,GAAG,MAAMD,eAAe,CAACJ,IAAI,CAAC,CAAC;UAChDd,UAAU,CAACmB,WAAW,CAACpB,OAAO,IAAI,EAAE,CAAC;QACvC,CAAC,MAAM;UACLK,QAAQ,CAAC,iCAAiC,CAAC;QAC7C;MACF,CAAC,CAAC,OAAOgB,GAAG,EAAE;QACZhB,QAAQ,CAAC,iBAAiB,GAAGgB,GAAG,CAACC,OAAO,CAAC;MAC3C,CAAC,SAAS;QACRnB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDK,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACX,WAAW,CAAC,CAAC;EAEjB,MAAM0B,kBAAkB,GAAIC,YAAY,IAAK;IAC3C5B,QAAQ,CAAC,uBAAuB4B,YAAY,eAAe,CAAC;EAC9D,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC7B,QAAQ,CAAC,UAAU,CAAC;EACtB,CAAC;EAED,MAAM8B,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,IAAIhC,OAAO,EAAE;IACX,oBACEb,OAAA;MAAK8C,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,OAAO;QACfC,eAAe,EAAE;MACnB,CAAE;MAAAC,QAAA,eACApD,OAAA;QAAK8C,KAAK,EAAE;UAAEO,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCpD,OAAA;UAAK8C,KAAK,EAAE;YACVQ,QAAQ,EAAE,MAAM;YAChBC,KAAK,EAAE,MAAM;YACbC,YAAY,EAAE;UAChB,CAAE;UAAAJ,QAAA,EAAC;QAEH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN5D,OAAA;UAAK8C,KAAK,EAAE;YACVe,KAAK,EAAE,MAAM;YACbX,MAAM,EAAE,MAAM;YACdY,MAAM,EAAE,mBAAmB;YAC3BC,SAAS,EAAE,mBAAmB;YAC9BC,YAAY,EAAE,KAAK;YACnBC,SAAS,EAAE,yBAAyB;YACpCC,MAAM,EAAE;UACV;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE5D,OAAA;IAAK8C,KAAK,EAAE;MACVqB,SAAS,EAAE,OAAO;MAClBhB,eAAe,EAAE,SAAS;MAC1BiB,OAAO,EAAE;IACX,CAAE;IAAAhB,QAAA,eACApD,OAAA;MAAK8C,KAAK,EAAE;QACVuB,QAAQ,EAAE,QAAQ;QAClBH,MAAM,EAAE,QAAQ;QAChBf,eAAe,EAAE,OAAO;QACxBa,YAAY,EAAE,MAAM;QACpBM,SAAS,EAAE,8BAA8B;QACzCC,QAAQ,EAAE;MACZ,CAAE;MAAAnB,QAAA,gBAEApD,OAAA;QAAK8C,KAAK,EAAE;UACV0B,UAAU,EAAE,mDAAmD;UAC/DjB,KAAK,EAAE,OAAO;UACda,OAAO,EAAE,MAAM;UACff,SAAS,EAAE;QACb,CAAE;QAAAD,QAAA,gBACApD,OAAA;UAAI8C,KAAK,EAAE;YACToB,MAAM,EAAE,YAAY;YACpBZ,QAAQ,EAAE,QAAQ;YAClBmB,UAAU,EAAE;UACd,CAAE;UAAArB,QAAA,EAAC;QAEH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL5D,OAAA;UAAG8C,KAAK,EAAE;YACRoB,MAAM,EAAE,GAAG;YACXZ,QAAQ,EAAE,QAAQ;YAClBoB,OAAO,EAAE;UACX,CAAE;UAAAtB,QAAA,GAAC,gBACa,EAACnC,QAAQ,EAAC,gCAC1B;QAAA;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN5D,OAAA;QAAK8C,KAAK,EAAE;UAAEsB,OAAO,EAAE;QAAO,CAAE;QAAAhB,QAAA,GAC7BrC,KAAK,iBACJf,OAAA;UAAK8C,KAAK,EAAE;YACVK,eAAe,EAAE,MAAM;YACvBI,KAAK,EAAE,MAAM;YACba,OAAO,EAAE,MAAM;YACfJ,YAAY,EAAE,KAAK;YACnBR,YAAY,EAAE,MAAM;YACpBM,MAAM,EAAE;UACV,CAAE;UAAAV,QAAA,EACCrC;QAAK;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAjD,OAAO,CAACgE,MAAM,KAAK,CAAC,gBACnB3E,OAAA;UAAK8C,KAAK,EAAE;YACVO,SAAS,EAAE,QAAQ;YACnBe,OAAO,EAAE,WAAW;YACpBb,KAAK,EAAE;UACT,CAAE;UAAAH,QAAA,gBACApD,OAAA;YAAK8C,KAAK,EAAE;cAAEQ,QAAQ,EAAE,MAAM;cAAEE,YAAY,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChE5D,OAAA;YAAI8C,KAAK,EAAE;cAAEU,YAAY,EAAE,MAAM;cAAED,KAAK,EAAE;YAAO,CAAE;YAAAH,QAAA,EAAC;UAAkB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3E5D,OAAA;YAAG8C,KAAK,EAAE;cAAEU,YAAY,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAA8C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtF5D,OAAA;YACE4E,OAAO,EAAEA,CAAA,KAAMrE,QAAQ,CAAC,aAAa,CAAE;YACvCuC,KAAK,EAAE;cACLK,eAAe,EAAE,SAAS;cAC1BI,KAAK,EAAE,OAAO;cACdO,MAAM,EAAE,MAAM;cACdM,OAAO,EAAE,WAAW;cACpBJ,YAAY,EAAE,KAAK;cACnBV,QAAQ,EAAE,MAAM;cAChBuB,MAAM,EAAE,SAAS;cACjBJ,UAAU,EAAE;YACd,CAAE;YAAArB,QAAA,EACH;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAEN5D,OAAA,CAAAE,SAAA;UAAAkD,QAAA,gBACEpD,OAAA;YAAI8C,KAAK,EAAE;cACTU,YAAY,EAAE,MAAM;cACpBD,KAAK,EAAE,MAAM;cACbD,QAAQ,EAAE;YACZ,CAAE;YAAAF,QAAA,GAAC,qBACkB,EAACzC,OAAO,CAACgE,MAAM,EAAC,GACrC;UAAA;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGL5D,OAAA;YAAK8C,KAAK,EAAE;cACVgC,SAAS,EAAE,OAAO;cAClBC,SAAS,EAAE,MAAM;cACjBjB,MAAM,EAAE,mBAAmB;cAC3BE,YAAY,EAAE,KAAK;cACnBb,eAAe,EAAE;YACnB,CAAE;YAAAC,QAAA,EACCzC,OAAO,CAACqE,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzBlF,OAAA;cAEE4E,OAAO,EAAEA,CAAA,KAAM1C,kBAAkB,CAAC+C,MAAM,CAACE,aAAa,CAAE;cACxDrC,KAAK,EAAE;gBACLsB,OAAO,EAAE,MAAM;gBACfgB,YAAY,EAAEF,KAAK,GAAGvE,OAAO,CAACgE,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;gBACvEE,MAAM,EAAE,SAAS;gBACjB1B,eAAe,EAAE,OAAO;gBACxBkC,UAAU,EAAE,eAAe;gBAC3BnB,MAAM,EAAE,KAAK;gBACbF,YAAY,EAAE,KAAK;gBACnBM,SAAS,EAAE;cACb,CAAE;cACFgB,WAAW,EAAGC,CAAC,IAAK;gBAClBA,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAACK,eAAe,GAAG,SAAS;gBAC1CoC,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAAC2C,SAAS,GAAG,kBAAkB;gBAC7CF,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAACwB,SAAS,GAAG,4BAA4B;cACzD,CAAE;cACFoB,UAAU,EAAGH,CAAC,IAAK;gBACjBA,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAACK,eAAe,GAAG,OAAO;gBACxCoC,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAAC2C,SAAS,GAAG,eAAe;gBAC1CF,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAACwB,SAAS,GAAG,2BAA2B;cACxD,CAAE;cAAAlB,QAAA,gBAEFpD,OAAA;gBAAK8C,KAAK,EAAE;kBACVC,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,eAAe;kBAC/BC,UAAU,EAAE,YAAY;kBACxBO,YAAY,EAAE;gBAChB,CAAE;gBAAAJ,QAAA,gBACApD,OAAA;kBAAK8C,KAAK,EAAE;oBACV2B,UAAU,EAAE,MAAM;oBAClBnB,QAAQ,EAAE,QAAQ;oBAClBC,KAAK,EAAE;kBACT,CAAE;kBAAAH,QAAA,GAAC,eACE,EAAC6B,MAAM,CAACE,aAAa;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACN5D,OAAA;kBAAK8C,KAAK,EAAE;oBACVQ,QAAQ,EAAE,QAAQ;oBAClBC,KAAK,EAAE,MAAM;oBACbJ,eAAe,EAAE,SAAS;oBAC1BiB,OAAO,EAAE,SAAS;oBAClBJ,YAAY,EAAE,MAAM;oBACpBS,UAAU,EAAE;kBACd,CAAE;kBAAArB,QAAA,EACC6B,MAAM,CAACU,MAAM,CAACC,WAAW,CAAC;gBAAC;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN5D,OAAA;gBAAK8C,KAAK,EAAE;kBACVQ,QAAQ,EAAE,MAAM;kBAChBmB,UAAU,EAAE,KAAK;kBACjBlB,KAAK,EAAE,SAAS;kBAChBC,YAAY,EAAE;gBAChB,CAAE;gBAAAJ,QAAA,EACC6B,MAAM,CAACY,KAAK,IAAIZ,MAAM,CAACa,WAAW,IAAI;cAAU;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eAEN5D,OAAA;gBAAK8C,KAAK,EAAE;kBACVQ,QAAQ,EAAE,QAAQ;kBAClBC,KAAK,EAAE,MAAM;kBACbC,YAAY,EAAE,MAAM;kBACpBuC,UAAU,EAAE;gBACd,CAAE;gBAAA3C,QAAA,EACC6B,MAAM,CAACe,mBAAmB,GACxBf,MAAM,CAACe,mBAAmB,CAACrB,MAAM,GAAG,GAAG,GACtCM,MAAM,CAACe,mBAAmB,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,GACpDhB,MAAM,CAACe,mBAAmB,GAE5B;cAA0B;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEzB,CAAC,eAEN5D,OAAA;gBAAK8C,KAAK,EAAE;kBACVC,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,eAAe;kBAC/BC,UAAU,EAAE,QAAQ;kBACpBK,QAAQ,EAAE,QAAQ;kBAClBC,KAAK,EAAE;gBACT,CAAE;gBAAAH,QAAA,gBACApD,OAAA;kBAAAoD,QAAA,GAAM,eACD,EAAC6B,MAAM,CAACiB,YAAY,EAAC,KAAG,EAACjB,MAAM,CAACkB,KAAK;gBAAA;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eACP5D,OAAA;kBAAAoD,QAAA,GAAM,eACD,EAACf,UAAU,CAAC4C,MAAM,CAACmB,aAAa,CAAC;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GArFDqB,MAAM,CAACE,aAAa;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsFtB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA,eACN,CACH,eAGD5D,OAAA;UAAK8C,KAAK,EAAE;YACVO,SAAS,EAAE,QAAQ;YACnBgD,SAAS,EAAE,MAAM;YACjBC,UAAU,EAAE,MAAM;YAClBvC,SAAS,EAAE;UACb,CAAE;UAAAX,QAAA,gBACApD,OAAA;YACE4E,OAAO,EAAExC,mBAAoB;YAC7BU,KAAK,EAAE;cACLK,eAAe,EAAE,SAAS;cAC1BI,KAAK,EAAE,OAAO;cACdO,MAAM,EAAE,MAAM;cACdM,OAAO,EAAE,WAAW;cACpBJ,YAAY,EAAE,KAAK;cACnBV,QAAQ,EAAE,MAAM;cAChBuB,MAAM,EAAE,SAAS;cACjBJ,UAAU,EAAE,MAAM;cAClB8B,WAAW,EAAE;YACf,CAAE;YACFjB,WAAW,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAACK,eAAe,GAAG,SAAU;YAC/DuC,UAAU,EAAGH,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAACK,eAAe,GAAG,SAAU;YAAAC,QAAA,EAC/D;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET5D,OAAA;YACE4E,OAAO,EAAEA,CAAA,KAAMrE,QAAQ,CAAC,aAAa,CAAE;YACvCuC,KAAK,EAAE;cACLK,eAAe,EAAE,SAAS;cAC1BI,KAAK,EAAE,OAAO;cACdO,MAAM,EAAE,MAAM;cACdM,OAAO,EAAE,WAAW;cACpBJ,YAAY,EAAE,KAAK;cACnBV,QAAQ,EAAE,MAAM;cAChBuB,MAAM,EAAE,SAAS;cACjBJ,UAAU,EAAE;YACd,CAAE;YACFa,WAAW,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAACK,eAAe,GAAG,SAAU;YAC/DuC,UAAU,EAAGH,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAACK,eAAe,GAAG,SAAU;YAAAC,QAAA,EAC/D;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACtD,EAAA,CAzVuBF,kBAAkB;EAAA,QACvBN,WAAW;AAAA;AAAA0G,EAAA,GADNpG,kBAAkB;AAAA,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}