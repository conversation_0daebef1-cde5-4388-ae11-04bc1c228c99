import React from "react";

export function YesNoButtons({ onYes, onNo, yesText = "Yes", noText = "No", disabled = false }) {
  return (
    <div style={{ 
      display: "flex", 
      gap: "15px", 
      justifyContent: "center",
      margin: "15px 0"
    }}>
      <button
        onClick={onYes}
        disabled={disabled}
        style={{
          backgroundColor: disabled ? "#ccc" : "#4CAF50",
          color: "white",
          border: "none",
          padding: "12px 24px",
          borderRadius: "6px",
          fontSize: "16px",
          fontWeight: "bold",
          cursor: disabled ? "not-allowed" : "pointer",
          minWidth: "80px",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          transition: "all 0.3s ease"
        }}
        onMouseOver={(e) => {
          if (!disabled) {
            e.target.style.backgroundColor = "#45a049";
            e.target.style.transform = "translateY(-1px)";
            e.target.style.boxShadow = "0 4px 8px rgba(0,0,0,0.15)";
          }
        }}
        onMouseOut={(e) => {
          if (!disabled) {
            e.target.style.backgroundColor = "#4CAF50";
            e.target.style.transform = "translateY(0)";
            e.target.style.boxShadow = "0 2px 4px rgba(0,0,0,0.1)";
          }
        }}
      >
        ✅ {yesText}
      </button>
      
      <button
        onClick={onNo}
        disabled={disabled}
        style={{
          backgroundColor: disabled ? "#ccc" : "#f44336",
          color: "white",
          border: "none",
          padding: "12px 24px",
          borderRadius: "6px",
          fontSize: "16px",
          fontWeight: "bold",
          cursor: disabled ? "not-allowed" : "pointer",
          minWidth: "80px",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          transition: "all 0.3s ease"
        }}
        onMouseOver={(e) => {
          if (!disabled) {
            e.target.style.backgroundColor = "#d32f2f";
            e.target.style.transform = "translateY(-1px)";
            e.target.style.boxShadow = "0 4px 8px rgba(0,0,0,0.15)";
          }
        }}
        onMouseOut={(e) => {
          if (!disabled) {
            e.target.style.backgroundColor = "#f44336";
            e.target.style.transform = "translateY(0)";
            e.target.style.boxShadow = "0 2px 4px rgba(0,0,0,0.1)";
          }
        }}
      >
        ❌ {noText}
      </button>
    </div>
  );
}

export function ConfirmationButtons({ 
  onConfirm, 
  onCancel, 
  confirmText = "Confirm", 
  cancelText = "Cancel",
  confirmColor = "#4CAF50",
  cancelColor = "#6c757d",
  disabled = false 
}) {
  return (
    <div style={{ 
      display: "flex", 
      gap: "15px", 
      justifyContent: "center",
      margin: "15px 0"
    }}>
      <button
        onClick={onCancel}
        disabled={disabled}
        style={{
          backgroundColor: disabled ? "#ccc" : cancelColor,
          color: "white",
          border: "none",
          padding: "12px 24px",
          borderRadius: "6px",
          fontSize: "16px",
          cursor: disabled ? "not-allowed" : "pointer",
          minWidth: "100px",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          transition: "all 0.3s ease"
        }}
        onMouseOver={(e) => {
          if (!disabled) {
            e.target.style.opacity = "0.8";
            e.target.style.transform = "translateY(-1px)";
          }
        }}
        onMouseOut={(e) => {
          if (!disabled) {
            e.target.style.opacity = "1";
            e.target.style.transform = "translateY(0)";
          }
        }}
      >
        {cancelText}
      </button>
      
      <button
        onClick={onConfirm}
        disabled={disabled}
        style={{
          backgroundColor: disabled ? "#ccc" : confirmColor,
          color: "white",
          border: "none",
          padding: "12px 24px",
          borderRadius: "6px",
          fontSize: "16px",
          fontWeight: "bold",
          cursor: disabled ? "not-allowed" : "pointer",
          minWidth: "100px",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          transition: "all 0.3s ease"
        }}
        onMouseOver={(e) => {
          if (!disabled) {
            e.target.style.opacity = "0.8";
            e.target.style.transform = "translateY(-1px)";
          }
        }}
        onMouseOut={(e) => {
          if (!disabled) {
            e.target.style.opacity = "1";
            e.target.style.transform = "translateY(0)";
          }
        }}
      >
        {confirmText}
      </button>
    </div>
  );
}

export function ActionButtons({ actions, disabled = false }) {
  return (
    <div style={{ 
      display: "flex", 
      gap: "10px", 
      justifyContent: "center",
      margin: "15px 0",
      flexWrap: "wrap"
    }}>
      {actions.map((action, index) => (
        <button
          key={index}
          onClick={action.onClick}
          disabled={disabled}
          style={{
            backgroundColor: disabled ? "#ccc" : (action.color || "#2196F3"),
            color: "white",
            border: "none",
            padding: "10px 20px",
            borderRadius: "6px",
            fontSize: "14px",
            fontWeight: "bold",
            cursor: disabled ? "not-allowed" : "pointer",
            minWidth: "80px",
            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
            transition: "all 0.3s ease"
          }}
          onMouseOver={(e) => {
            if (!disabled) {
              e.target.style.opacity = "0.8";
              e.target.style.transform = "translateY(-1px)";
            }
          }}
          onMouseOut={(e) => {
            if (!disabled) {
              e.target.style.opacity = "1";
              e.target.style.transform = "translateY(0)";
            }
          }}
        >
          {action.icon && <span style={{ marginRight: "5px" }}>{action.icon}</span>}
          {action.text}
        </button>
      ))}
    </div>
  );
}

// Specialized button for file download confirmation
export function FileDownloadButtons({ onDownload, onSkip, disabled = false }) {
  return (
    <YesNoButtons
      onYes={onDownload}
      onNo={onSkip}
      yesText="Download File"
      noText="Skip"
      disabled={disabled}
    />
  );
}

// Specialized button for ticket closure
export function TicketCloseButtons({ onClose, onKeepOpen, onEscalate, disabled = false }) {
  const actions = [
    {
      text: "Close Ticket",
      onClick: onClose,
      color: "#4CAF50",
      icon: "✅"
    },
    {
      text: "Keep Open",
      onClick: onKeepOpen,
      color: "#2196F3",
      icon: "📝"
    }
  ];

  if (onEscalate) {
    actions.push({
      text: "Escalate",
      onClick: onEscalate,
      color: "#FF9800",
      icon: "⚠️"
    });
  }

  return <ActionButtons actions={actions} disabled={disabled} />;
}
