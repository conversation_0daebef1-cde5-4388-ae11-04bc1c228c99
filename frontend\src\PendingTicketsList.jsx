import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import "./App.css";

const BACKEND_URL = "http://localhost:8000";

export default function PendingTicketsList({ token }) {
  const navigate = useNavigate();
  const accessToken = token || localStorage.getItem("access");
  const [tickets, setTickets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [userName, setUserName] = useState("");

  // Redirect to auth if no token
  useEffect(() => {
    if (!accessToken) {
      navigate("/auth");
      return;
    }
  }, [accessToken, navigate]);

  // Fetch user info and pending tickets
  useEffect(() => {
    if (!accessToken) return;

    const fetchData = async () => {
      try {
        // Get user info
        const userResponse = await fetch(`${BACKEND_URL}/api/user_info/`, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
        });

        if (userResponse.ok) {
          const userData = await userResponse.json();
          setUserName(userData.name || userData.username || userData.email);
        }

        // Get pending tickets
        const ticketsResponse = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
        });

        if (ticketsResponse.ok) {
          const ticketsData = await ticketsResponse.json();
          setTickets(ticketsData.tickets || []);
        } else {
          setError("Failed to fetch pending tickets");
        }
      } catch (err) {
        setError("Network error: " + err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [accessToken]);

  const handleTicketSelect = (ticketNumber) => {
    navigate(`/legacy-chat?ticket=${ticketNumber}&mode=pending`);
  };

  const handleBackToActions = () => {
    navigate("/actions");
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div style={{ 
        display: "flex", 
        justifyContent: "center", 
        alignItems: "center", 
        height: "100vh",
        backgroundColor: "#f5f5f5"
      }}>
        <div style={{ textAlign: "center" }}>
          <div style={{ 
            fontSize: "18px", 
            color: "#666",
            marginBottom: "20px"
          }}>
            Loading pending tickets...
          </div>
          <div style={{
            width: "40px",
            height: "40px",
            border: "4px solid #f3f3f3",
            borderTop: "4px solid #3498db",
            borderRadius: "50%",
            animation: "spin 1s linear infinite",
            margin: "0 auto"
          }}></div>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: "100vh",
      backgroundColor: "#f5f5f5",
      padding: "20px"
    }}>
      <div style={{
        maxWidth: "1000px",
        margin: "0 auto",
        backgroundColor: "white",
        borderRadius: "12px",
        boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
        overflow: "hidden"
      }}>
        {/* Header */}
        <div style={{
          background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          color: "white",
          padding: "30px",
          textAlign: "center"
        }}>
          <h1 style={{ 
            margin: "0 0 10px 0", 
            fontSize: "2.5rem",
            fontWeight: "bold"
          }}>
            📋 Pending Tickets
          </h1>
          <p style={{ 
            margin: "0", 
            fontSize: "1.1rem",
            opacity: "0.9"
          }}>
            Welcome back, {userName}! Select a ticket to continue.
          </p>
        </div>

        {/* Content */}
        <div style={{ padding: "30px" }}>
          {error && (
            <div style={{
              backgroundColor: "#fee",
              color: "#c33",
              padding: "15px",
              borderRadius: "8px",
              marginBottom: "20px",
              border: "1px solid #fcc"
            }}>
              {error}
            </div>
          )}

          {tickets.length === 0 ? (
            <div style={{
              textAlign: "center",
              padding: "60px 20px",
              color: "#666"
            }}>
              <div style={{ fontSize: "4rem", marginBottom: "20px" }}>📝</div>
              <h3 style={{ marginBottom: "10px", color: "#333" }}>No Pending Tickets</h3>
              <p style={{ marginBottom: "30px" }}>You don't have any open tickets at the moment.</p>
              <button
                onClick={() => navigate("/new-ticket")}
                style={{
                  backgroundColor: "#4CAF50",
                  color: "white",
                  border: "none",
                  padding: "12px 24px",
                  borderRadius: "6px",
                  fontSize: "16px",
                  cursor: "pointer",
                  fontWeight: "bold"
                }}
              >
                Create New Ticket
              </button>
            </div>
          ) : (
            <>
              <h2 style={{ 
                marginBottom: "20px", 
                color: "#333",
                fontSize: "1.5rem"
              }}>
                Your Open Tickets ({tickets.length})
              </h2>
              
              {/* Scrollable tickets list */}
              <div style={{
                maxHeight: "500px",
                overflowY: "auto",
                border: "1px solid #e0e0e0",
                borderRadius: "8px",
                backgroundColor: "#fafafa"
              }}>
                {tickets.map((ticket, index) => (
                  <div
                    key={ticket.ticket_number}
                    onClick={() => handleTicketSelect(ticket.ticket_number)}
                    style={{
                      padding: "20px",
                      borderBottom: index < tickets.length - 1 ? "1px solid #e0e0e0" : "none",
                      cursor: "pointer",
                      backgroundColor: "white",
                      transition: "all 0.3s ease",
                      margin: "8px",
                      borderRadius: "6px",
                      boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
                    }}
                    onMouseOver={(e) => {
                      e.target.style.backgroundColor = "#f8f9fa";
                      e.target.style.transform = "translateY(-2px)";
                      e.target.style.boxShadow = "0 4px 8px rgba(0,0,0,0.15)";
                    }}
                    onMouseOut={(e) => {
                      e.target.style.backgroundColor = "white";
                      e.target.style.transform = "translateY(0)";
                      e.target.style.boxShadow = "0 2px 4px rgba(0,0,0,0.1)";
                    }}
                  >
                    <div style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "flex-start",
                      marginBottom: "10px"
                    }}>
                      <div style={{
                        fontWeight: "bold",
                        fontSize: "1.1rem",
                        color: "#2c3e50"
                      }}>
                        🎫 {ticket.ticket_number}
                      </div>
                      <div style={{
                        fontSize: "0.9rem",
                        color: "#666",
                        backgroundColor: "#e8f5e8",
                        padding: "4px 8px",
                        borderRadius: "12px",
                        fontWeight: "bold"
                      }}>
                        {ticket.status.toUpperCase()}
                      </div>
                    </div>
                    
                    <div style={{
                      fontSize: "1rem",
                      fontWeight: "600",
                      color: "#34495e",
                      marginBottom: "8px"
                    }}>
                      {ticket.title || ticket.short_title || "No title"}
                    </div>
                    
                    <div style={{
                      fontSize: "0.9rem",
                      color: "#666",
                      marginBottom: "10px",
                      lineHeight: "1.4"
                    }}>
                      {ticket.problem_description ? 
                        (ticket.problem_description.length > 150 ? 
                          ticket.problem_description.substring(0, 150) + "..." : 
                          ticket.problem_description
                        ) : 
                        "No description available"
                      }
                    </div>
                    
                    <div style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      fontSize: "0.8rem",
                      color: "#888"
                    }}>
                      <span>
                        📦 {ticket.product_name} - {ticket.model}
                      </span>
                      <span>
                        🕒 {formatDate(ticket.last_activity)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}

          {/* Back button */}
          <div style={{ 
            textAlign: "center", 
            marginTop: "30px",
            paddingTop: "20px",
            borderTop: "1px solid #e0e0e0"
          }}>
            <button
              onClick={handleBackToActions}
              style={{
                backgroundColor: "#6c757d",
                color: "white",
                border: "none",
                padding: "12px 24px",
                borderRadius: "6px",
                fontSize: "16px",
                cursor: "pointer",
                fontWeight: "bold",
                marginRight: "15px"
              }}
              onMouseOver={(e) => e.target.style.backgroundColor = "#5a6268"}
              onMouseOut={(e) => e.target.style.backgroundColor = "#6c757d"}
            >
              ← Back to Actions
            </button>
            
            <button
              onClick={() => navigate("/new-ticket")}
              style={{
                backgroundColor: "#28a745",
                color: "white",
                border: "none",
                padding: "12px 24px",
                borderRadius: "6px",
                fontSize: "16px",
                cursor: "pointer",
                fontWeight: "bold"
              }}
              onMouseOver={(e) => e.target.style.backgroundColor = "#218838"}
              onMouseOut={(e) => e.target.style.backgroundColor = "#28a745"}
            >
              + Create New Ticket
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
