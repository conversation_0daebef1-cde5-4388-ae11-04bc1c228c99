import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import "./App.css";

const BACKEND_URL = "http://localhost:8000";

export default function NewTicketForm({ token }) {
  const navigate = useNavigate();
  const accessToken = token || localStorage.getItem("access");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const [formData, setFormData] = useState({
    shortTitle: "",
    productType: "Camera",
    productName: "",
    model: "",
    serialNo: "",
    purchasedFrom: "",
    yearOfPurchase: "",
    operatingSystem: "",
    problemDescription: "",
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    if (error) setError("");
  };

  const validateForm = () => {
    const requiredFields = [
      'shortTitle', 'productName', 'model', 'serialNo', 'purchasedFrom', 'yearOfPurchase', 'operatingSystem', 'problemDescription'
    ];

    for (let field of requiredFields) {
      if (!formData[field] || formData[field].trim() === "") {
        return `Please fill in the ${field.replace(/([A-Z])/g, ' $1').toLowerCase()} field.`;
      }
    }

    return null;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setLoading(true);
    setError("");

    try {
      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          short_title: formData.shortTitle,
          product_type: formData.productType,
          product_name: formData.productName,
          model: formData.model,
          serial_no: formData.serialNo,
          purchased_from: formData.purchasedFrom,
          year_of_purchase: formData.yearOfPurchase,
          operating_system: formData.operatingSystem,
          problem_description: formData.problemDescription,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Redirect to Home.jsx chatbot with the new ticket
        navigate(`/legacy-chat?ticket=${data.ticket_number}&mode=new`);
      } else {
        setError(data.message || "Failed to create ticket. Please try again.");
      }
    } catch (err) {
      console.error("Error creating ticket:", err);
      setError("Network error. Please check your connection and try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate("/actions");
  };

  return (
    <div style={{ 
      padding: "40px", 
      maxWidth: "600px", 
      margin: "0 auto", 
      fontFamily: "Arial, sans-serif"
    }}>
      <h1 style={{ 
        color: "#333", 
        marginBottom: "20px",
        textAlign: "center"
      }}>
        Create New Support Ticket
      </h1>
      
      <p style={{ 
        fontSize: "1.1rem", 
        color: "#666", 
        marginBottom: "30px",
        textAlign: "center"
      }}>
        Please provide your product details below. After submitting, you'll be able to describe your issue in the chat.
      </p>

      {error && (
        <div style={{
          backgroundColor: "#ffebee",
          color: "#c62828",
          padding: "12px",
          borderRadius: "4px",
          marginBottom: "20px",
          border: "1px solid #ef5350"
        }}>
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} style={{ display: "flex", flexDirection: "column", gap: "20px" }}>

        {/* Short Title */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Short Title *
          </label>
          <input
            type="text"
            name="shortTitle"
            value={formData.shortTitle}
            onChange={handleInputChange}
            placeholder="Brief description of the issue"
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          />
        </div>

        {/* Product Type */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Product Type *
          </label>
          <select
            name="productType"
            value={formData.productType}
            onChange={handleInputChange}
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          >
            <option value="Camera">Camera</option>
            <option value="Frame Grabber">Frame Grabber</option>
            <option value="Accessories">Accessories</option>
            <option value="Software">Software</option>
          </select>
        </div>

        {/* Product Name */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Product Name *
          </label>
          <input
            type="text"
            name="productName"
            value={formData.productName}
            onChange={handleInputChange}
            placeholder="e.g., Genie Nano, Falcon4-CLHS"
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          />
        </div>

        {/* Model */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Model *
          </label>
          <input
            type="text"
            name="model"
            value={formData.model}
            onChange={handleInputChange}
            placeholder="e.g., C2590, M1920"
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          />
        </div>

        {/* Serial Number */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Serial Number *
          </label>
          <input
            type="text"
            name="serialNo"
            value={formData.serialNo}
            onChange={handleInputChange}
            placeholder="e.g., SN123456789"
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          />
        </div>

        {/* Purchased From */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Purchased From *
          </label>
          <input
            type="text"
            name="purchasedFrom"
            value={formData.purchasedFrom}
            onChange={handleInputChange}
            placeholder="e.g., Teledyne DALSA, Authorized Dealer"
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          />
        </div>

        {/* Year of Purchase */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Year of Purchase *
          </label>
          <input
            type="text"
            name="yearOfPurchase"
            value={formData.yearOfPurchase}
            onChange={handleInputChange}
            placeholder="e.g., 2023"
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          />
        </div>

        {/* Operating System */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Operating System *
          </label>
          <input
            type="text"
            name="operatingSystem"
            value={formData.operatingSystem}
            onChange={handleInputChange}
            placeholder="e.g., Windows 11, Ubuntu 20.04"
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          />
        </div>

        {/* Problem Description */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Problem Description *
          </label>
          <textarea
            name="problemDescription"
            value={formData.problemDescription}
            onChange={handleInputChange}
            placeholder="Describe the issue you're experiencing in detail..."
            required
            rows={4}
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px",
              resize: "vertical"
            }}
          />
        </div>

        {/* Buttons */}
        <div style={{ display: "flex", gap: "15px", marginTop: "20px" }}>
          <button
            type="button"
            onClick={handleCancel}
            disabled={loading}
            style={{
              flex: 1,
              padding: "15px",
              backgroundColor: "#6c757d",
              color: "white",
              border: "none",
              borderRadius: "4px",
              fontSize: "16px",
              cursor: loading ? "not-allowed" : "pointer"
            }}
          >
            Cancel
          </button>
          
          <button
            type="submit"
            disabled={loading}
            style={{
              flex: 2,
              padding: "15px",
              backgroundColor: loading ? "#ccc" : "#4CAF50",
              color: "white",
              border: "none",
              borderRadius: "4px",
              fontSize: "16px",
              cursor: loading ? "not-allowed" : "pointer"
            }}
          >
            {loading ? "Creating Ticket..." : "Create Ticket & Start Chat"}
          </button>
        </div>
      </form>
    </div>
  );
}
