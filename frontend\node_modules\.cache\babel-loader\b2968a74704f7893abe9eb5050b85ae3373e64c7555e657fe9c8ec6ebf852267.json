{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AI-Agent-Chatbot-main\\\\frontend\\\\src\\\\NewTicketForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport \"./App.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BACKEND_URL = \"http://localhost:8000\";\nexport default function NewTicketForm({\n  token\n}) {\n  _s();\n  const navigate = useNavigate();\n  const accessToken = token || localStorage.getItem(\"access\");\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n  const [formData, setFormData] = useState({\n    shortTitle: \"\",\n    productName: \"\",\n    model: \"\",\n    serialNo: \"\",\n    poNumber: \"\",\n    problemDescription: \"\"\n  });\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    if (error) setError(\"\");\n  };\n  const validateForm = () => {\n    const requiredFields = ['shortTitle', 'productName', 'model', 'serialNo', 'poNumber', 'problemDescription'];\n    for (let field of requiredFields) {\n      if (!formData[field] || formData[field].trim() === \"\") {\n        return `Please fill in the ${field.replace(/([A-Z])/g, ' $1').toLowerCase()} field.`;\n      }\n    }\n    return null;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const validationError = validateForm();\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n    setLoading(true);\n    setError(\"\");\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        },\n        body: JSON.stringify({\n          short_title: formData.shortTitle,\n          product_name: formData.productName,\n          model: formData.model,\n          serial_no: formData.serialNo,\n          po_number: formData.poNumber,\n          problem_description: formData.problemDescription\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        // Redirect to Home.jsx chatbot with the new ticket\n        navigate(`/?ticket=${data.ticket_number}&mode=new`);\n      } else {\n        setError(data.message || \"Failed to create ticket. Please try again.\");\n      }\n    } catch (err) {\n      console.error(\"Error creating ticket:\", err);\n      setError(\"Network error. Please check your connection and try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCancel = () => {\n    navigate(\"/actions\");\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: \"40px\",\n      maxWidth: \"600px\",\n      margin: \"0 auto\",\n      fontFamily: \"Arial, sans-serif\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      style: {\n        color: \"#333\",\n        marginBottom: \"20px\",\n        textAlign: \"center\"\n      },\n      children: \"Create New Support Ticket\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        fontSize: \"1.1rem\",\n        color: \"#666\",\n        marginBottom: \"30px\",\n        textAlign: \"center\"\n      },\n      children: \"Please provide your product details below. After submitting, you'll be able to describe your issue in the chat.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: \"#ffebee\",\n        color: \"#c62828\",\n        padding: \"12px\",\n        borderRadius: \"4px\",\n        marginBottom: \"20px\",\n        border: \"1px solid #ef5350\"\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      style: {\n        display: \"flex\",\n        flexDirection: \"column\",\n        gap: \"20px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Product Type *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          name: \"productType\",\n          value: formData.productType,\n          onChange: handleInputChange,\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Select Product Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), productTypeOptions.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: option,\n            children: option\n          }, option, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Purchased From *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"purchasedFrom\",\n          value: formData.purchasedFrom,\n          onChange: handleInputChange,\n          placeholder: \"e.g., Online Solutions, Amazon, Direct from manufacturer\",\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Year of Purchase *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          name: \"yearOfPurchase\",\n          value: formData.yearOfPurchase,\n          onChange: handleInputChange,\n          placeholder: \"e.g., 2023\",\n          min: \"1990\",\n          max: new Date().getFullYear(),\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Product Name *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"productName\",\n          value: formData.productName,\n          onChange: handleInputChange,\n          placeholder: \"e.g., Genie Nano, Falcon4-CLHS\",\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Model *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"model\",\n          value: formData.model,\n          onChange: handleInputChange,\n          placeholder: \"e.g., C2590, M1920\",\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Serial Number *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"serialNo\",\n          value: formData.serialNo,\n          onChange: handleInputChange,\n          placeholder: \"e.g., SN123456789\",\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginBottom: \"8px\",\n            fontWeight: \"bold\",\n            color: \"#333\"\n          },\n          children: \"Operating System *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"operatingSystem\",\n          value: formData.operatingSystem,\n          onChange: handleInputChange,\n          placeholder: \"e.g., Windows 11, Ubuntu 20.04, macOS Monterey\",\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          gap: \"15px\",\n          marginTop: \"20px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handleCancel,\n          disabled: loading,\n          style: {\n            flex: 1,\n            padding: \"15px\",\n            backgroundColor: \"#6c757d\",\n            color: \"white\",\n            border: \"none\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\",\n            cursor: loading ? \"not-allowed\" : \"pointer\"\n          },\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: loading,\n          style: {\n            flex: 2,\n            padding: \"15px\",\n            backgroundColor: loading ? \"#ccc\" : \"#4CAF50\",\n            color: \"white\",\n            border: \"none\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\",\n            cursor: loading ? \"not-allowed\" : \"pointer\"\n          },\n          children: loading ? \"Creating Ticket...\" : \"Create Ticket & Start Chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n}\n_s(NewTicketForm, \"Ws5/Wvk/Yd2rmEal3Mg9RXu2F8c=\", false, function () {\n  return [useNavigate];\n});\n_c = NewTicketForm;\nvar _c;\n$RefreshReg$(_c, \"NewTicketForm\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "jsxDEV", "_jsxDEV", "BACKEND_URL", "NewTicketForm", "token", "_s", "navigate", "accessToken", "localStorage", "getItem", "loading", "setLoading", "error", "setError", "formData", "setFormData", "shortTitle", "productName", "model", "serialNo", "poNumber", "problemDescription", "handleInputChange", "e", "name", "value", "target", "prev", "validateForm", "requiredFields", "field", "trim", "replace", "toLowerCase", "handleSubmit", "preventDefault", "validationError", "response", "fetch", "method", "headers", "Authorization", "body", "JSON", "stringify", "short_title", "product_name", "serial_no", "po_number", "problem_description", "data", "json", "ok", "ticket_number", "message", "err", "console", "handleCancel", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "fontFamily", "children", "color", "marginBottom", "textAlign", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "backgroundColor", "borderRadius", "border", "onSubmit", "display", "flexDirection", "gap", "fontWeight", "productType", "onChange", "required", "width", "productTypeOptions", "map", "option", "type", "purchasedFrom", "placeholder", "yearOfPurchase", "min", "max", "Date", "getFullYear", "operatingSystem", "marginTop", "onClick", "disabled", "flex", "cursor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/AI-Agent-Chatbot-main/frontend/src/NewTicketForm.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport \"./App.css\";\n\nconst BACKEND_URL = \"http://localhost:8000\";\n\nexport default function NewTicketForm({ token }) {\n  const navigate = useNavigate();\n  const accessToken = token || localStorage.getItem(\"access\");\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  const [formData, setFormData] = useState({\n    shortTitle: \"\",\n    productName: \"\",\n    model: \"\",\n    serialNo: \"\",\n    poNumber: \"\",\n    problemDescription: \"\",\n  });\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    if (error) setError(\"\");\n  };\n\n  const validateForm = () => {\n    const requiredFields = [\n      'shortTitle', 'productName', 'model', 'serialNo', 'poNumber', 'problemDescription'\n    ];\n\n    for (let field of requiredFields) {\n      if (!formData[field] || formData[field].trim() === \"\") {\n        return `Please fill in the ${field.replace(/([A-Z])/g, ' $1').toLowerCase()} field.`;\n      }\n    }\n\n    return null;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const validationError = validateForm();\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    setLoading(true);\n    setError(\"\");\n\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`,\n        },\n        body: JSON.stringify({\n          short_title: formData.shortTitle,\n          product_name: formData.productName,\n          model: formData.model,\n          serial_no: formData.serialNo,\n          po_number: formData.poNumber,\n          problem_description: formData.problemDescription,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        // Redirect to Home.jsx chatbot with the new ticket\n        navigate(`/?ticket=${data.ticket_number}&mode=new`);\n      } else {\n        setError(data.message || \"Failed to create ticket. Please try again.\");\n      }\n    } catch (err) {\n      console.error(\"Error creating ticket:\", err);\n      setError(\"Network error. Please check your connection and try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCancel = () => {\n    navigate(\"/actions\");\n  };\n\n  return (\n    <div style={{ \n      padding: \"40px\", \n      maxWidth: \"600px\", \n      margin: \"0 auto\", \n      fontFamily: \"Arial, sans-serif\"\n    }}>\n      <h1 style={{ \n        color: \"#333\", \n        marginBottom: \"20px\",\n        textAlign: \"center\"\n      }}>\n        Create New Support Ticket\n      </h1>\n      \n      <p style={{ \n        fontSize: \"1.1rem\", \n        color: \"#666\", \n        marginBottom: \"30px\",\n        textAlign: \"center\"\n      }}>\n        Please provide your product details below. After submitting, you'll be able to describe your issue in the chat.\n      </p>\n\n      {error && (\n        <div style={{\n          backgroundColor: \"#ffebee\",\n          color: \"#c62828\",\n          padding: \"12px\",\n          borderRadius: \"4px\",\n          marginBottom: \"20px\",\n          border: \"1px solid #ef5350\"\n        }}>\n          {error}\n        </div>\n      )}\n\n      <form onSubmit={handleSubmit} style={{ display: \"flex\", flexDirection: \"column\", gap: \"20px\" }}>\n        \n        {/* Product Type */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Product Type *\n          </label>\n          <select\n            name=\"productType\"\n            value={formData.productType}\n            onChange={handleInputChange}\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          >\n            <option value=\"\">Select Product Type</option>\n            {productTypeOptions.map(option => (\n              <option key={option} value={option}>{option}</option>\n            ))}\n          </select>\n        </div>\n\n        {/* Purchased From */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Purchased From *\n          </label>\n          <input\n            type=\"text\"\n            name=\"purchasedFrom\"\n            value={formData.purchasedFrom}\n            onChange={handleInputChange}\n            placeholder=\"e.g., Online Solutions, Amazon, Direct from manufacturer\"\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n\n        {/* Year of Purchase */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Year of Purchase *\n          </label>\n          <input\n            type=\"number\"\n            name=\"yearOfPurchase\"\n            value={formData.yearOfPurchase}\n            onChange={handleInputChange}\n            placeholder=\"e.g., 2023\"\n            min=\"1990\"\n            max={new Date().getFullYear()}\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n\n        {/* Product Name */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Product Name *\n          </label>\n          <input\n            type=\"text\"\n            name=\"productName\"\n            value={formData.productName}\n            onChange={handleInputChange}\n            placeholder=\"e.g., Genie Nano, Falcon4-CLHS\"\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n\n        {/* Model */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Model *\n          </label>\n          <input\n            type=\"text\"\n            name=\"model\"\n            value={formData.model}\n            onChange={handleInputChange}\n            placeholder=\"e.g., C2590, M1920\"\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n\n        {/* Serial Number */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Serial Number *\n          </label>\n          <input\n            type=\"text\"\n            name=\"serialNo\"\n            value={formData.serialNo}\n            onChange={handleInputChange}\n            placeholder=\"e.g., SN123456789\"\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n\n        {/* Operating System */}\n        <div>\n          <label style={{ display: \"block\", marginBottom: \"8px\", fontWeight: \"bold\", color: \"#333\" }}>\n            Operating System *\n          </label>\n          <input\n            type=\"text\"\n            name=\"operatingSystem\"\n            value={formData.operatingSystem}\n            onChange={handleInputChange}\n            placeholder=\"e.g., Windows 11, Ubuntu 20.04, macOS Monterey\"\n            required\n            style={{\n              width: \"100%\",\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n\n        {/* Buttons */}\n        <div style={{ display: \"flex\", gap: \"15px\", marginTop: \"20px\" }}>\n          <button\n            type=\"button\"\n            onClick={handleCancel}\n            disabled={loading}\n            style={{\n              flex: 1,\n              padding: \"15px\",\n              backgroundColor: \"#6c757d\",\n              color: \"white\",\n              border: \"none\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\",\n              cursor: loading ? \"not-allowed\" : \"pointer\"\n            }}\n          >\n            Cancel\n          </button>\n          \n          <button\n            type=\"submit\"\n            disabled={loading}\n            style={{\n              flex: 2,\n              padding: \"15px\",\n              backgroundColor: loading ? \"#ccc\" : \"#4CAF50\",\n              color: \"white\",\n              border: \"none\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\",\n              cursor: loading ? \"not-allowed\" : \"pointer\"\n            }}\n          >\n            {loading ? \"Creating Ticket...\" : \"Create Ticket & Start Chat\"}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,MAAMC,WAAW,GAAG,uBAAuB;AAE3C,eAAe,SAASC,aAAaA,CAAC;EAAEC;AAAM,CAAC,EAAE;EAAAC,EAAA;EAC/C,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAMQ,WAAW,GAAGH,KAAK,IAAII,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;EAC3D,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCX,WAAW,CAACY,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;IACH,IAAIb,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC;EAED,MAAMe,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,cAAc,GAAG,CACrB,YAAY,EAAE,aAAa,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,oBAAoB,CACnF;IAED,KAAK,IAAIC,KAAK,IAAID,cAAc,EAAE;MAChC,IAAI,CAACf,QAAQ,CAACgB,KAAK,CAAC,IAAIhB,QAAQ,CAACgB,KAAK,CAAC,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACrD,OAAO,sBAAsBD,KAAK,CAACE,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACC,WAAW,CAAC,CAAC,SAAS;MACtF;IACF;IAEA,OAAO,IAAI;EACb,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,MAAMC,eAAe,GAAGR,YAAY,CAAC,CAAC;IACtC,IAAIQ,eAAe,EAAE;MACnBvB,QAAQ,CAACuB,eAAe,CAAC;MACzB;IACF;IAEAzB,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMwB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGpC,WAAW,qBAAqB,EAAE;QAChEqC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUlC,WAAW;QACtC,CAAC;QACDmC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,WAAW,EAAE/B,QAAQ,CAACE,UAAU;UAChC8B,YAAY,EAAEhC,QAAQ,CAACG,WAAW;UAClCC,KAAK,EAAEJ,QAAQ,CAACI,KAAK;UACrB6B,SAAS,EAAEjC,QAAQ,CAACK,QAAQ;UAC5B6B,SAAS,EAAElC,QAAQ,CAACM,QAAQ;UAC5B6B,mBAAmB,EAAEnC,QAAQ,CAACO;QAChC,CAAC;MACH,CAAC,CAAC;MAEF,MAAM6B,IAAI,GAAG,MAAMb,QAAQ,CAACc,IAAI,CAAC,CAAC;MAElC,IAAId,QAAQ,CAACe,EAAE,EAAE;QACf;QACA9C,QAAQ,CAAC,YAAY4C,IAAI,CAACG,aAAa,WAAW,CAAC;MACrD,CAAC,MAAM;QACLxC,QAAQ,CAACqC,IAAI,CAACI,OAAO,IAAI,4CAA4C,CAAC;MACxE;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAAC5C,KAAK,CAAC,wBAAwB,EAAE2C,GAAG,CAAC;MAC5C1C,QAAQ,CAAC,4DAA4D,CAAC;IACxE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8C,YAAY,GAAGA,CAAA,KAAM;IACzBnD,QAAQ,CAAC,UAAU,CAAC;EACtB,CAAC;EAED,oBACEL,OAAA;IAAKyD,KAAK,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,gBACA9D,OAAA;MAAIyD,KAAK,EAAE;QACTM,KAAK,EAAE,MAAM;QACbC,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE;MACb,CAAE;MAAAH,QAAA,EAAC;IAEH;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAELrE,OAAA;MAAGyD,KAAK,EAAE;QACRa,QAAQ,EAAE,QAAQ;QAClBP,KAAK,EAAE,MAAM;QACbC,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE;MACb,CAAE;MAAAH,QAAA,EAAC;IAEH;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,EAEH1D,KAAK,iBACJX,OAAA;MAAKyD,KAAK,EAAE;QACVc,eAAe,EAAE,SAAS;QAC1BR,KAAK,EAAE,SAAS;QAChBL,OAAO,EAAE,MAAM;QACfc,YAAY,EAAE,KAAK;QACnBR,YAAY,EAAE,MAAM;QACpBS,MAAM,EAAE;MACV,CAAE;MAAAX,QAAA,EACCnD;IAAK;MAAAuD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDrE,OAAA;MAAM0E,QAAQ,EAAEzC,YAAa;MAACwB,KAAK,EAAE;QAAEkB,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAf,QAAA,gBAG7F9D,OAAA;QAAA8D,QAAA,gBACE9D,OAAA;UAAOyD,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRrE,OAAA;UACEuB,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAEX,QAAQ,CAACkE,WAAY;UAC5BC,QAAQ,EAAE3D,iBAAkB;UAC5B4D,QAAQ;UACRxB,KAAK,EAAE;YACLyB,KAAK,EAAE,MAAM;YACbxB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ,CAAE;UAAAR,QAAA,gBAEF9D,OAAA;YAAQwB,KAAK,EAAC,EAAE;YAAAsC,QAAA,EAAC;UAAmB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAC5Cc,kBAAkB,CAACC,GAAG,CAACC,MAAM,iBAC5BrF,OAAA;YAAqBwB,KAAK,EAAE6D,MAAO;YAAAvB,QAAA,EAAEuB;UAAM,GAA9BA,MAAM;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiC,CACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNrE,OAAA;QAAA8D,QAAA,gBACE9D,OAAA;UAAOyD,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRrE,OAAA;UACEsF,IAAI,EAAC,MAAM;UACX/D,IAAI,EAAC,eAAe;UACpBC,KAAK,EAAEX,QAAQ,CAAC0E,aAAc;UAC9BP,QAAQ,EAAE3D,iBAAkB;UAC5BmE,WAAW,EAAC,0DAA0D;UACtEP,QAAQ;UACRxB,KAAK,EAAE;YACLyB,KAAK,EAAE,MAAM;YACbxB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNrE,OAAA;QAAA8D,QAAA,gBACE9D,OAAA;UAAOyD,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRrE,OAAA;UACEsF,IAAI,EAAC,QAAQ;UACb/D,IAAI,EAAC,gBAAgB;UACrBC,KAAK,EAAEX,QAAQ,CAAC4E,cAAe;UAC/BT,QAAQ,EAAE3D,iBAAkB;UAC5BmE,WAAW,EAAC,YAAY;UACxBE,GAAG,EAAC,MAAM;UACVC,GAAG,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAE;UAC9BZ,QAAQ;UACRxB,KAAK,EAAE;YACLyB,KAAK,EAAE,MAAM;YACbxB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNrE,OAAA;QAAA8D,QAAA,gBACE9D,OAAA;UAAOyD,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRrE,OAAA;UACEsF,IAAI,EAAC,MAAM;UACX/D,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAEX,QAAQ,CAACG,WAAY;UAC5BgE,QAAQ,EAAE3D,iBAAkB;UAC5BmE,WAAW,EAAC,gCAAgC;UAC5CP,QAAQ;UACRxB,KAAK,EAAE;YACLyB,KAAK,EAAE,MAAM;YACbxB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNrE,OAAA;QAAA8D,QAAA,gBACE9D,OAAA;UAAOyD,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRrE,OAAA;UACEsF,IAAI,EAAC,MAAM;UACX/D,IAAI,EAAC,OAAO;UACZC,KAAK,EAAEX,QAAQ,CAACI,KAAM;UACtB+D,QAAQ,EAAE3D,iBAAkB;UAC5BmE,WAAW,EAAC,oBAAoB;UAChCP,QAAQ;UACRxB,KAAK,EAAE;YACLyB,KAAK,EAAE,MAAM;YACbxB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNrE,OAAA;QAAA8D,QAAA,gBACE9D,OAAA;UAAOyD,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRrE,OAAA;UACEsF,IAAI,EAAC,MAAM;UACX/D,IAAI,EAAC,UAAU;UACfC,KAAK,EAAEX,QAAQ,CAACK,QAAS;UACzB8D,QAAQ,EAAE3D,iBAAkB;UAC5BmE,WAAW,EAAC,mBAAmB;UAC/BP,QAAQ;UACRxB,KAAK,EAAE;YACLyB,KAAK,EAAE,MAAM;YACbxB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNrE,OAAA;QAAA8D,QAAA,gBACE9D,OAAA;UAAOyD,KAAK,EAAE;YAAEkB,OAAO,EAAE,OAAO;YAAEX,YAAY,EAAE,KAAK;YAAEc,UAAU,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAE5F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRrE,OAAA;UACEsF,IAAI,EAAC,MAAM;UACX/D,IAAI,EAAC,iBAAiB;UACtBC,KAAK,EAAEX,QAAQ,CAACiF,eAAgB;UAChCd,QAAQ,EAAE3D,iBAAkB;UAC5BmE,WAAW,EAAC,gDAAgD;UAC5DP,QAAQ;UACRxB,KAAK,EAAE;YACLyB,KAAK,EAAE,MAAM;YACbxB,OAAO,EAAE,MAAM;YACfe,MAAM,EAAE,gBAAgB;YACxBD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE;UACZ;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNrE,OAAA;QAAKyD,KAAK,EAAE;UAAEkB,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE,MAAM;UAAEkB,SAAS,EAAE;QAAO,CAAE;QAAAjC,QAAA,gBAC9D9D,OAAA;UACEsF,IAAI,EAAC,QAAQ;UACbU,OAAO,EAAExC,YAAa;UACtByC,QAAQ,EAAExF,OAAQ;UAClBgD,KAAK,EAAE;YACLyC,IAAI,EAAE,CAAC;YACPxC,OAAO,EAAE,MAAM;YACfa,eAAe,EAAE,SAAS;YAC1BR,KAAK,EAAE,OAAO;YACdU,MAAM,EAAE,MAAM;YACdD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE,MAAM;YAChB6B,MAAM,EAAE1F,OAAO,GAAG,aAAa,GAAG;UACpC,CAAE;UAAAqD,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETrE,OAAA;UACEsF,IAAI,EAAC,QAAQ;UACbW,QAAQ,EAAExF,OAAQ;UAClBgD,KAAK,EAAE;YACLyC,IAAI,EAAE,CAAC;YACPxC,OAAO,EAAE,MAAM;YACfa,eAAe,EAAE9D,OAAO,GAAG,MAAM,GAAG,SAAS;YAC7CsD,KAAK,EAAE,OAAO;YACdU,MAAM,EAAE,MAAM;YACdD,YAAY,EAAE,KAAK;YACnBF,QAAQ,EAAE,MAAM;YAChB6B,MAAM,EAAE1F,OAAO,GAAG,aAAa,GAAG;UACpC,CAAE;UAAAqD,QAAA,EAEDrD,OAAO,GAAG,oBAAoB,GAAG;QAA4B;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACjE,EAAA,CArUuBF,aAAa;EAAA,QAClBJ,WAAW;AAAA;AAAAsG,EAAA,GADNlG,aAAa;AAAA,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}